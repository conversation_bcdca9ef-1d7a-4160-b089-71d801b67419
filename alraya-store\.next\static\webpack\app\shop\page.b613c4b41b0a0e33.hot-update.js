"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./app/shop/page.tsx":
/*!***************************!*\
  !*** ./app/shop/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShopPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/utils/pricingUtils */ \"(app-pages-browser)/./lib/utils/pricingUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Enhanced categories with icons\nconst categories = [\n    {\n        id: \"all\",\n        label: \"جميع المنتجات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 35,\n            columnNumber: 46\n        }, undefined)\n    },\n    {\n        id: \"ألعاب الموبايل\",\n        label: \"ألعاب الموبايل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"منصات التواصل\",\n        label: \"منصات التواصل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 56\n        }, undefined)\n    },\n    {\n        id: \"بطاقات الألعاب\",\n        label: \"بطاقات الألعاب\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"digital\",\n        label: \"🎮 منتجات رقمية\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 52\n        }, undefined)\n    }\n];\nfunction ShopPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use global currency context for price conversion\n    const { formatPrice } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter)();\n    // Get products from global store (loaded on app startup)\n    const products = useProducts();\n    const isLoading = useAppLoading();\n    // Navigation handler\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n        } else if (tab === \"support\") {\n            router.push(\"/contact\");\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    // Product click handler\n    const handleProductClick = (productId)=>{\n        router.push(\"/shop/\".concat(productId));\n    };\n    // Filter products based on search and category\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase());\n        // Handle category filtering including digital products\n        let matchesCategory = false;\n        if (selectedCategory === \"all\") {\n            matchesCategory = true;\n        } else if (selectedCategory === \"digital\") {\n            matchesCategory = product.productType === \"digital\";\n        } else {\n            matchesCategory = product.category === selectedCategory;\n        }\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"متجر الألعاب\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"اكتشف أفضل العروض لشحن ألعابك المفضلة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            placeholder: \"ابحث عن لعبتك المفضلة...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pr-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:border-yellow-400 focus:outline-none appearance-none\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category.id,\n                                                className: \"bg-slate-800\",\n                                                children: category.label\n                                            }, category.id, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 146,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-8\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"flex items-center gap-2 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-0\" : \"border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400\"),\n                                children: [\n                                    category.icon,\n                                    category.label\n                                ]\n                            }, category.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⏳\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-2\",\n                                children: \"جاري تحميل المنتجات...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400\",\n                                children: \"يرجى الانتظار\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 md:gap-6\",\n                                children: filteredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        onClick: ()=>handleProductClick(product.id),\n                                        className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 hover:border-yellow-400/30 hover:shadow-2xl hover:shadow-yellow-400/10 transition-all duration-500 cursor-pointer group overflow-hidden transform hover:scale-105 hover:-translate-y-2\",\n                                        style: {\n                                            animationDelay: \"\".concat(index * 100, \"ms\")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square overflow-hidden\",\n                                                    children: [\n                                                        product.previewImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.previewImage,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center relative overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 group-hover:from-yellow-400/10 group-hover:to-orange-500/10 transition-all duration-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-slate-400 group-hover:text-yellow-400 transition-colors duration-300 relative z-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"أكواد فورية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 213,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_13__.isProductPopular)(product) && product.productType !== \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 animate-pulse\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg\",\n                                                                children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_13__.isProductPopular)(product) && product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"أكواد فورية\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg block\",\n                                                                    children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 237,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs font-medium border-0 \".concat(product.processingType === \"instant\" ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400'),\n                                                                children: product.processingType === \"instant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"فوري\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"يدوي\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 w-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-white\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-yellow-400 fill-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 272,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium\",\n                                                                                    children: \"4.8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 273,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-slate-300\",\n                                                                                    children: \"(جديد)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 274,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs font-bold text-yellow-400\",\n                                                                            children: [\n                                                                                \"من \",\n                                                                                formatPrice((0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_13__.getProductStartingPrice)(product))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 276,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-bold text-center group-hover:text-yellow-400 transition-colors duration-300 text-sm leading-tight\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-xs text-center mt-2 line-clamp-2 group-hover:text-slate-300 transition-colors duration-300\",\n                                                            children: product.description || \"منتج رائع من متجر الراية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center gap-2 mt-3 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 296,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: product.estimatedTime\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 295,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 299,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                product.packages.length,\n                                                                                \" حزمة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 302,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 294,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"لا توجد منتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400\",\n                                        children: products.length === 0 ? \"لم يتم إنشاء أي منتجات بعد. قم بإنشاء منتجات من لوحة الإدارة.\" : \"لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 329,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopPage, \"AB18iAu5Scj2vugrzLlmXC2iJNY=\", true, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter\n    ];\n});\n_c = ShopPage;\nvar _c;\n$RefreshReg$(_c, \"ShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/shop/page.tsx\n"));

/***/ })

});