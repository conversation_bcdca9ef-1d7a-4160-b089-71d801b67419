"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./components/pages/WalletPage.tsx":
/*!*****************************************!*\
  !*** ./components/pages/WalletPage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletPage: () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/wallet/WalletBalance */ \"(app-pages-browser)/./components/wallet/WalletBalance.tsx\");\n/* harmony import */ var _components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletTransactions */ \"(app-pages-browser)/./components/wallet/WalletTransactions.tsx\");\n/* harmony import */ var _barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/digitalContentUtils */ \"(app-pages-browser)/./lib/utils/digitalContentUtils.ts\");\n/* harmony import */ var _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/hooks/useChat */ \"(app-pages-browser)/./lib/hooks/useChat.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Convert orders to transactions for unified display in the transactions component\r\n */ async function loadOrdersAsTransactions(userEmail) {\n    try {\n        // Import order service dynamically to avoid circular dependencies\n        const { getOrdersByUser } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_services_orderService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/orderService */ \"(app-pages-browser)/./lib/services/orderService.ts\"));\n        const orders = await getOrdersByUser(userEmail);\n        return orders.map((order)=>({\n                id: \"order_\".concat(order.id),\n                userId: userEmail,\n                walletId: \"wallet_\".concat(userEmail),\n                type: \"purchase\",\n                amount: order.totalPrice,\n                currency: order.currency,\n                description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n                referenceNumber: order.id,\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n                orderId: order.id,\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                date: order.createdAt,\n                createdAt: order.createdAt,\n                updatedAt: order.updatedAt || order.createdAt\n            }));\n    } catch (error) {\n        console.error('Error converting orders to transactions:', error);\n        return [];\n    }\n}\nfunction WalletPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\") // Set to wallet tab since this is wallet page\n    ;\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Use global currency context\n    const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency)();\n    // Mock user email (in real app, this would come from auth)\n    const userEmail = \"<EMAIL>\";\n    // Load user transactions (including orders converted to transactions)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            const loadTransactions = {\n                \"WalletPage.useEffect.loadTransactions\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Load transactions from localStorage (created by order system)\n                        const localTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n                        // Load orders and convert them to transactions for unified display\n                        const orderTransactions = await loadOrdersAsTransactions(userEmail);\n                        // Merge and sort by date (newest first)\n                        const allTransactions = [\n                            ...localTransactions,\n                            ...orderTransactions\n                        ].sort({\n                            \"WalletPage.useEffect.loadTransactions.allTransactions\": (a, b)=>new Date(b.date || b.createdAt).getTime() - new Date(a.date || a.createdAt).getTime()\n                        }[\"WalletPage.useEffect.loadTransactions.allTransactions\"]);\n                        setTransactions(allTransactions);\n                    } catch (error) {\n                        console.error('Error loading transactions:', error);\n                        setError('فشل في تحميل المعاملات');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WalletPage.useEffect.loadTransactions\"];\n            loadTransactions();\n            // Listen for real-time transaction updates (from order creation)\n            const handleTransactionsUpdated = {\n                \"WalletPage.useEffect.handleTransactionsUpdated\": (event)=>{\n                    console.log('Transactions updated:', event.detail);\n                    loadTransactions() // Reload transactions when they change\n                    ;\n                }\n            }[\"WalletPage.useEffect.handleTransactionsUpdated\"];\n            window.addEventListener('ordersUpdated', handleTransactionsUpdated);\n            window.addEventListener('transactionsUpdated', handleTransactionsUpdated);\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    window.removeEventListener('ordersUpdated', handleTransactionsUpdated);\n                    window.removeEventListener('transactionsUpdated', handleTransactionsUpdated);\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        userEmail\n    ]);\n    // Get chat unread count for navigation badge\n    const { unreadCount: chatUnreadCount } = (0,_lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__.useChat)({\n        userId: 'customer-demo',\n        userType: 'customer'\n    });\n    // Enhanced handler for currency change - now updates global currency context\n    const handleCurrencyChange = async (currency)=>{\n        try {\n            // Update global currency context (affects entire app)\n            await updateCurrency(currency);\n        } catch (err) {\n            console.error('Failed to update currency preference:', err);\n        }\n    };\n    // Calculate wallet statistics and balance from transactions\n    const calculateWalletBalance = ()=>{\n        // Get initial balance from localStorage (default: $100 for demo)\n        const initialBalance = parseFloat(localStorage.getItem(\"wallet_balance_\".concat(userEmail)) || '100');\n        // Calculate total spent from completed purchase transactions\n        const totalSpent = transactions.filter((transaction)=>transaction.status === 'completed' && transaction.type === 'purchase').reduce((sum, transaction)=>sum + transaction.amount, 0);\n        // Calculate total deposits from completed deposit transactions\n        const totalDeposits = transactions.filter((transaction)=>transaction.status === 'completed' && transaction.type === 'deposit').reduce((sum, transaction)=>sum + transaction.amount, 0);\n        // Current balance = Initial + Deposits - Spent\n        const currentBalance = initialBalance + totalDeposits - totalSpent;\n        return {\n            currentBalance,\n            initialBalance,\n            totalSpent,\n            totalDeposits\n        };\n    };\n    const balanceInfo = calculateWalletBalance();\n    const walletStats = {\n        currentBalance: balanceInfo.currentBalance,\n        totalSpent: balanceInfo.totalSpent,\n        totalDeposits: balanceInfo.totalDeposits,\n        totalTransactions: transactions.length,\n        completedTransactions: transactions.filter((transaction)=>transaction.status === 'completed').length,\n        pendingTransactions: transactions.filter((transaction)=>transaction.status === 'pending').length,\n        digitalCodes: transactions.filter((transaction)=>transaction.status === 'completed' && transaction.hasDigitalContent).reduce((sum, transaction)=>{\n            var _transaction_digitalContent_contents, _transaction_digitalContent;\n            return sum + (((_transaction_digitalContent = transaction.digitalContent) === null || _transaction_digitalContent === void 0 ? void 0 : (_transaction_digitalContent_contents = _transaction_digitalContent.contents) === null || _transaction_digitalContent_contents === void 0 ? void 0 : _transaction_digitalContent_contents.length) || 0);\n        }, 0)\n    };\n    // ## Handler for adding balance - adds demo balance for testing\n    const handleAddBalance = async ()=>{\n        try {\n            const amount = 50 // Add $50 for demo\n            ;\n            console.log(\"Adding $\".concat(amount, \" to wallet\"));\n            // Create a deposit transaction\n            const depositTransaction = {\n                id: \"txn_deposit_\".concat(Date.now()),\n                userId: userEmail,\n                walletId: \"wallet_\".concat(userEmail),\n                type: \"deposit\",\n                amount: amount,\n                currency: selectedCurrency,\n                description: \"\\uD83D\\uDCB3 Wallet Top-up - $\".concat(amount),\n                referenceNumber: \"DEP_\".concat(Date.now()),\n                status: \"completed\",\n                hasDigitalContent: false,\n                date: new Date(),\n                createdAt: new Date(),\n                updatedAt: new Date()\n            };\n            // Add to localStorage\n            const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n            existingTransactions.unshift(depositTransaction);\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Reload transactions to update the UI\n            const loadTransactions = async ()=>{\n                try {\n                    setIsLoading(true);\n                    const localTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n                    const orderTransactions = await loadOrdersAsTransactions(userEmail);\n                    const allTransactions = [\n                        ...localTransactions,\n                        ...orderTransactions\n                    ].sort((a, b)=>new Date(b.date || b.createdAt).getTime() - new Date(a.date || a.createdAt).getTime());\n                    setTransactions(allTransactions);\n                } catch (error) {\n                    console.error('Error loading transactions:', error);\n                } finally{\n                    setIsLoading(false);\n                }\n            };\n            await loadTransactions();\n            // Show success message\n            console.log(\"✅ Successfully added $\".concat(amount, \" to wallet\"));\n        } catch (error) {\n            console.error('Error adding balance:', error);\n        }\n    };\n    // Navigation handler for navbar\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n            router.refresh();\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 254,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 255,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-slate-900\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"محفظتي\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 271,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"إدارة رصيدك ومعاملاتك المالية بسهولة وأمان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__.WalletBalance, {\n                            walletData: {\n                                balances: [\n                                    {\n                                        id: \"balance_demo\",\n                                        userId: userEmail,\n                                        currency: selectedCurrency,\n                                        amount: walletStats.currentBalance,\n                                        reservedBalance: 0,\n                                        totalDeposits: walletStats.totalDeposits,\n                                        totalWithdrawals: 0,\n                                        totalPurchases: walletStats.totalSpent,\n                                        lastTransactionAt: transactions.length > 0 ? transactions[0].date || transactions[0].createdAt : undefined,\n                                        lastUpdated: new Date(),\n                                        createdAt: new Date()\n                                    }\n                                ],\n                                selectedCurrency: selectedCurrency,\n                                availableCurrencies: availableCurrencies,\n                                totalPurchases: walletStats.totalSpent,\n                                transactions: transactions\n                            },\n                            selectedCurrency: selectedCurrency,\n                            onCurrencyChange: handleCurrencyChange,\n                            onAddBalance: handleAddBalance,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 281,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-100\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__.WalletTransactions, {\n                        transactions: transactions,\n                        selectedCurrency: selectedCurrency,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange,\n                walletNotificationCount: (0,_lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_11__.getDigitalContentNotificationCount)(transactions),\n                unreadChatCount: chatUnreadCount\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 325,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 331,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n        lineNumber: 252,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"7kane9a5Sq60rm/11PdYZorZhIM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency,\n        _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__.useChat\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/WalletPage.tsx\n"));

/***/ })

});