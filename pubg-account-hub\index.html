<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="/favicon.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
    <link rel="icon" type="image/png" sizes="48x48" href="/favicon-48x48.png" />
    <link rel="shortcut icon" href="/favicon.png" />
    <link rel="apple-touch-icon" href="/favicon-48x48.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    
    <!-- Cairo Font - Both local and Google Fonts -->
    <link rel="stylesheet" href="/fonts/cairo.css" />
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Security Headers -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; connect-src 'self' https://api.imgbb.com https://openrouter.ai https://*.firebaseio.com https://*.googleapis.com; img-src 'self' https: data:; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com https: data:;" />
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta name="referrer" content="strict-origin-when-cross-origin" />
    <meta name="description" content="PUBG STORE - متجر حسابات ببجي وباقات UC" />
    
    <title>PUBG STORE - متجر حسابات ببجي وباقات UC</title>
    
    <!-- Critical Cairo font styling -->
    <style>
      @font-face {
        font-family: 'Cairo';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url(https://fonts.gstatic.com/s/cairo/v28/SLXVc1nY6HkvangtZmpQdkhzfH5lkSs2SgRjCAGMQ1z0hGA-W1ToLQ-BaKE.woff2) format('woff2');
        unicode-range: U+0600-06FF, U+0750-077F, U+0870-088E, U+0890-0891, U+0898-08E1, U+08E3-08FF, U+200C-200E, U+2010-2011, U+204F, U+2E41, U+FB50-FDFF, U+FE70-FE74, U+FE76-FEFC;
      }
      
      html[lang="ar"], 
      body {
        font-family: 'Cairo', -apple-system, BlinkMacSystemFont, sans-serif !important;
      }
      
      button, input, select, textarea, div, p, span, h1, h2, h3, h4, h5, h6 {
        font-family: 'Cairo', -apple-system, BlinkMacSystemFont, sans-serif !important;
      }

      /* Language Loader */
      #language-loader {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.2s ease-in-out, visibility 0.2s ease-in-out;
      }
      
      #language-loader.active {
        opacity: 1;
        visibility: visible;
      }
      
      /* Yin Yang loader */
      #language-loader .loader {
        width: 96px;
        box-sizing: content-box;
        height: 48px;
        background: #FFF;
        border-color: #F2A900;
        border-style: solid;
        border-width: 2px 2px 50px 2px;
        border-radius: 100%;
        position: relative;
        animation: 3s yinYang linear infinite;
      }
      
      #language-loader .loader:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 0;
        background: #FFF;
        border: 18px solid #F2A900;
        border-radius: 100%;
        width: 12px;
        height: 12px;
        box-sizing: content-box;
      }
      
      #language-loader .loader:after {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        background: #F2A900;
        border: 18px solid #FFF;
        border-radius: 100%;
        width: 12px;
        height: 12px;
        box-sizing: content-box;
      }
      
      @keyframes yinYang {
        100% {transform: rotate(360deg)}
      }
      
      #language-loader .text {
        margin-top: 24px;
        color: white;
        font-size: 16px;
        font-weight: 500;
      }
    </style>
    <script>
      // Check if we're coming from a language change
      document.addEventListener('DOMContentLoaded', function() {
        const params = new URLSearchParams(window.location.search);
        if (params.has('lang_change')) {
          // Reset global language change tracking variable if it exists
          if (window.isChangingLanguageGlobal) {
            window.isChangingLanguageGlobal.value = false;
          }
          
          // Remove the query parameter without reloading the page
          const url = new URL(window.location);
          url.searchParams.delete('lang_change');
          window.history.replaceState({}, '', url);
          
          // Hide the loader after a short delay
          setTimeout(() => {
            const languageLoader = document.getElementById('language-loader');
            if (languageLoader) {
              languageLoader.classList.remove('active');
            }
          }, 500);
        }
      });
    </script>
  </head>
  <body class="font-cairo">
    <!-- Language loader overlay -->
    <div id="language-loader">
      <div class="loader"></div>
      <div class="text" id="language-loader-text">...Changing language</div>
    </div>
    
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
