"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./components/pages/WalletPage.tsx":
/*!*****************************************!*\
  !*** ./components/pages/WalletPage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletPage: () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/wallet/WalletBalance */ \"(app-pages-browser)/./components/wallet/WalletBalance.tsx\");\n/* harmony import */ var _components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletTransactions */ \"(app-pages-browser)/./components/wallet/WalletTransactions.tsx\");\n/* harmony import */ var _components_wallet_WalletOrders__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/wallet/WalletOrders */ \"(app-pages-browser)/./components/wallet/WalletOrders.tsx\");\n/* harmony import */ var _barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils/digitalContentUtils */ \"(app-pages-browser)/./lib/utils/digitalContentUtils.ts\");\n/* harmony import */ var _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/useChat */ \"(app-pages-browser)/./lib/hooks/useChat.ts\");\n/* harmony import */ var _lib_services_orderService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/services/orderService */ \"(app-pages-browser)/./lib/services/orderService.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\") // Set to wallet tab since this is wallet page\n    ;\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // Use global currency context\n    const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    // Mock user email (in real app, this would come from auth)\n    const userEmail = \"<EMAIL>\";\n    // Load user orders\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            const loadOrders = {\n                \"WalletPage.useEffect.loadOrders\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const userOrders = await (0,_lib_services_orderService__WEBPACK_IMPORTED_MODULE_14__.getOrdersByUser)(userEmail);\n                        setOrders(userOrders);\n                    } catch (error) {\n                        console.error('Error loading orders:', error);\n                        setError('فشل في تحميل الطلبات');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WalletPage.useEffect.loadOrders\"];\n            loadOrders();\n            // Listen for real-time order updates\n            const handleOrdersUpdated = {\n                \"WalletPage.useEffect.handleOrdersUpdated\": (event)=>{\n                    console.log('Orders updated:', event.detail);\n                    loadOrders() // Reload orders when they change\n                    ;\n                }\n            }[\"WalletPage.useEffect.handleOrdersUpdated\"];\n            window.addEventListener('ordersUpdated', handleOrdersUpdated);\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    window.removeEventListener('ordersUpdated', handleOrdersUpdated);\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        userEmail\n    ]);\n    // Get chat unread count for navigation badge\n    const { unreadCount: chatUnreadCount } = (0,_lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__.useChat)({\n        userId: 'customer-demo',\n        userType: 'customer'\n    });\n    // Enhanced handler for currency change - now updates global currency context\n    const handleCurrencyChange = async (currency)=>{\n        try {\n            // Update global currency context (affects entire app)\n            await updateCurrency(currency);\n        } catch (err) {\n            console.error('Failed to update currency preference:', err);\n        }\n    };\n    // Calculate wallet statistics from orders\n    const walletStats = {\n        totalSpent: orders.filter((order)=>order.status === 'completed').reduce((sum, order)=>sum + order.totalPrice, 0),\n        totalOrders: orders.length,\n        completedOrders: orders.filter((order)=>order.status === 'completed').length,\n        pendingOrders: orders.filter((order)=>order.status === 'pending').length,\n        digitalCodes: orders.filter((order)=>order.status === 'completed').reduce((sum, order)=>{\n            var _order_digitalCodes;\n            return sum + (((_order_digitalCodes = order.digitalCodes) === null || _order_digitalCodes === void 0 ? void 0 : _order_digitalCodes.length) || 0);\n        }, 0)\n    };\n    // ## Handler for adding balance - navigates to checkout page\n    const handleAddBalance = ()=>{\n        router.push(\"/checkout\");\n    };\n    // Navigation handler for navbar\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n            router.refresh();\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-slate-900\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"محفظتي\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"إدارة رصيدك ومعاملاتك المالية بسهولة وأمان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__.WalletBalance, {\n                            walletData: {\n                                balances: [\n                                    {\n                                        id: \"balance_demo\",\n                                        userId: userEmail,\n                                        currency: selectedCurrency,\n                                        amount: walletStats.totalSpent,\n                                        reservedBalance: 0,\n                                        totalDeposits: walletStats.totalSpent,\n                                        totalWithdrawals: 0,\n                                        totalPurchases: walletStats.totalSpent,\n                                        lastTransactionAt: orders.length > 0 ? orders[0].createdAt : undefined,\n                                        lastUpdated: new Date(),\n                                        createdAt: new Date()\n                                    }\n                                ],\n                                selectedCurrency: selectedCurrency,\n                                availableCurrencies: availableCurrencies,\n                                totalPurchases: walletStats.totalSpent,\n                                transactions: [] // TODO: Implement transactions\n                            },\n                            selectedCurrency: selectedCurrency,\n                            onCurrencyChange: handleCurrencyChange,\n                            onAddBalance: handleAddBalance,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-100\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletOrders__WEBPACK_IMPORTED_MODULE_9__.WalletOrders, {\n                            orders: orders,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__.WalletTransactions, {\n                        transactions: [],\n                        selectedCurrency: selectedCurrency,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange,\n                walletNotificationCount: (0,_lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_12__.getDigitalContentNotificationCount)([]),\n                unreadChatCount: chatUnreadCount\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"7de+xVlYkCc8Ah/YiilpUPhT4f4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__.useChat\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/WalletPage.tsx\n"));

/***/ })

});