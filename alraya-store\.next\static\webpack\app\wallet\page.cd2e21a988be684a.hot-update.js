"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./components/pages/WalletPage.tsx":
/*!*****************************************!*\
  !*** ./components/pages/WalletPage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletPage: () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/wallet/WalletBalance */ \"(app-pages-browser)/./components/wallet/WalletBalance.tsx\");\n/* harmony import */ var _components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletTransactions */ \"(app-pages-browser)/./components/wallet/WalletTransactions.tsx\");\n/* harmony import */ var _components_wallet_WalletOrders__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/wallet/WalletOrders */ \"(app-pages-browser)/./components/wallet/WalletOrders.tsx\");\n/* harmony import */ var _barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils/digitalContentUtils */ \"(app-pages-browser)/./lib/utils/digitalContentUtils.ts\");\n/* harmony import */ var _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/hooks/useChat */ \"(app-pages-browser)/./lib/hooks/useChat.ts\");\n/* harmony import */ var _lib_services_orderService__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/services/orderService */ \"(app-pages-browser)/./lib/services/orderService.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WalletPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\") // Set to wallet tab since this is wallet page\n    ;\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter)();\n    // Use global currency context\n    const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency)();\n    // Mock user email (in real app, this would come from auth)\n    const userEmail = \"<EMAIL>\";\n    // Load user orders\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            const loadOrders = {\n                \"WalletPage.useEffect.loadOrders\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const userOrders = await (0,_lib_services_orderService__WEBPACK_IMPORTED_MODULE_14__.getOrdersByUser)(userEmail);\n                        setOrders(userOrders);\n                    } catch (error) {\n                        console.error('Error loading orders:', error);\n                        setError('فشل في تحميل الطلبات');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WalletPage.useEffect.loadOrders\"];\n            loadOrders();\n            // Listen for real-time order updates\n            const handleOrdersUpdated = {\n                \"WalletPage.useEffect.handleOrdersUpdated\": (event)=>{\n                    console.log('Orders updated:', event.detail);\n                    loadOrders() // Reload orders when they change\n                    ;\n                }\n            }[\"WalletPage.useEffect.handleOrdersUpdated\"];\n            window.addEventListener('ordersUpdated', handleOrdersUpdated);\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    window.removeEventListener('ordersUpdated', handleOrdersUpdated);\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        userEmail\n    ]);\n    // Get chat unread count for navigation badge\n    const { unreadCount: chatUnreadCount } = (0,_lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__.useChat)({\n        userId: 'customer-demo',\n        userType: 'customer'\n    });\n    // Enhanced handler for currency change - now updates global currency context\n    const handleCurrencyChange = async (currency)=>{\n        try {\n            // Update global currency context (affects entire app)\n            await updateCurrency(currency);\n        } catch (err) {\n            console.error('Failed to update currency preference:', err);\n        }\n    };\n    // Calculate wallet statistics from orders\n    const walletStats = {\n        totalSpent: orders.filter((order)=>order.status === 'completed').reduce((sum, order)=>sum + order.totalPrice, 0),\n        totalOrders: orders.length,\n        completedOrders: orders.filter((order)=>order.status === 'completed').length,\n        pendingOrders: orders.filter((order)=>order.status === 'pending').length,\n        digitalCodes: orders.filter((order)=>order.status === 'completed').reduce((sum, order)=>{\n            var _order_digitalCodes;\n            return sum + (((_order_digitalCodes = order.digitalCodes) === null || _order_digitalCodes === void 0 ? void 0 : _order_digitalCodes.length) || 0);\n        }, 0)\n    };\n    // ## Handler for adding balance - navigates to checkout page\n    const handleAddBalance = ()=>{\n        router.push(\"/checkout\");\n    };\n    // Navigation handler for navbar\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n            router.refresh();\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-8 w-8 text-slate-900\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"محفظتي\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"إدارة رصيدك ومعاملاتك المالية بسهولة وأمان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__.WalletBalance, {\n                            walletData: {\n                                balance: walletStats.totalSpent,\n                                currency: selectedCurrency,\n                                orders: orders,\n                                transactions: [],\n                                lastUpdated: new Date()\n                            },\n                            selectedCurrency: selectedCurrency,\n                            onCurrencyChange: handleCurrencyChange,\n                            onAddBalance: handleAddBalance,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-100\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletOrders__WEBPACK_IMPORTED_MODULE_9__.WalletOrders, {\n                            orders: orders,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__.WalletTransactions, {\n                        transactions: [],\n                        selectedCurrency: selectedCurrency,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange,\n                walletNotificationCount: (0,_lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_12__.getDigitalContentNotificationCount)([]),\n                unreadChatCount: chatUnreadCount\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"7de+xVlYkCc8Ah/YiilpUPhT4f4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_10__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_11__.useCurrency,\n        _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_13__.useChat\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/WalletPage.tsx\n"));

/***/ })

});