"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data: productData, error: productError } = await supabase\n    .from('products')\n    .insert({\n      name: product.name,\n      name_english: product.nameEnglish,\n      description: product.description,\n      category: product.category,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (productError) throw productError\n  \n  // Insert packages\n  if (product.packages.length > 0) {\n    const { error: packagesError } = await supabase\n      .from('product_packages')\n      .insert(product.packages.map(pkg => ({\n        product_id: productData.id,\n        name: pkg.name,\n        // ... other package fields\n      })))\n    \n    if (packagesError) throw packagesError\n  }\n  \n  // Insert custom fields\n  if (product.fields.length > 0) {\n    const { error: fieldsError } = await supabase\n      .from('custom_fields')\n      .insert(product.fields.map(field => ({\n        product_id: productData.id,\n        field_type: field.type,\n        // ... other field properties\n      })))\n    \n    if (fieldsError) throw fieldsError\n  }\n  \n  return getProductById(productData.id)\n  */ // Temporary: Save to localStorage\n    const newProduct = {\n        ...product,\n        id: generateId(),\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    saveProductTemplate(newProduct);\n    return newProduct;\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .update({\n      name: updates.name,\n      description: updates.description,\n      // ... other fields\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', id)\n    .select()\n    .single()\n  \n  if (error) throw error\n  \n  // Update packages and fields if provided\n  // ... handle packages and fields updates\n  \n  return getProductById(id)\n  */ // Temporary: Update in localStorage\n    const products = loadProductTemplates();\n    const index = products.findIndex((p)=>p.id === id);\n    if (index === -1) throw new Error('Product not found');\n    const updatedProduct = {\n        ...products[index],\n        ...updates,\n        updatedAt: new Date()\n    };\n    saveProductTemplate(updatedProduct);\n    return updatedProduct;\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // ## TODO: Replace with Supabase cascade delete\n    /*\n  const { error } = await supabase\n    .from('products')\n    .delete()\n    .eq('id', id)\n  \n  if (error) throw error\n  */ // Temporary: Remove from localStorage\n    deleteProductTemplate(id);\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages) || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});