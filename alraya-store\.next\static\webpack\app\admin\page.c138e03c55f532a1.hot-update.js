"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(\"Product with id \".concat(id, \" not found\"));\n        }\n        console.log('✅ Updated product: \"'.concat(updatedProduct.name, '\"'));\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // ## TODO: Replace with Supabase cascade delete\n    /*\n  const { error } = await supabase\n    .from('products')\n    .delete()\n    .eq('id', id)\n  \n  if (error) throw error\n  */ // Temporary: Remove from localStorage\n    deleteProductTemplate(id);\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages) || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});