/**
 * Order Service
 * 
 * Handles all order-related operations using localStorage
 */

import { Order, ProductFormData, ProductTemplate, DigitalCode, CheckoutUserDetails } from '@/lib/types'
import { OrderStorage, ProductStorage } from '@/lib/storage/localStorage'
import { assignDigitalCode } from './productService'

/**
 * Create order from product form data
 */
export async function createOrderFromProduct(
  formData: ProductFormData,
  userDetails: CheckoutUserDetails
): Promise<Order> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300))

  try {
    // Validate that the selected package is still available
    const product = ProductStorage.getById(formData.templateId)
    if (!product) {
      throw new Error('Product not found')
    }

    const selectedPackage = product.packages.find(pkg => pkg.id === formData.selectedPackage.id)
    if (!selectedPackage || !selectedPackage.isActive) {
      throw new Error('Selected package is no longer available')
    }

    // Check digital code availability if needed
    if (selectedPackage.digitalCodes) {
      const availableCodes = selectedPackage.digitalCodes.filter(code => !code.used)
      if (availableCodes.length < formData.quantity) {
        throw new Error(`Only ${availableCodes.length} codes available, but ${formData.quantity} requested`)
      }
    }

    // Create the order
    const orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
      productId: formData.templateId,
      productName: product.name,
      packageId: formData.selectedPackage.id,
      packageName: formData.selectedPackage.name,
      quantity: formData.quantity,
      unitPrice: formData.selectedPackage.price,
      totalPrice: formData.totalPrice,
      currency: formData.currency,
      status: 'pending',
      userDetails,
      customFields: formData.customFields,
      digitalCodes: [],
      processingType: product.processingType,
      deliveryType: product.deliveryType,
      timeline: [
        {
          id: `timeline_${Date.now()}`,
          status: 'pending',
          timestamp: new Date(),
          message: 'Order created and awaiting processing',
          isVisible: true
        }
      ]
    }

    const newOrder = OrderStorage.create(orderData)

    // Auto-assign digital codes if it's an instant processing product
    if (product.processingType === 'instant' && selectedPackage.digitalCodes) {
      await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity)
    }

    console.log(`✅ Created order: ${newOrder.id} for product: ${product.name}`)
    return newOrder

  } catch (error) {
    console.error('Error creating order:', error)
    throw error
  }
}

/**
 * Assign digital codes to an order
 */
export async function assignDigitalCodesToOrder(
  orderId: string,
  productId: string,
  packageId: string,
  quantity: number
): Promise<DigitalCode[]> {
  const assignedCodes: DigitalCode[] = []

  try {
    for (let i = 0; i < quantity; i++) {
      const code = await assignDigitalCode(productId, packageId, orderId)
      if (code) {
        assignedCodes.push(code)
      } else {
        throw new Error(`Failed to assign digital code ${i + 1} of ${quantity}`)
      }
    }

    // Add codes to order
    if (assignedCodes.length > 0) {
      OrderStorage.addDigitalCodes(orderId, assignedCodes)
      
      // Update order status to completed if all codes assigned
      OrderStorage.updateStatus(orderId, 'completed', `${assignedCodes.length} digital codes assigned`)
    }

    return assignedCodes

  } catch (error) {
    console.error('Error assigning digital codes:', error)
    
    // Update order status to failed
    OrderStorage.updateStatus(orderId, 'failed', `Failed to assign digital codes: ${error}`)
    
    throw error
  }
}

/**
 * Get all orders
 */
export async function getOrders(): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getAll()
}

/**
 * Get order by ID
 */
export async function getOrderById(id: string): Promise<Order | null> {
  await new Promise(resolve => setTimeout(resolve, 50))
  return OrderStorage.getById(id)
}

/**
 * Get orders by user email
 */
export async function getOrdersByUser(userEmail: string): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getByUser(userEmail)
}

/**
 * Get orders by status
 */
export async function getOrdersByStatus(status: string): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getByStatus(status)
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  orderId: string,
  status: string,
  notes?: string
): Promise<Order | null> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.updateStatus(orderId, status, notes)
}

/**
 * Process pending orders (for admin use)
 */
export async function processPendingOrders(): Promise<{
  processed: number
  failed: number
  errors: string[]
}> {
  const pendingOrders = await getOrdersByStatus('pending')
  let processed = 0
  let failed = 0
  const errors: string[] = []

  for (const order of pendingOrders) {
    try {
      // Try to assign digital codes if not already assigned
      if (order.digitalCodes.length === 0 && order.packageId) {
        await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity)
        processed++
      } else {
        // Just mark as completed if codes already assigned
        await updateOrderStatus(order.id, 'completed', 'Order processed successfully')
        processed++
      }
    } catch (error) {
      failed++
      errors.push(`Order ${order.id}: ${error}`)
      await updateOrderStatus(order.id, 'failed', `Processing failed: ${error}`)
    }
  }

  return { processed, failed, errors }
}

/**
 * Get order statistics
 */
export async function getOrderStats(): Promise<{
  total: number
  pending: number
  completed: number
  failed: number
  totalRevenue: number
}> {
  const orders = await getOrders()
  
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    completed: orders.filter(o => o.status === 'completed').length,
    failed: orders.filter(o => o.status === 'failed').length,
    totalRevenue: orders
      .filter(o => o.status === 'completed')
      .reduce((sum, o) => sum + o.totalPrice, 0)
  }

  return stats
}

/**
 * Cancel order (if still pending)
 */
export async function cancelOrder(orderId: string, reason?: string): Promise<boolean> {
  const order = await getOrderById(orderId)
  if (!order || order.status !== 'pending') {
    return false
  }

  const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user')
  return !!updated
}

/**
 * Refund order (release digital codes back to pool)
 */
export async function refundOrder(orderId: string, reason?: string): Promise<boolean> {
  const order = await getOrderById(orderId)
  if (!order) return false

  try {
    // Release digital codes back to the product
    if (order.digitalCodes.length > 0) {
      const product = ProductStorage.getById(order.productId)
      if (product) {
        const pkg = product.packages.find(p => p.id === order.packageId)
        if (pkg && pkg.digitalCodes) {
          // Mark codes as unused and remove order assignment
          order.digitalCodes.forEach(orderCode => {
            const productCode = pkg.digitalCodes!.find(pc => pc.id === orderCode.id)
            if (productCode) {
              productCode.used = false
              productCode.assignedToOrderId = null
              productCode.usedAt = undefined
            }
          })
          
          // Update product in storage
          ProductStorage.update(order.productId, product)
        }
      }
    }

    // Update order status
    await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded')
    
    return true
  } catch (error) {
    console.error('Error refunding order:', error)
    return false
  }
}
