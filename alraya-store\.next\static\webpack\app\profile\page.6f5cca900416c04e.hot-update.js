"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/profile/page",{

/***/ "(app-pages-browser)/./components/layout/MobileNavigation.tsx":
/*!************************************************!*\
  !*** ./components/layout/MobileNavigation.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileNavigation: () => (/* binding */ MobileNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/ChatSystem */ \"(app-pages-browser)/./components/chat/ChatSystem.tsx\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileNavigation auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MobileNavigation(param) {\n    let { activeTab, onTabChange, unreadChatCount = 0, walletNotificationCount = 0 } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__.useGlobalChat)();\n    // Standardized navigation items matching desktop\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 40,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            center: true,\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    walletNotificationCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__.ChatBadge, {\n                        count: walletNotificationCount,\n                        className: \"absolute -top-2 -right-2 scale-75\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    unreadChatCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__.ChatBadge, {\n                        count: unreadChatCount,\n                        className: \"absolute -top-2 -right-2 scale-75\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this),\n            label: \"الدعم\",\n            action: ()=>openChat() // Opens chat for support\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-slate-800/90 backdrop-blur-2xl rounded-3xl px-4 py-3 shadow-2xl border border-slate-700/50 ring-1 ring-white/10\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: navItems.map((param)=>{\n                    let { id, icon, label, center, action } = param;\n                    return center ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: action,\n                        className: \"relative flex flex-col items-center justify-center p-4 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-110 active:scale-95 \".concat(activeTab === id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-yellow-500/25\" : \"bg-gradient-to-r from-yellow-400/80 to-orange-500/80 text-slate-900 hover:from-yellow-400 hover:to-orange-500\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    icon,\n                                    activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-white/20 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 17\n                            }, this),\n                            label && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-semibold mt-1 opacity-90\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: action,\n                        className: \"relative flex flex-col items-center justify-center space-y-1 p-3 rounded-2xl transition-all duration-300 transform hover:scale-110 active:scale-95 min-w-[3rem] \".concat(activeTab === id ? \"bg-white/20 text-yellow-400 shadow-lg shadow-yellow-400/20 ring-1 ring-yellow-400/30\" : \"text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    icon,\n                                    activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-yellow-400/20 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-medium transition-all duration-300 \".concat(activeTab === id ? \"text-yellow-400\" : \"text-slate-400\"),\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 17\n                            }, this),\n                            activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-yellow-400 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 19\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileNavigation, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__.useGlobalChat\n    ];\n});\n_c = MobileNavigation;\nvar _c;\n$RefreshReg$(_c, \"MobileNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/MobileNavigation.tsx\n"));

/***/ })

});