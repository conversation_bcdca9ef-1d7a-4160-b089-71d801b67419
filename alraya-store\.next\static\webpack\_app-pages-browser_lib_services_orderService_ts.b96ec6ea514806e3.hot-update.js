"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_lib_services_orderService_ts",{

/***/ "(app-pages-browser)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Check if user has sufficient wallet balance for purchase\n */ async function checkWalletBalance(userEmail, requiredAmount) {\n    try {\n        // Get initial balance from localStorage (default: $100 for demo)\n        const initialBalance = parseFloat(localStorage.getItem(\"wallet_balance_\".concat(userEmail)) || '100');\n        // Get all transactions for this user\n        const allTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        const userTransactions = allTransactions.filter((t)=>t.userId === userEmail);\n        // Calculate total spent from completed purchase transactions\n        const totalSpent = userTransactions.filter((t)=>t.status === 'completed' && t.type === 'purchase').reduce((sum, t)=>sum + t.amount, 0);\n        // Calculate total deposits from completed deposit transactions\n        const totalDeposits = userTransactions.filter((t)=>t.status === 'completed' && t.type === 'deposit').reduce((sum, t)=>sum + t.amount, 0);\n        // Current balance = Initial + Deposits - Spent\n        const currentBalance = initialBalance + totalDeposits - totalSpent;\n        console.log(\"\\uD83D\\uDCB0 Wallet Balance Check for \".concat(userEmail, \":\"), {\n            currentBalance: currentBalance.toFixed(2),\n            requiredAmount: requiredAmount.toFixed(2),\n            sufficient: currentBalance >= requiredAmount\n        });\n        return currentBalance >= requiredAmount;\n    } catch (error) {\n        console.error('Error checking wallet balance:', error);\n        return false // Fail safe - deny purchase if balance check fails\n        ;\n    }\n}\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(\"Only \".concat(availableCodes.length, \" codes available, but \").concat(formData.quantity, \" requested\"));\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(\"Only \".concat(selectedPackage.quantityLimit, \" items available, but \").concat(formData.quantity, \" requested\"));\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Check wallet balance before creating order\n        const totalCost = selectedPackage.price * formData.quantity;\n        const hasSufficientBalance = await checkWalletBalance(formData.userDetails.email, totalCost);\n        if (!hasSufficientBalance) {\n            throw new Error(\"Insufficient wallet balance. Required: $\".concat(totalCost.toFixed(2)));\n        }\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: \"timeline_\".concat(Date.now()),\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            // Ensure the package has available digital codes\n            await ensureDigitalCodesAvailable(formData.templateId, formData.selectedPackage.id);\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        // Create corresponding wallet transaction for unified display\n        await createWalletTransactionFromOrder(newOrder);\n        console.log(\"✅ Created order: \".concat(newOrder.id, \" for product: \").concat(product.name));\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Ensure digital codes are available for a package\n */ async function ensureDigitalCodesAvailable(productId, packageId) {\n    const availableCodes = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.getAvailableCodes)(productId, packageId);\n    if (availableCodes.length === 0) {\n        console.log(\"⚠️ No digital codes available for package \".concat(packageId, \", adding sample codes...\"));\n        // Add sample digital codes to the package\n        const { ProductStorage } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\"));\n        const product = ProductStorage.getById(productId);\n        if (product) {\n            const pkg = product.packages.find((p)=>p.id === packageId);\n            if (pkg) {\n                // Add sample digital codes\n                const sampleCodes = [\n                    {\n                        id: \"code_\".concat(Date.now(), \"_1\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_2\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_3\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    }\n                ];\n                pkg.digitalCodes = [\n                    ...pkg.digitalCodes || [],\n                    ...sampleCodes\n                ];\n                ProductStorage.update(productId, product);\n                console.log(\"✅ Added \".concat(sampleCodes.length, \" sample digital codes to package \").concat(packageId));\n            }\n        }\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(\"Failed to assign digital code \".concat(i + 1, \" of \").concat(quantity));\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', \"\".concat(assignedCodes.length, \" digital codes assigned\"));\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', \"Failed to assign digital codes: \".concat(error));\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        const updatedOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n        if (updatedOrder) {\n            console.log(\"✅ Updated order \".concat(orderId, \" status to: \").concat(status));\n            // Update corresponding wallet transaction\n            await updateWalletTransactionFromOrder(updatedOrder);\n        }\n        return updatedOrder;\n    } catch (error) {\n        console.error('Error updating order status:', error);\n        return null;\n    }\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(\"Order \".concat(order.id, \": \").concat(error));\n            await updateOrderStatus(order.id, 'failed', \"Processing failed: \".concat(error));\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n/**\n * Create a wallet transaction from an order for unified display in the wallet\n */ async function createWalletTransactionFromOrder(order) {\n    try {\n        const transaction = {\n            userId: order.userDetails.email,\n            walletId: \"wallet_\".concat(order.userDetails.email),\n            type: \"purchase\",\n            amount: order.totalPrice,\n            currency: order.currency,\n            description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n            referenceNumber: order.id,\n            status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n            orderId: order.id,\n            hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n            digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                status: order.status === 'completed' ? 'ready' : 'pending',\n                contents: order.digitalCodes.map((code)=>({\n                        id: code.id,\n                        type: 'game_code',\n                        title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                        content: code.key,\n                        instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                        isRevealed: false,\n                        deliveredAt: order.updatedAt || order.createdAt\n                    })),\n                deliveryMethod: 'instant',\n                lastUpdated: order.updatedAt || order.createdAt\n            } : undefined,\n            createdAt: order.createdAt,\n            updatedAt: order.updatedAt || order.createdAt\n        };\n        // Create full transaction with ID and date\n        const fullTransaction = {\n            ...transaction,\n            id: \"txn_order_\".concat(order.id),\n            date: order.createdAt\n        };\n        // Save to localStorage for wallet display\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Check if transaction already exists to avoid duplicates\n        const existingIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (existingIndex >= 0) {\n            // Update existing transaction\n            existingTransactions[existingIndex] = fullTransaction;\n        } else {\n            // Add new transaction\n            existingTransactions.unshift(fullTransaction);\n        }\n        localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n        // Dispatch event to notify wallet page of transaction update\n        window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n            detail: {\n                transaction: fullTransaction,\n                order\n            }\n        }));\n        console.log(\"✅ Created wallet transaction for order: \".concat(order.id));\n    } catch (error) {\n        console.error('Error creating wallet transaction from order:', error);\n    }\n}\n/**\n * Update wallet transaction when order status changes\n */ async function updateWalletTransactionFromOrder(order) {\n    try {\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Find the transaction for this order\n        const transactionIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (transactionIndex >= 0) {\n            // Update the existing transaction\n            const updatedTransaction = {\n                ...existingTransactions[transactionIndex],\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : order.status === 'cancelled' ? 'cancelled' : 'pending',\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                updatedAt: order.updatedAt || order.createdAt\n            };\n            existingTransactions[transactionIndex] = updatedTransaction;\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Dispatch event to notify wallet page\n            window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n                detail: {\n                    transaction: updatedTransaction,\n                    order\n                }\n            }));\n            console.log(\"✅ Updated wallet transaction for order: \".concat(order.id));\n        } else {\n            // Transaction doesn't exist, create it\n            await createWalletTransactionFromOrder(order);\n        }\n    } catch (error) {\n        console.error('Error updating wallet transaction from order:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/orderService.ts\n"));

/***/ })

});