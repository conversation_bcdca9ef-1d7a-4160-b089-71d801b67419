"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .update({\n      name: updates.name,\n      description: updates.description,\n      // ... other fields\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', id)\n    .select()\n    .single()\n  \n  if (error) throw error\n  \n  // Update packages and fields if provided\n  // ... handle packages and fields updates\n  \n  return getProductById(id)\n  */ // Temporary: Update in localStorage\n    const products = loadProductTemplates();\n    const index = products.findIndex((p)=>p.id === id);\n    if (index === -1) throw new Error('Product not found');\n    const updatedProduct = {\n        ...products[index],\n        ...updates,\n        updatedAt: new Date()\n    };\n    saveProductTemplate(updatedProduct);\n    return updatedProduct;\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // ## TODO: Replace with Supabase cascade delete\n    /*\n  const { error } = await supabase\n    .from('products')\n    .delete()\n    .eq('id', id)\n  \n  if (error) throw error\n  */ // Temporary: Remove from localStorage\n    deleteProductTemplate(id);\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages) || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});