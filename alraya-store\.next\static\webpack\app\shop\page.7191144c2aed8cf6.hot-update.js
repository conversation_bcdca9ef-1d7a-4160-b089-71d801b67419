"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./components/layout/DesktopFooter.tsx":
/*!*********************************************!*\
  !*** ./components/layout/DesktopFooter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DesktopFooter: () => (/* binding */ DesktopFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ DesktopFooter auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DesktopFooter(param) {\n    let { activeTab, onTabChange } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat)();\n    // Standardized navigation items matching mobile\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, this),\n            label: \"الدعم الفني\",\n            action: ()=>openChat() // Opens chat for support, consistent with mobile\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-8 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-8\",\n                children: navItems.map((param)=>{\n                    let { id, icon, label, action } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: action,\n                        className: \"relative flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 min-w-[5rem] \".concat(activeTab === id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg shadow-yellow-500/25 ring-1 ring-yellow-400/30\" : \"text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    icon,\n                                    activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-white/20 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium transition-all duration-300 \".concat(activeTab === id ? \"text-slate-900\" : \"text-slate-400\"),\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-slate-900 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopFooter, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat\n    ];\n});\n_c = DesktopFooter;\nvar _c;\n$RefreshReg$(_c, \"DesktopFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/DesktopFooter.tsx\n"));

/***/ })

});