"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { ProductPackage, Currency, enhancePackageWithDiscountInfo } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"
import { Zap, Star, Shield, AlertTriangle } from "lucide-react"

interface ModernPackageGridProps {
  packages: ProductPackage[]
  selectedPackage?: ProductPackage
  onPackageSelect: (pkg: ProductPackage) => void
  currency: Currency
  disabled?: boolean
  className?: string
}

export function ModernPackageGrid({
  packages,
  selectedPackage,
  onPackageSelect,
  currency,
  disabled = false,
  className = ""
}: ModernPackageGridProps) {
  const { convertPrice } = useCurrencyConverter()

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = convertPrice(priceUSD, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  // Filter active packages and sort by price
  const activePackages = packages
    .filter(pkg => pkg.isActive)
    .sort((a, b) => a.price - b.price)

  if (activePackages.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-2">لا توجد حزم متاحة حالياً</div>
      </div>
    )
  }

  // Determine grid layout based on package count
  const getGridCols = () => {
    if (activePackages.length === 1) return "grid-cols-1"
    if (activePackages.length === 2) return "grid-cols-2"
    if (activePackages.length === 3) return "grid-cols-3"
    return "grid-cols-2"
  }

  return (
    <div className={`space-y-4 ${className}`} dir="rtl">
      <div className={`grid ${getGridCols()} gap-3`}>
        {activePackages.map((pkg, index) => {
          // Enhance package with discount calculations
          const enhancedPkg = enhancePackageWithDiscountInfo(pkg)
          const isSelected = selectedPackage?.id === pkg.id
          const isPopular = pkg.popular || index === Math.floor(activePackages.length / 2) // Middle package as popular if not set
          const hasDiscount = enhancedPkg.hasDiscount
          const availableCodes = pkg.digitalCodes?.filter(code => !code.used).length || 0
          const isLowStock = availableCodes > 0 && availableCodes < 5
          const isOutOfStock = availableCodes === 0
          
          return (
            <button
              key={pkg.id}
              onClick={() => !disabled && !isOutOfStock && onPackageSelect(pkg)}
              disabled={disabled || isOutOfStock}
              className={`relative p-4 rounded-2xl border-2 transition-all duration-300 transform hover:scale-105 ${
                isOutOfStock
                  ? 'border-red-500/50 bg-gradient-to-br from-red-500/10 to-red-600/10 opacity-60 cursor-not-allowed'
                  : isSelected
                  ? 'border-yellow-400 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 shadow-xl shadow-yellow-400/20'
                  : 'border-slate-600 bg-gradient-to-br from-slate-700/50 to-slate-800/50 hover:border-slate-500 hover:shadow-lg'
              } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              {/* Popular Badge */}
              {isPopular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold px-3 py-1 shadow-lg">
                    <Star className="h-3 w-3 ml-1 fill-current" />
                    الأشهر
                  </Badge>
                </div>
              )}

              {/* Discount Badge */}
              {hasDiscount && enhancedPkg.discountPercentage && (
                <div className="absolute -top-2 -right-2">
                  <Badge className="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                    -{enhancedPkg.discountPercentage}%
                  </Badge>
                </div>
              )}

              {/* Package Content */}
              <div className="text-center space-y-2">
                {/* Package Name/Amount */}
                <div className="space-y-1">
                  <div className="text-white font-bold text-lg leading-tight">
                    {pkg.name}
                  </div>
                  <div className="text-slate-300 text-sm">
                    {pkg.amount}
                  </div>
                </div>

                {/* Pricing */}
                <div className="space-y-1">
                  <div className="text-yellow-400 font-bold text-xl">
                    {formatPrice(pkg.price)}
                  </div>
                  
                  {hasDiscount && (
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-slate-500 text-sm line-through">
                        {formatPrice(pkg.originalPrice!)}
                      </span>
                    </div>
                  )}
                </div>

                {/* Stock Status */}
                {availableCodes > 0 && (
                  <div className="flex items-center justify-center gap-1 text-xs">
                    <Shield className="h-3 w-3 text-green-400" />
                    <span className={`${isLowStock ? 'text-yellow-400' : 'text-green-400'}`}>
                      {availableCodes} متاح
                    </span>
                    {isLowStock && <AlertTriangle className="h-3 w-3 text-yellow-400" />}
                  </div>
                )}

                {isOutOfStock && (
                  <div className="flex items-center justify-center gap-1 text-xs text-red-400">
                    <AlertTriangle className="h-3 w-3" />
                    <span>نفد المخزون</span>
                  </div>
                )}

                {/* Selection Indicator */}
                {isSelected && !isOutOfStock && (
                  <div className="flex items-center justify-center">
                    <div className="w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-slate-900 rounded-full"></div>
                    </div>
                  </div>
                )}
              </div>

              {/* Hover Effect Overlay */}
              <div className={`absolute inset-0 rounded-2xl transition-opacity duration-300 ${
                isSelected 
                  ? 'bg-gradient-to-br from-yellow-400/10 to-orange-500/10' 
                  : 'bg-gradient-to-br from-white/0 to-white/5 opacity-0 hover:opacity-100'
              }`} />
            </button>
          )
        })}
      </div>

      {/* Package Benefits */}
      <div className="grid grid-cols-2 gap-4 mt-6 text-xs">
        <div className="flex items-center gap-2 text-slate-400">
          <div className="w-2 h-2 bg-green-400 rounded-full"></div>
          <span>تسليم فوري</span>
        </div>
        <div className="flex items-center gap-2 text-slate-400">
          <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
          <span>ضمان الجودة</span>
        </div>
        <div className="flex items-center gap-2 text-slate-400">
          <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
          <span>دعم فني 24/7</span>
        </div>
        <div className="flex items-center gap-2 text-slate-400">
          <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
          <span>أسعار تنافسية</span>
        </div>
      </div>
    </div>
  )
}

/**
 * Compact version for smaller spaces
 */
export function CompactPackageGrid({
  packages,
  selectedPackage,
  onPackageSelect,
  currency,
  disabled = false
}: Omit<ModernPackageGridProps, 'className'>) {
  const { convertPrice } = useCurrencyConverter()

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = convertPrice(priceUSD, "USD", currency)
    return formatCurrency(convertedPrice, currency)
  }

  const activePackages = packages.filter(pkg => pkg.isActive)

  return (
    <div className="space-y-2" dir="rtl">
      {activePackages.map((pkg) => {
        const enhancedPkg = enhancePackageWithDiscountInfo(pkg)
        const isSelected = selectedPackage?.id === pkg.id
        const hasDiscount = enhancedPkg.hasDiscount
        
        return (
          <button
            key={pkg.id}
            onClick={() => !disabled && onPackageSelect(pkg)}
            disabled={disabled}
            className={`w-full p-3 rounded-xl border transition-all duration-200 flex items-center justify-between ${
              isSelected
                ? 'border-yellow-400 bg-yellow-400/10'
                : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
            }`}
          >
            <div className="flex items-center gap-3">
              {pkg.popular && (
                <Badge className="bg-yellow-500/20 text-yellow-400 text-xs">
                  شائع
                </Badge>
              )}
              <div className="text-right">
                <div className="text-white font-medium text-sm">{pkg.name}</div>
                <div className="text-slate-400 text-xs">{pkg.amount}</div>
              </div>
            </div>
            
            <div className="text-left">
              <div className="text-yellow-400 font-bold text-sm">
                {formatPrice(pkg.price)}
              </div>
              {hasDiscount && (
                <div className="text-slate-500 text-xs line-through">
                  {formatPrice(pkg.originalPrice!)}
                </div>
              )}
            </div>
          </button>
        )
      })}
    </div>
  )
}
