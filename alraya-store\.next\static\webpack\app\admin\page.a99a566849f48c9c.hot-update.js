"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   hardDeleteProduct: () => (/* binding */ hardDeleteProduct),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(\"Product with id \".concat(id, \" not found\"));\n        }\n        console.log('✅ Updated product: \"'.concat(updatedProduct.name, '\"'));\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Soft delete by setting isActive to false\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (!product) return false;\n        const updated = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, {\n            isActive: false\n        });\n        console.log('✅ Soft deleted product: \"'.concat(product.name, '\"'));\n        return !!updated;\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return false;\n    }\n}\n/**\n * Hard delete product (completely remove from storage)\n */ async function hardDeleteProduct(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const success = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.delete(id);\n        console.log(\"✅ Hard deleted product with ID: \".concat(id));\n        return success;\n    } catch (error) {\n        console.error('Error hard deleting product:', error);\n        return false;\n    }\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages) || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});