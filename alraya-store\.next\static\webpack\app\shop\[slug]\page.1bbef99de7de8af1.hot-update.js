"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(\"Only \".concat(availableCodes.length, \" codes available, but \").concat(formData.quantity, \" requested\"));\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(\"Only \".concat(selectedPackage.quantityLimit, \" items available, but \").concat(formData.quantity, \" requested\"));\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Check wallet balance before creating order\n        const totalCost = selectedPackage.price * formData.quantity;\n        const hasSufficientBalance = await checkWalletBalance(formData.userDetails.email, totalCost);\n        if (!hasSufficientBalance) {\n            throw new Error(\"Insufficient wallet balance. Required: $\".concat(totalCost.toFixed(2)));\n        }\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: \"timeline_\".concat(Date.now()),\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            // Ensure the package has available digital codes\n            await ensureDigitalCodesAvailable(formData.templateId, formData.selectedPackage.id);\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        // Create corresponding wallet transaction for unified display\n        await createWalletTransactionFromOrder(newOrder);\n        console.log(\"✅ Created order: \".concat(newOrder.id, \" for product: \").concat(product.name));\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Ensure digital codes are available for a package\n */ async function ensureDigitalCodesAvailable(productId, packageId) {\n    const availableCodes = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.getAvailableCodes)(productId, packageId);\n    if (availableCodes.length === 0) {\n        console.log(\"⚠️ No digital codes available for package \".concat(packageId, \", adding sample codes...\"));\n        // Add sample digital codes to the package\n        const { ProductStorage } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\"));\n        const product = ProductStorage.getById(productId);\n        if (product) {\n            const pkg = product.packages.find((p)=>p.id === packageId);\n            if (pkg) {\n                // Add sample digital codes\n                const sampleCodes = [\n                    {\n                        id: \"code_\".concat(Date.now(), \"_1\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_2\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_3\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    }\n                ];\n                pkg.digitalCodes = [\n                    ...pkg.digitalCodes || [],\n                    ...sampleCodes\n                ];\n                ProductStorage.update(productId, product);\n                console.log(\"✅ Added \".concat(sampleCodes.length, \" sample digital codes to package \").concat(packageId));\n            }\n        }\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(\"Failed to assign digital code \".concat(i + 1, \" of \").concat(quantity));\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', \"\".concat(assignedCodes.length, \" digital codes assigned\"));\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', \"Failed to assign digital codes: \".concat(error));\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        const updatedOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n        if (updatedOrder) {\n            console.log(\"✅ Updated order \".concat(orderId, \" status to: \").concat(status));\n            // Update corresponding wallet transaction\n            await updateWalletTransactionFromOrder(updatedOrder);\n        }\n        return updatedOrder;\n    } catch (error) {\n        console.error('Error updating order status:', error);\n        return null;\n    }\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(\"Order \".concat(order.id, \": \").concat(error));\n            await updateOrderStatus(order.id, 'failed', \"Processing failed: \".concat(error));\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n/**\n * Create a wallet transaction from an order for unified display in the wallet\n */ async function createWalletTransactionFromOrder(order) {\n    try {\n        const transaction = {\n            userId: order.userDetails.email,\n            walletId: \"wallet_\".concat(order.userDetails.email),\n            type: \"purchase\",\n            amount: order.totalPrice,\n            currency: order.currency,\n            description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n            referenceNumber: order.id,\n            status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n            orderId: order.id,\n            hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n            digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                status: order.status === 'completed' ? 'ready' : 'pending',\n                contents: order.digitalCodes.map((code)=>({\n                        id: code.id,\n                        type: 'game_code',\n                        title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                        content: code.key,\n                        instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                        isRevealed: false,\n                        deliveredAt: order.updatedAt || order.createdAt\n                    })),\n                deliveryMethod: 'instant',\n                lastUpdated: order.updatedAt || order.createdAt\n            } : undefined,\n            createdAt: order.createdAt,\n            updatedAt: order.updatedAt || order.createdAt\n        };\n        // Create full transaction with ID and date\n        const fullTransaction = {\n            ...transaction,\n            id: \"txn_order_\".concat(order.id),\n            date: order.createdAt\n        };\n        // Save to localStorage for wallet display\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Check if transaction already exists to avoid duplicates\n        const existingIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (existingIndex >= 0) {\n            // Update existing transaction\n            existingTransactions[existingIndex] = fullTransaction;\n        } else {\n            // Add new transaction\n            existingTransactions.unshift(fullTransaction);\n        }\n        localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n        // Dispatch event to notify wallet page of transaction update\n        window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n            detail: {\n                transaction: fullTransaction,\n                order\n            }\n        }));\n        console.log(\"✅ Created wallet transaction for order: \".concat(order.id));\n    } catch (error) {\n        console.error('Error creating wallet transaction from order:', error);\n    }\n}\n/**\n * Update wallet transaction when order status changes\n */ async function updateWalletTransactionFromOrder(order) {\n    try {\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Find the transaction for this order\n        const transactionIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (transactionIndex >= 0) {\n            // Update the existing transaction\n            const updatedTransaction = {\n                ...existingTransactions[transactionIndex],\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : order.status === 'cancelled' ? 'cancelled' : 'pending',\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                updatedAt: order.updatedAt || order.createdAt\n            };\n            existingTransactions[transactionIndex] = updatedTransaction;\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Dispatch event to notify wallet page\n            window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n                detail: {\n                    transaction: updatedTransaction,\n                    order\n                }\n            }));\n            console.log(\"✅ Updated wallet transaction for order: \".concat(order.id));\n        } else {\n            // Transaction doesn't exist, create it\n            await createWalletTransactionFromOrder(order);\n        }\n    } catch (error) {\n        console.error('Error updating wallet transaction from order:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9zZXJ2aWNlcy9vcmRlclNlcnZpY2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7O0NBSUMsR0FHd0U7QUFDRjtBQUV2RTs7Q0FFQyxHQUNNLGVBQWVJLHVCQUNwQkMsUUFBeUIsRUFDekJDLFdBQWdDO0lBRWhDLHFCQUFxQjtJQUNyQixNQUFNLElBQUlDLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsSUFBSTtRQUNGLHdEQUF3RDtRQUN4RCxNQUFNRSxVQUFVVCxxRUFBY0EsQ0FBQ1UsT0FBTyxDQUFDTixTQUFTTyxVQUFVO1FBQzFELElBQUksQ0FBQ0YsU0FBUztZQUNaLE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLE1BQU1DLGtCQUFrQkosUUFBUUssUUFBUSxDQUFDQyxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLEVBQUUsS0FBS2IsU0FBU1MsZUFBZSxDQUFDSSxFQUFFO1FBQzNGLElBQUksQ0FBQ0osbUJBQW1CLENBQUNBLGdCQUFnQkssUUFBUSxFQUFFO1lBQ2pELE1BQU0sSUFBSU4sTUFBTTtRQUNsQjtRQUVBLDBEQUEwRDtRQUMxRCxJQUFJQyxnQkFBZ0JNLFlBQVksSUFBSU4sZ0JBQWdCTSxZQUFZLENBQUNDLE1BQU0sR0FBRyxHQUFHO1lBQzNFLHdEQUF3RDtZQUN4RCxNQUFNQyxpQkFBaUJSLGdCQUFnQk0sWUFBWSxDQUFDRyxNQUFNLENBQUNDLENBQUFBLE9BQVEsQ0FBQ0EsS0FBS0MsSUFBSTtZQUM3RSxJQUFJSCxlQUFlRCxNQUFNLEdBQUdoQixTQUFTcUIsUUFBUSxFQUFFO2dCQUM3QyxNQUFNLElBQUliLE1BQU0sUUFBc0RSLE9BQTlDaUIsZUFBZUQsTUFBTSxFQUFDLDBCQUEwQyxPQUFsQmhCLFNBQVNxQixRQUFRLEVBQUM7WUFDMUY7UUFDRixPQUFPLElBQUlaLGdCQUFnQmEsYUFBYSxLQUFLQyxhQUFhZCxnQkFBZ0JhLGFBQWEsS0FBSyxNQUFNO1lBQ2hHLHVDQUF1QztZQUN2QyxJQUFJYixnQkFBZ0JhLGFBQWEsR0FBR3RCLFNBQVNxQixRQUFRLEVBQUU7Z0JBQ3JELE1BQU0sSUFBSWIsTUFBTSxRQUE4RFIsT0FBdERTLGdCQUFnQmEsYUFBYSxFQUFDLDBCQUEwQyxPQUFsQnRCLFNBQVNxQixRQUFRLEVBQUM7WUFDbEc7UUFDRjtRQUNBLDhGQUE4RjtRQUU5Riw2Q0FBNkM7UUFDN0MsTUFBTUcsWUFBWWYsZ0JBQWdCZ0IsS0FBSyxHQUFHekIsU0FBU3FCLFFBQVE7UUFDM0QsTUFBTUssdUJBQXVCLE1BQU1DLG1CQUFtQjNCLFNBQVNDLFdBQVcsQ0FBQzJCLEtBQUssRUFBRUo7UUFDbEYsSUFBSSxDQUFDRSxzQkFBc0I7WUFDekIsTUFBTSxJQUFJbEIsTUFBTSwyQ0FBZ0UsT0FBckJnQixVQUFVSyxPQUFPLENBQUM7UUFDL0U7UUFFQSxtQkFBbUI7UUFDbkIsTUFBTUMsWUFBMkQ7WUFDL0RDLFdBQVcvQixTQUFTTyxVQUFVO1lBQzlCeUIsYUFBYTNCLFFBQVE0QixJQUFJO1lBQ3pCQyxXQUFXbEMsU0FBU1MsZUFBZSxDQUFDSSxFQUFFO1lBQ3RDc0IsYUFBYW5DLFNBQVNTLGVBQWUsQ0FBQ3dCLElBQUk7WUFDMUNaLFVBQVVyQixTQUFTcUIsUUFBUTtZQUMzQmUsV0FBV3BDLFNBQVNTLGVBQWUsQ0FBQ2dCLEtBQUs7WUFDekNZLFlBQVlyQyxTQUFTcUMsVUFBVTtZQUMvQkMsVUFBVXRDLFNBQVNzQyxRQUFRO1lBQzNCQyxRQUFRO1lBQ1J0QztZQUNBdUMsY0FBY3hDLFNBQVN3QyxZQUFZO1lBQ25DekIsY0FBYyxFQUFFO1lBQ2hCMEIsZ0JBQWdCcEMsUUFBUW9DLGNBQWM7WUFDdENDLGNBQWNyQyxRQUFRcUMsWUFBWTtZQUNsQ0MsVUFBVTtnQkFDUjtvQkFDRTlCLElBQUksWUFBdUIsT0FBWCtCLEtBQUtDLEdBQUc7b0JBQ3hCTixRQUFRO29CQUNSTyxXQUFXLElBQUlGO29CQUNmRyxTQUFTO29CQUNUQyxXQUFXO2dCQUNiO2FBQ0Q7UUFDSDtRQUVBLE1BQU1DLFdBQVd0RCxtRUFBWUEsQ0FBQ3VELE1BQU0sQ0FBQ3BCO1FBRXJDLGtFQUFrRTtRQUNsRSxJQUFJekIsUUFBUW9DLGNBQWMsS0FBSyxhQUFhaEMsZ0JBQWdCTSxZQUFZLEVBQUU7WUFDeEUsaURBQWlEO1lBQ2pELE1BQU1vQyw0QkFBNEJuRCxTQUFTTyxVQUFVLEVBQUVQLFNBQVNTLGVBQWUsQ0FBQ0ksRUFBRTtZQUNsRixNQUFNdUMsMEJBQTBCSCxTQUFTcEMsRUFBRSxFQUFFYixTQUFTTyxVQUFVLEVBQUVQLFNBQVNTLGVBQWUsQ0FBQ0ksRUFBRSxFQUFFYixTQUFTcUIsUUFBUTtRQUNsSDtRQUVBLDhEQUE4RDtRQUM5RCxNQUFNZ0MsaUNBQWlDSjtRQUV2Q0ssUUFBUUMsR0FBRyxDQUFDLG9CQUFnRGxELE9BQTVCNEMsU0FBU3BDLEVBQUUsRUFBQyxrQkFBNkIsT0FBYlIsUUFBUTRCLElBQUk7UUFDeEUsT0FBT2dCO0lBRVQsRUFBRSxPQUFPTyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3ZDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOztDQUVDLEdBQ0QsZUFBZUwsNEJBQTRCcEIsU0FBaUIsRUFBRUcsU0FBaUI7SUFDN0UsTUFBTWpCLGlCQUFpQixNQUFNbkIsa0VBQWlCQSxDQUFDaUMsV0FBV0c7SUFFMUQsSUFBSWpCLGVBQWVELE1BQU0sS0FBSyxHQUFHO1FBQy9Cc0MsUUFBUUMsR0FBRyxDQUFDLDZDQUF1RCxPQUFWckIsV0FBVTtRQUVuRSwwQ0FBMEM7UUFDMUMsTUFBTSxFQUFFdEMsY0FBYyxFQUFFLEdBQUcsTUFBTSwyS0FBb0M7UUFDckUsTUFBTVMsVUFBVVQsZUFBZVUsT0FBTyxDQUFDeUI7UUFFdkMsSUFBSTFCLFNBQVM7WUFDWCxNQUFNTyxNQUFNUCxRQUFRSyxRQUFRLENBQUNDLElBQUksQ0FBQzhDLENBQUFBLElBQUtBLEVBQUU1QyxFQUFFLEtBQUtxQjtZQUNoRCxJQUFJdEIsS0FBSztnQkFDUCwyQkFBMkI7Z0JBQzNCLE1BQU04QyxjQUFjO29CQUNsQjt3QkFDRTdDLElBQUksUUFBbUIsT0FBWCtCLEtBQUtDLEdBQUcsSUFBRzt3QkFDdkJjLEtBQUssVUFBbUVDLE9BQXpEQSxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxHQUFHQyxXQUFXLElBQUcsS0FBeUQsT0FBdERKLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLEdBQUdDLFdBQVc7d0JBQzNINUMsTUFBTTt3QkFDTjZDLG1CQUFtQjt3QkFDbkJDLFdBQVcsSUFBSXRCO29CQUNqQjtvQkFDQTt3QkFDRS9CLElBQUksUUFBbUIsT0FBWCtCLEtBQUtDLEdBQUcsSUFBRzt3QkFDdkJjLEtBQUssVUFBbUVDLE9BQXpEQSxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxHQUFHQyxXQUFXLElBQUcsS0FBeUQsT0FBdERKLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLEdBQUdDLFdBQVc7d0JBQzNINUMsTUFBTTt3QkFDTjZDLG1CQUFtQjt3QkFDbkJDLFdBQVcsSUFBSXRCO29CQUNqQjtvQkFDQTt3QkFDRS9CLElBQUksUUFBbUIsT0FBWCtCLEtBQUtDLEdBQUcsSUFBRzt3QkFDdkJjLEtBQUssVUFBbUVDLE9BQXpEQSxLQUFLQyxNQUFNLEdBQUdDLFFBQVEsQ0FBQyxJQUFJQyxNQUFNLENBQUMsR0FBRyxHQUFHQyxXQUFXLElBQUcsS0FBeUQsT0FBdERKLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLEdBQUdDLFdBQVc7d0JBQzNINUMsTUFBTTt3QkFDTjZDLG1CQUFtQjt3QkFDbkJDLFdBQVcsSUFBSXRCO29CQUNqQjtpQkFDRDtnQkFFRGhDLElBQUlHLFlBQVksR0FBRzt1QkFBS0gsSUFBSUcsWUFBWSxJQUFJLEVBQUU7dUJBQU0yQztpQkFBWTtnQkFDaEU5RCxlQUFldUUsTUFBTSxDQUFDcEMsV0FBVzFCO2dCQUVqQ2lELFFBQVFDLEdBQUcsQ0FBQyxXQUFpRXJCLE9BQXREd0IsWUFBWTFDLE1BQU0sRUFBQyxxQ0FBNkMsT0FBVmtCO1lBQy9FO1FBQ0Y7SUFDRjtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFla0IsMEJBQ3BCZ0IsT0FBZSxFQUNmckMsU0FBaUIsRUFDakJHLFNBQWlCLEVBQ2pCYixRQUFnQjtJQUVoQixNQUFNZ0QsZ0JBQStCLEVBQUU7SUFFdkMsSUFBSTtRQUNGLElBQUssSUFBSUMsSUFBSSxHQUFHQSxJQUFJakQsVUFBVWlELElBQUs7WUFDakMsTUFBTW5ELE9BQU8sTUFBTXRCLGtFQUFpQkEsQ0FBQ2tDLFdBQVdHLFdBQVdrQztZQUMzRCxJQUFJakQsTUFBTTtnQkFDUmtELGNBQWNFLElBQUksQ0FBQ3BEO1lBQ3JCLE9BQU87Z0JBQ0wsTUFBTSxJQUFJWCxNQUFNLGlDQUE2Q2EsT0FBWmlELElBQUksR0FBRSxRQUFlLE9BQVRqRDtZQUMvRDtRQUNGO1FBRUEscUJBQXFCO1FBQ3JCLElBQUlnRCxjQUFjckQsTUFBTSxHQUFHLEdBQUc7WUFDNUJyQixtRUFBWUEsQ0FBQzZFLGVBQWUsQ0FBQ0osU0FBU0M7WUFFdEMseURBQXlEO1lBQ3pEMUUsbUVBQVlBLENBQUM4RSxZQUFZLENBQUNMLFNBQVMsYUFBYSxHQUF3QixPQUFyQkMsY0FBY3JELE1BQU0sRUFBQztRQUMxRTtRQUVBLE9BQU9xRDtJQUVULEVBQUUsT0FBT2IsT0FBTztRQUNkRixRQUFRRSxLQUFLLENBQUMsa0NBQWtDQTtRQUVoRCxnQ0FBZ0M7UUFDaEM3RCxtRUFBWUEsQ0FBQzhFLFlBQVksQ0FBQ0wsU0FBUyxVQUFVLG1DQUF5QyxPQUFOWjtRQUVoRixNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNNLGVBQWVrQjtJQUNwQixNQUFNLElBQUl4RSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBQ2pELE9BQU9SLG1FQUFZQSxDQUFDZ0YsTUFBTTtBQUM1QjtBQUVBOztDQUVDLEdBQ00sZUFBZUMsYUFBYS9ELEVBQVU7SUFDM0MsTUFBTSxJQUFJWCxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBQ2pELE9BQU9SLG1FQUFZQSxDQUFDVyxPQUFPLENBQUNPO0FBQzlCO0FBRUE7O0NBRUMsR0FDTSxlQUFlZ0UsZ0JBQWdCQyxTQUFpQjtJQUNyRCxNQUFNLElBQUk1RSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBQ2pELE9BQU9SLG1FQUFZQSxDQUFDb0YsU0FBUyxDQUFDRDtBQUNoQztBQUVBOztDQUVDLEdBQ00sZUFBZUUsa0JBQWtCekMsTUFBYztJQUNwRCxNQUFNLElBQUlyQyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBQ2pELE9BQU9SLG1FQUFZQSxDQUFDc0YsV0FBVyxDQUFDMUM7QUFDbEM7QUFFQTs7Q0FFQyxHQUNNLGVBQWUyQyxrQkFDcEJkLE9BQWUsRUFDZjdCLE1BQWMsRUFDZDRDLEtBQWM7SUFFZCxNQUFNLElBQUlqRixRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBRWpELElBQUk7UUFDRixNQUFNaUYsZUFBZXpGLG1FQUFZQSxDQUFDOEUsWUFBWSxDQUFDTCxTQUFTN0IsUUFBUTRDO1FBQ2hFLElBQUlDLGNBQWM7WUFDaEI5QixRQUFRQyxHQUFHLENBQUMsbUJBQXlDaEIsT0FBdEI2QixTQUFRLGdCQUFxQixPQUFQN0I7WUFFckQsMENBQTBDO1lBQzFDLE1BQU04QyxpQ0FBaUNEO1FBQ3pDO1FBQ0EsT0FBT0E7SUFDVCxFQUFFLE9BQU81QixPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFlOEI7SUFLcEIsTUFBTUMsZ0JBQWdCLE1BQU1QLGtCQUFrQjtJQUM5QyxJQUFJUSxZQUFZO0lBQ2hCLElBQUlDLFNBQVM7SUFDYixNQUFNQyxTQUFtQixFQUFFO0lBRTNCLEtBQUssTUFBTUMsU0FBU0osY0FBZTtRQUNqQyxJQUFJO1lBQ0Ysc0RBQXNEO1lBQ3RELElBQUlJLE1BQU01RSxZQUFZLENBQUNDLE1BQU0sS0FBSyxLQUFLMkUsTUFBTXpELFNBQVMsRUFBRTtnQkFDdEQsTUFBTWtCLDBCQUEwQnVDLE1BQU05RSxFQUFFLEVBQUU4RSxNQUFNNUQsU0FBUyxFQUFFNEQsTUFBTXpELFNBQVMsRUFBRXlELE1BQU10RSxRQUFRO2dCQUMxRm1FO1lBQ0YsT0FBTztnQkFDTCxtREFBbUQ7Z0JBQ25ELE1BQU1OLGtCQUFrQlMsTUFBTTlFLEVBQUUsRUFBRSxhQUFhO2dCQUMvQzJFO1lBQ0Y7UUFDRixFQUFFLE9BQU9oQyxPQUFPO1lBQ2RpQztZQUNBQyxPQUFPbkIsSUFBSSxDQUFDLFNBQXNCZixPQUFibUMsTUFBTTlFLEVBQUUsRUFBQyxNQUFVLE9BQU4yQztZQUNsQyxNQUFNMEIsa0JBQWtCUyxNQUFNOUUsRUFBRSxFQUFFLFVBQVUsc0JBQTRCLE9BQU4yQztRQUNwRTtJQUNGO0lBRUEsT0FBTztRQUFFZ0M7UUFBV0M7UUFBUUM7SUFBTztBQUNyQztBQUVBOztDQUVDLEdBQ00sZUFBZUU7SUFPcEIsTUFBTUMsU0FBUyxNQUFNbkI7SUFFckIsTUFBTW9CLFFBQVE7UUFDWkMsT0FBT0YsT0FBTzdFLE1BQU07UUFDcEJnRixTQUFTSCxPQUFPM0UsTUFBTSxDQUFDK0UsQ0FBQUEsSUFBS0EsRUFBRTFELE1BQU0sS0FBSyxXQUFXdkIsTUFBTTtRQUMxRGtGLFdBQVdMLE9BQU8zRSxNQUFNLENBQUMrRSxDQUFBQSxJQUFLQSxFQUFFMUQsTUFBTSxLQUFLLGFBQWF2QixNQUFNO1FBQzlEeUUsUUFBUUksT0FBTzNFLE1BQU0sQ0FBQytFLENBQUFBLElBQUtBLEVBQUUxRCxNQUFNLEtBQUssVUFBVXZCLE1BQU07UUFDeERtRixjQUFjTixPQUNYM0UsTUFBTSxDQUFDK0UsQ0FBQUEsSUFBS0EsRUFBRTFELE1BQU0sS0FBSyxhQUN6QjZELE1BQU0sQ0FBQyxDQUFDQyxLQUFLSixJQUFNSSxNQUFNSixFQUFFNUQsVUFBVSxFQUFFO0lBQzVDO0lBRUEsT0FBT3lEO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNNLGVBQWVRLFlBQVlsQyxPQUFlLEVBQUVtQyxNQUFlO0lBQ2hFLE1BQU1aLFFBQVEsTUFBTWYsYUFBYVI7SUFDakMsSUFBSSxDQUFDdUIsU0FBU0EsTUFBTXBELE1BQU0sS0FBSyxXQUFXO1FBQ3hDLE9BQU87SUFDVDtJQUVBLE1BQU1pRSxVQUFVLE1BQU10QixrQkFBa0JkLFNBQVMsYUFBYW1DLFVBQVU7SUFDeEUsT0FBTyxDQUFDLENBQUNDO0FBQ1g7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDLFlBQVlyQyxPQUFlLEVBQUVtQyxNQUFlO0lBQ2hFLE1BQU1aLFFBQVEsTUFBTWYsYUFBYVI7SUFDakMsSUFBSSxDQUFDdUIsT0FBTyxPQUFPO0lBRW5CLElBQUk7UUFDRiw0Q0FBNEM7UUFDNUMsSUFBSUEsTUFBTTVFLFlBQVksQ0FBQ0MsTUFBTSxHQUFHLEdBQUc7WUFDakMsTUFBTVgsVUFBVVQscUVBQWNBLENBQUNVLE9BQU8sQ0FBQ3FGLE1BQU01RCxTQUFTO1lBQ3RELElBQUkxQixTQUFTO2dCQUNYLE1BQU1PLE1BQU1QLFFBQVFLLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDOEMsQ0FBQUEsSUFBS0EsRUFBRTVDLEVBQUUsS0FBSzhFLE1BQU16RCxTQUFTO2dCQUMvRCxJQUFJdEIsT0FBT0EsSUFBSUcsWUFBWSxFQUFFO29CQUMzQixtREFBbUQ7b0JBQ25ENEUsTUFBTTVFLFlBQVksQ0FBQzJGLE9BQU8sQ0FBQ0MsQ0FBQUE7d0JBQ3pCLE1BQU1DLGNBQWNoRyxJQUFJRyxZQUFZLENBQUVKLElBQUksQ0FBQ2tHLENBQUFBLEtBQU1BLEdBQUdoRyxFQUFFLEtBQUs4RixVQUFVOUYsRUFBRTt3QkFDdkUsSUFBSStGLGFBQWE7NEJBQ2ZBLFlBQVl4RixJQUFJLEdBQUc7NEJBQ25Cd0YsWUFBWTNDLGlCQUFpQixHQUFHOzRCQUNoQzJDLFlBQVlFLE1BQU0sR0FBR3ZGO3dCQUN2QjtvQkFDRjtvQkFFQSw0QkFBNEI7b0JBQzVCM0IscUVBQWNBLENBQUN1RSxNQUFNLENBQUN3QixNQUFNNUQsU0FBUyxFQUFFMUI7Z0JBQ3pDO1lBQ0Y7UUFDRjtRQUVBLHNCQUFzQjtRQUN0QixNQUFNNkUsa0JBQWtCZCxTQUFTLFlBQVltQyxVQUFVO1FBRXZELE9BQU87SUFDVCxFQUFFLE9BQU8vQyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDRCxlQUFlSCxpQ0FBaUNzQyxLQUFZO0lBQzFELElBQUk7UUFDRixNQUFNb0IsY0FBZ0Q7WUFDcERDLFFBQVFyQixNQUFNMUYsV0FBVyxDQUFDMkIsS0FBSztZQUMvQnFGLFVBQVUsVUFBa0MsT0FBeEJ0QixNQUFNMUYsV0FBVyxDQUFDMkIsS0FBSztZQUMzQ3NGLE1BQU07WUFDTkMsUUFBUXhCLE1BQU10RCxVQUFVO1lBQ3hCQyxVQUFVcUQsTUFBTXJELFFBQVE7WUFDeEI4RSxhQUFhLGdCQUE2QnpCLE9BQXZCQSxNQUFNM0QsV0FBVyxFQUFDLE9BQXVCLE9BQWxCMkQsTUFBTXhELFdBQVc7WUFDM0RrRixpQkFBaUIxQixNQUFNOUUsRUFBRTtZQUN6QjBCLFFBQVFvRCxNQUFNcEQsTUFBTSxLQUFLLGNBQWMsY0FDL0JvRCxNQUFNcEQsTUFBTSxLQUFLLFlBQVksWUFDN0JvRCxNQUFNcEQsTUFBTSxLQUFLLFdBQVcsV0FBVztZQUMvQzZCLFNBQVN1QixNQUFNOUUsRUFBRTtZQUNqQnlHLG1CQUFtQjNCLE1BQU01RSxZQUFZLElBQUk0RSxNQUFNNUUsWUFBWSxDQUFDQyxNQUFNLEdBQUc7WUFDckV1RyxnQkFBZ0I1QixNQUFNNUUsWUFBWSxJQUFJNEUsTUFBTTVFLFlBQVksQ0FBQ0MsTUFBTSxHQUFHLElBQUk7Z0JBQ3BFdUIsUUFBUW9ELE1BQU1wRCxNQUFNLEtBQUssY0FBYyxVQUFVO2dCQUNqRGlGLFVBQVU3QixNQUFNNUUsWUFBWSxDQUFDMEcsR0FBRyxDQUFDdEcsQ0FBQUEsT0FBUzt3QkFDeENOLElBQUlNLEtBQUtOLEVBQUU7d0JBQ1hxRyxNQUFNO3dCQUNOUSxPQUFPLEdBQXFCLE9BQWxCL0IsTUFBTXhELFdBQVcsRUFBQzt3QkFDNUJ3RixTQUFTeEcsS0FBS3dDLEdBQUc7d0JBQ2pCaUUsY0FBYzt3QkFDZEMsWUFBWTt3QkFDWkMsYUFBYW5DLE1BQU1vQyxTQUFTLElBQUlwQyxNQUFNekIsU0FBUztvQkFDakQ7Z0JBQ0E4RCxnQkFBZ0I7Z0JBQ2hCQyxhQUFhdEMsTUFBTW9DLFNBQVMsSUFBSXBDLE1BQU16QixTQUFTO1lBQ2pELElBQUkzQztZQUNKMkMsV0FBV3lCLE1BQU16QixTQUFTO1lBQzFCNkQsV0FBV3BDLE1BQU1vQyxTQUFTLElBQUlwQyxNQUFNekIsU0FBUztRQUMvQztRQUVBLDJDQUEyQztRQUMzQyxNQUFNZ0Usa0JBQStCO1lBQ25DLEdBQUduQixXQUFXO1lBQ2RsRyxJQUFJLGFBQXNCLE9BQVQ4RSxNQUFNOUUsRUFBRTtZQUN6QnNILE1BQU14QyxNQUFNekIsU0FBUztRQUN2QjtRQUVBLDBDQUEwQztRQUMxQyxNQUFNa0UsdUJBQXVCQyxLQUFLQyxLQUFLLENBQUNDLGFBQWFDLE9BQU8sQ0FBQyx5QkFBeUI7UUFFdEYsMERBQTBEO1FBQzFELE1BQU1DLGdCQUFnQkwscUJBQXFCTSxTQUFTLENBQUMsQ0FBQ0MsSUFBbUJBLEVBQUV2RSxPQUFPLEtBQUt1QixNQUFNOUUsRUFBRTtRQUMvRixJQUFJNEgsaUJBQWlCLEdBQUc7WUFDdEIsOEJBQThCO1lBQzlCTCxvQkFBb0IsQ0FBQ0ssY0FBYyxHQUFHUDtRQUN4QyxPQUFPO1lBQ0wsc0JBQXNCO1lBQ3RCRSxxQkFBcUJRLE9BQU8sQ0FBQ1Y7UUFDL0I7UUFFQUssYUFBYU0sT0FBTyxDQUFDLHNCQUFzQlIsS0FBS1MsU0FBUyxDQUFDVjtRQUUxRCw2REFBNkQ7UUFDN0RXLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLHVCQUF1QjtZQUMxREMsUUFBUTtnQkFBRW5DLGFBQWFtQjtnQkFBaUJ2QztZQUFNO1FBQ2hEO1FBRUFyQyxRQUFRQyxHQUFHLENBQUMsMkNBQW9ELE9BQVRvQyxNQUFNOUUsRUFBRTtJQUNqRSxFQUFFLE9BQU8yQyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxpREFBaURBO0lBQ2pFO0FBQ0Y7QUFFQTs7Q0FFQyxHQUNELGVBQWU2QixpQ0FBaUNNLEtBQVk7SUFDMUQsSUFBSTtRQUNGLE1BQU15Qyx1QkFBdUJDLEtBQUtDLEtBQUssQ0FBQ0MsYUFBYUMsT0FBTyxDQUFDLHlCQUF5QjtRQUV0RixzQ0FBc0M7UUFDdEMsTUFBTVcsbUJBQW1CZixxQkFBcUJNLFNBQVMsQ0FBQyxDQUFDQyxJQUFtQkEsRUFBRXZFLE9BQU8sS0FBS3VCLE1BQU05RSxFQUFFO1FBRWxHLElBQUlzSSxvQkFBb0IsR0FBRztZQUN6QixrQ0FBa0M7WUFDbEMsTUFBTUMscUJBQWtDO2dCQUN0QyxHQUFHaEIsb0JBQW9CLENBQUNlLGlCQUFpQjtnQkFDekM1RyxRQUFRb0QsTUFBTXBELE1BQU0sS0FBSyxjQUFjLGNBQy9Cb0QsTUFBTXBELE1BQU0sS0FBSyxZQUFZLFlBQzdCb0QsTUFBTXBELE1BQU0sS0FBSyxXQUFXLFdBQzVCb0QsTUFBTXBELE1BQU0sS0FBSyxjQUFjLGNBQWM7Z0JBQ3JEK0UsbUJBQW1CM0IsTUFBTTVFLFlBQVksSUFBSTRFLE1BQU01RSxZQUFZLENBQUNDLE1BQU0sR0FBRztnQkFDckV1RyxnQkFBZ0I1QixNQUFNNUUsWUFBWSxJQUFJNEUsTUFBTTVFLFlBQVksQ0FBQ0MsTUFBTSxHQUFHLElBQUk7b0JBQ3BFdUIsUUFBUW9ELE1BQU1wRCxNQUFNLEtBQUssY0FBYyxVQUFVO29CQUNqRGlGLFVBQVU3QixNQUFNNUUsWUFBWSxDQUFDMEcsR0FBRyxDQUFDdEcsQ0FBQUEsT0FBUzs0QkFDeENOLElBQUlNLEtBQUtOLEVBQUU7NEJBQ1hxRyxNQUFNOzRCQUNOUSxPQUFPLEdBQXFCLE9BQWxCL0IsTUFBTXhELFdBQVcsRUFBQzs0QkFDNUJ3RixTQUFTeEcsS0FBS3dDLEdBQUc7NEJBQ2pCaUUsY0FBYzs0QkFDZEMsWUFBWTs0QkFDWkMsYUFBYW5DLE1BQU1vQyxTQUFTLElBQUlwQyxNQUFNekIsU0FBUzt3QkFDakQ7b0JBQ0E4RCxnQkFBZ0I7b0JBQ2hCQyxhQUFhdEMsTUFBTW9DLFNBQVMsSUFBSXBDLE1BQU16QixTQUFTO2dCQUNqRCxJQUFJM0M7Z0JBQ0p3RyxXQUFXcEMsTUFBTW9DLFNBQVMsSUFBSXBDLE1BQU16QixTQUFTO1lBQy9DO1lBRUFrRSxvQkFBb0IsQ0FBQ2UsaUJBQWlCLEdBQUdDO1lBQ3pDYixhQUFhTSxPQUFPLENBQUMsc0JBQXNCUixLQUFLUyxTQUFTLENBQUNWO1lBRTFELHVDQUF1QztZQUN2Q1csT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVksdUJBQXVCO2dCQUMxREMsUUFBUTtvQkFBRW5DLGFBQWFxQztvQkFBb0J6RDtnQkFBTTtZQUNuRDtZQUVBckMsUUFBUUMsR0FBRyxDQUFDLDJDQUFvRCxPQUFUb0MsTUFBTTlFLEVBQUU7UUFDakUsT0FBTztZQUNMLHVDQUF1QztZQUN2QyxNQUFNd0MsaUNBQWlDc0M7UUFDekM7SUFDRixFQUFFLE9BQU9uQyxPQUFPO1FBQ2RGLFFBQVFFLEtBQUssQ0FBQyxpREFBaURBO0lBQ2pFO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxWUy1wcm9qZWN0c1xcdHJ5XFxhbHJheWEtc3RvcmVcXGxpYlxcc2VydmljZXNcXG9yZGVyU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE9yZGVyIFNlcnZpY2VcbiAqIFxuICogSGFuZGxlcyBhbGwgb3JkZXItcmVsYXRlZCBvcGVyYXRpb25zIHVzaW5nIGxvY2FsU3RvcmFnZVxuICovXG5cbmltcG9ydCB7IE9yZGVyLCBQcm9kdWN0Rm9ybURhdGEsIFByb2R1Y3RUZW1wbGF0ZSwgRGlnaXRhbENvZGUsIENoZWNrb3V0VXNlckRldGFpbHMsIFRyYW5zYWN0aW9uIH0gZnJvbSAnQC9saWIvdHlwZXMnXG5pbXBvcnQgeyBPcmRlclN0b3JhZ2UsIFByb2R1Y3RTdG9yYWdlIH0gZnJvbSAnQC9saWIvc3RvcmFnZS9sb2NhbFN0b3JhZ2UnXG5pbXBvcnQgeyBhc3NpZ25EaWdpdGFsQ29kZSwgZ2V0QXZhaWxhYmxlQ29kZXMgfSBmcm9tICcuL3Byb2R1Y3RTZXJ2aWNlJ1xuXG4vKipcbiAqIENyZWF0ZSBvcmRlciBmcm9tIHByb2R1Y3QgZm9ybSBkYXRhXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVPcmRlckZyb21Qcm9kdWN0KFxuICBmb3JtRGF0YTogUHJvZHVjdEZvcm1EYXRhLFxuICB1c2VyRGV0YWlsczogQ2hlY2tvdXRVc2VyRGV0YWlsc1xuKTogUHJvbWlzZTxPcmRlcj4ge1xuICAvLyBTaW11bGF0ZSBBUEkgZGVsYXlcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDMwMCkpXG5cbiAgdHJ5IHtcbiAgICAvLyBWYWxpZGF0ZSB0aGF0IHRoZSBzZWxlY3RlZCBwYWNrYWdlIGlzIHN0aWxsIGF2YWlsYWJsZVxuICAgIGNvbnN0IHByb2R1Y3QgPSBQcm9kdWN0U3RvcmFnZS5nZXRCeUlkKGZvcm1EYXRhLnRlbXBsYXRlSWQpXG4gICAgaWYgKCFwcm9kdWN0KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1Byb2R1Y3Qgbm90IGZvdW5kJylcbiAgICB9XG5cbiAgICBjb25zdCBzZWxlY3RlZFBhY2thZ2UgPSBwcm9kdWN0LnBhY2thZ2VzLmZpbmQocGtnID0+IHBrZy5pZCA9PT0gZm9ybURhdGEuc2VsZWN0ZWRQYWNrYWdlLmlkKVxuICAgIGlmICghc2VsZWN0ZWRQYWNrYWdlIHx8ICFzZWxlY3RlZFBhY2thZ2UuaXNBY3RpdmUpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignU2VsZWN0ZWQgcGFja2FnZSBpcyBubyBsb25nZXIgYXZhaWxhYmxlJylcbiAgICB9XG5cbiAgICAvLyBFbmhhbmNlZCBhdmFpbGFiaWxpdHkgY2hlY2sgZm9yIGRpZmZlcmVudCBwcm9kdWN0IHR5cGVzXG4gICAgaWYgKHNlbGVjdGVkUGFja2FnZS5kaWdpdGFsQ29kZXMgJiYgc2VsZWN0ZWRQYWNrYWdlLmRpZ2l0YWxDb2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICAvLyBEaWdpdGFsIHByb2R1Y3RzIHdpdGggY29kZXMgLSBjaGVjayBjb2RlIGF2YWlsYWJpbGl0eVxuICAgICAgY29uc3QgYXZhaWxhYmxlQ29kZXMgPSBzZWxlY3RlZFBhY2thZ2UuZGlnaXRhbENvZGVzLmZpbHRlcihjb2RlID0+ICFjb2RlLnVzZWQpXG4gICAgICBpZiAoYXZhaWxhYmxlQ29kZXMubGVuZ3RoIDwgZm9ybURhdGEucXVhbnRpdHkpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBPbmx5ICR7YXZhaWxhYmxlQ29kZXMubGVuZ3RofSBjb2RlcyBhdmFpbGFibGUsIGJ1dCAke2Zvcm1EYXRhLnF1YW50aXR5fSByZXF1ZXN0ZWRgKVxuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoc2VsZWN0ZWRQYWNrYWdlLnF1YW50aXR5TGltaXQgIT09IHVuZGVmaW5lZCAmJiBzZWxlY3RlZFBhY2thZ2UucXVhbnRpdHlMaW1pdCAhPT0gbnVsbCkge1xuICAgICAgLy8gUHJvZHVjdHMgd2l0aCBtYW51YWwgcXVhbnRpdHkgbGltaXRzXG4gICAgICBpZiAoc2VsZWN0ZWRQYWNrYWdlLnF1YW50aXR5TGltaXQgPCBmb3JtRGF0YS5xdWFudGl0eSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYE9ubHkgJHtzZWxlY3RlZFBhY2thZ2UucXVhbnRpdHlMaW1pdH0gaXRlbXMgYXZhaWxhYmxlLCBidXQgJHtmb3JtRGF0YS5xdWFudGl0eX0gcmVxdWVzdGVkYClcbiAgICAgIH1cbiAgICB9XG4gICAgLy8gRm9yIHVubGltaXRlZCBkaWdpdGFsIHByb2R1Y3RzL3NlcnZpY2VzIChubyBjb2Rlcywgbm8gbGltaXRzKSwgbm8gYXZhaWxhYmlsaXR5IGNoZWNrIG5lZWRlZFxuXG4gICAgLy8gQ2hlY2sgd2FsbGV0IGJhbGFuY2UgYmVmb3JlIGNyZWF0aW5nIG9yZGVyXG4gICAgY29uc3QgdG90YWxDb3N0ID0gc2VsZWN0ZWRQYWNrYWdlLnByaWNlICogZm9ybURhdGEucXVhbnRpdHlcbiAgICBjb25zdCBoYXNTdWZmaWNpZW50QmFsYW5jZSA9IGF3YWl0IGNoZWNrV2FsbGV0QmFsYW5jZShmb3JtRGF0YS51c2VyRGV0YWlscy5lbWFpbCwgdG90YWxDb3N0KVxuICAgIGlmICghaGFzU3VmZmljaWVudEJhbGFuY2UpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgSW5zdWZmaWNpZW50IHdhbGxldCBiYWxhbmNlLiBSZXF1aXJlZDogJCR7dG90YWxDb3N0LnRvRml4ZWQoMil9YClcbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgdGhlIG9yZGVyXG4gICAgY29uc3Qgb3JkZXJEYXRhOiBPbWl0PE9yZGVyLCAnaWQnIHwgJ2NyZWF0ZWRBdCcgfCAndXBkYXRlZEF0Jz4gPSB7XG4gICAgICBwcm9kdWN0SWQ6IGZvcm1EYXRhLnRlbXBsYXRlSWQsXG4gICAgICBwcm9kdWN0TmFtZTogcHJvZHVjdC5uYW1lLFxuICAgICAgcGFja2FnZUlkOiBmb3JtRGF0YS5zZWxlY3RlZFBhY2thZ2UuaWQsXG4gICAgICBwYWNrYWdlTmFtZTogZm9ybURhdGEuc2VsZWN0ZWRQYWNrYWdlLm5hbWUsXG4gICAgICBxdWFudGl0eTogZm9ybURhdGEucXVhbnRpdHksXG4gICAgICB1bml0UHJpY2U6IGZvcm1EYXRhLnNlbGVjdGVkUGFja2FnZS5wcmljZSxcbiAgICAgIHRvdGFsUHJpY2U6IGZvcm1EYXRhLnRvdGFsUHJpY2UsXG4gICAgICBjdXJyZW5jeTogZm9ybURhdGEuY3VycmVuY3ksXG4gICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgIHVzZXJEZXRhaWxzLFxuICAgICAgY3VzdG9tRmllbGRzOiBmb3JtRGF0YS5jdXN0b21GaWVsZHMsXG4gICAgICBkaWdpdGFsQ29kZXM6IFtdLFxuICAgICAgcHJvY2Vzc2luZ1R5cGU6IHByb2R1Y3QucHJvY2Vzc2luZ1R5cGUsXG4gICAgICBkZWxpdmVyeVR5cGU6IHByb2R1Y3QuZGVsaXZlcnlUeXBlLFxuICAgICAgdGltZWxpbmU6IFtcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBgdGltZWxpbmVfJHtEYXRlLm5vdygpfWAsXG4gICAgICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgIG1lc3NhZ2U6ICdPcmRlciBjcmVhdGVkIGFuZCBhd2FpdGluZyBwcm9jZXNzaW5nJyxcbiAgICAgICAgICBpc1Zpc2libGU6IHRydWVcbiAgICAgICAgfVxuICAgICAgXVxuICAgIH1cblxuICAgIGNvbnN0IG5ld09yZGVyID0gT3JkZXJTdG9yYWdlLmNyZWF0ZShvcmRlckRhdGEpXG5cbiAgICAvLyBBdXRvLWFzc2lnbiBkaWdpdGFsIGNvZGVzIGlmIGl0J3MgYW4gaW5zdGFudCBwcm9jZXNzaW5nIHByb2R1Y3RcbiAgICBpZiAocHJvZHVjdC5wcm9jZXNzaW5nVHlwZSA9PT0gJ2luc3RhbnQnICYmIHNlbGVjdGVkUGFja2FnZS5kaWdpdGFsQ29kZXMpIHtcbiAgICAgIC8vIEVuc3VyZSB0aGUgcGFja2FnZSBoYXMgYXZhaWxhYmxlIGRpZ2l0YWwgY29kZXNcbiAgICAgIGF3YWl0IGVuc3VyZURpZ2l0YWxDb2Rlc0F2YWlsYWJsZShmb3JtRGF0YS50ZW1wbGF0ZUlkLCBmb3JtRGF0YS5zZWxlY3RlZFBhY2thZ2UuaWQpXG4gICAgICBhd2FpdCBhc3NpZ25EaWdpdGFsQ29kZXNUb09yZGVyKG5ld09yZGVyLmlkLCBmb3JtRGF0YS50ZW1wbGF0ZUlkLCBmb3JtRGF0YS5zZWxlY3RlZFBhY2thZ2UuaWQsIGZvcm1EYXRhLnF1YW50aXR5KVxuICAgIH1cblxuICAgIC8vIENyZWF0ZSBjb3JyZXNwb25kaW5nIHdhbGxldCB0cmFuc2FjdGlvbiBmb3IgdW5pZmllZCBkaXNwbGF5XG4gICAgYXdhaXQgY3JlYXRlV2FsbGV0VHJhbnNhY3Rpb25Gcm9tT3JkZXIobmV3T3JkZXIpXG5cbiAgICBjb25zb2xlLmxvZyhg4pyFIENyZWF0ZWQgb3JkZXI6ICR7bmV3T3JkZXIuaWR9IGZvciBwcm9kdWN0OiAke3Byb2R1Y3QubmFtZX1gKVxuICAgIHJldHVybiBuZXdPcmRlclxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgb3JkZXI6JywgZXJyb3IpXG4gICAgdGhyb3cgZXJyb3JcbiAgfVxufVxuXG4vKipcbiAqIEVuc3VyZSBkaWdpdGFsIGNvZGVzIGFyZSBhdmFpbGFibGUgZm9yIGEgcGFja2FnZVxuICovXG5hc3luYyBmdW5jdGlvbiBlbnN1cmVEaWdpdGFsQ29kZXNBdmFpbGFibGUocHJvZHVjdElkOiBzdHJpbmcsIHBhY2thZ2VJZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gIGNvbnN0IGF2YWlsYWJsZUNvZGVzID0gYXdhaXQgZ2V0QXZhaWxhYmxlQ29kZXMocHJvZHVjdElkLCBwYWNrYWdlSWQpXG5cbiAgaWYgKGF2YWlsYWJsZUNvZGVzLmxlbmd0aCA9PT0gMCkge1xuICAgIGNvbnNvbGUubG9nKGDimqDvuI8gTm8gZGlnaXRhbCBjb2RlcyBhdmFpbGFibGUgZm9yIHBhY2thZ2UgJHtwYWNrYWdlSWR9LCBhZGRpbmcgc2FtcGxlIGNvZGVzLi4uYClcblxuICAgIC8vIEFkZCBzYW1wbGUgZGlnaXRhbCBjb2RlcyB0byB0aGUgcGFja2FnZVxuICAgIGNvbnN0IHsgUHJvZHVjdFN0b3JhZ2UgfSA9IGF3YWl0IGltcG9ydCgnQC9saWIvc3RvcmFnZS9sb2NhbFN0b3JhZ2UnKVxuICAgIGNvbnN0IHByb2R1Y3QgPSBQcm9kdWN0U3RvcmFnZS5nZXRCeUlkKHByb2R1Y3RJZClcblxuICAgIGlmIChwcm9kdWN0KSB7XG4gICAgICBjb25zdCBwa2cgPSBwcm9kdWN0LnBhY2thZ2VzLmZpbmQocCA9PiBwLmlkID09PSBwYWNrYWdlSWQpXG4gICAgICBpZiAocGtnKSB7XG4gICAgICAgIC8vIEFkZCBzYW1wbGUgZGlnaXRhbCBjb2Rlc1xuICAgICAgICBjb25zdCBzYW1wbGVDb2RlcyA9IFtcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogYGNvZGVfJHtEYXRlLm5vdygpfV8xYCxcbiAgICAgICAgICAgIGtleTogYFNBTVBMRS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfWAsXG4gICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgIGFzc2lnbmVkVG9PcmRlcklkOiBudWxsLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogYGNvZGVfJHtEYXRlLm5vdygpfV8yYCxcbiAgICAgICAgICAgIGtleTogYFNBTVBMRS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfWAsXG4gICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgIGFzc2lnbmVkVG9PcmRlcklkOiBudWxsLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgfSxcbiAgICAgICAgICB7XG4gICAgICAgICAgICBpZDogYGNvZGVfJHtEYXRlLm5vdygpfV8zYCxcbiAgICAgICAgICAgIGtleTogYFNBTVBMRS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfS0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA0KS50b1VwcGVyQ2FzZSgpfWAsXG4gICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgIGFzc2lnbmVkVG9PcmRlcklkOiBudWxsLFxuICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgfVxuICAgICAgICBdXG5cbiAgICAgICAgcGtnLmRpZ2l0YWxDb2RlcyA9IFsuLi4ocGtnLmRpZ2l0YWxDb2RlcyB8fCBbXSksIC4uLnNhbXBsZUNvZGVzXVxuICAgICAgICBQcm9kdWN0U3RvcmFnZS51cGRhdGUocHJvZHVjdElkLCBwcm9kdWN0KVxuXG4gICAgICAgIGNvbnNvbGUubG9nKGDinIUgQWRkZWQgJHtzYW1wbGVDb2Rlcy5sZW5ndGh9IHNhbXBsZSBkaWdpdGFsIGNvZGVzIHRvIHBhY2thZ2UgJHtwYWNrYWdlSWR9YClcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyoqXG4gKiBBc3NpZ24gZGlnaXRhbCBjb2RlcyB0byBhbiBvcmRlclxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gYXNzaWduRGlnaXRhbENvZGVzVG9PcmRlcihcbiAgb3JkZXJJZDogc3RyaW5nLFxuICBwcm9kdWN0SWQ6IHN0cmluZyxcbiAgcGFja2FnZUlkOiBzdHJpbmcsXG4gIHF1YW50aXR5OiBudW1iZXJcbik6IFByb21pc2U8RGlnaXRhbENvZGVbXT4ge1xuICBjb25zdCBhc3NpZ25lZENvZGVzOiBEaWdpdGFsQ29kZVtdID0gW11cblxuICB0cnkge1xuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgcXVhbnRpdHk7IGkrKykge1xuICAgICAgY29uc3QgY29kZSA9IGF3YWl0IGFzc2lnbkRpZ2l0YWxDb2RlKHByb2R1Y3RJZCwgcGFja2FnZUlkLCBvcmRlcklkKVxuICAgICAgaWYgKGNvZGUpIHtcbiAgICAgICAgYXNzaWduZWRDb2Rlcy5wdXNoKGNvZGUpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBhc3NpZ24gZGlnaXRhbCBjb2RlICR7aSArIDF9IG9mICR7cXVhbnRpdHl9YClcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBBZGQgY29kZXMgdG8gb3JkZXJcbiAgICBpZiAoYXNzaWduZWRDb2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICBPcmRlclN0b3JhZ2UuYWRkRGlnaXRhbENvZGVzKG9yZGVySWQsIGFzc2lnbmVkQ29kZXMpXG4gICAgICBcbiAgICAgIC8vIFVwZGF0ZSBvcmRlciBzdGF0dXMgdG8gY29tcGxldGVkIGlmIGFsbCBjb2RlcyBhc3NpZ25lZFxuICAgICAgT3JkZXJTdG9yYWdlLnVwZGF0ZVN0YXR1cyhvcmRlcklkLCAnY29tcGxldGVkJywgYCR7YXNzaWduZWRDb2Rlcy5sZW5ndGh9IGRpZ2l0YWwgY29kZXMgYXNzaWduZWRgKVxuICAgIH1cblxuICAgIHJldHVybiBhc3NpZ25lZENvZGVzXG5cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBhc3NpZ25pbmcgZGlnaXRhbCBjb2RlczonLCBlcnJvcilcbiAgICBcbiAgICAvLyBVcGRhdGUgb3JkZXIgc3RhdHVzIHRvIGZhaWxlZFxuICAgIE9yZGVyU3RvcmFnZS51cGRhdGVTdGF0dXMob3JkZXJJZCwgJ2ZhaWxlZCcsIGBGYWlsZWQgdG8gYXNzaWduIGRpZ2l0YWwgY29kZXM6ICR7ZXJyb3J9YClcbiAgICBcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8qKlxuICogR2V0IGFsbCBvcmRlcnNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldE9yZGVycygpOiBQcm9taXNlPE9yZGVyW10+IHtcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpXG4gIHJldHVybiBPcmRlclN0b3JhZ2UuZ2V0QWxsKClcbn1cblxuLyoqXG4gKiBHZXQgb3JkZXIgYnkgSURcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldE9yZGVyQnlJZChpZDogc3RyaW5nKTogUHJvbWlzZTxPcmRlciB8IG51bGw+IHtcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwKSlcbiAgcmV0dXJuIE9yZGVyU3RvcmFnZS5nZXRCeUlkKGlkKVxufVxuXG4vKipcbiAqIEdldCBvcmRlcnMgYnkgdXNlciBlbWFpbFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0T3JkZXJzQnlVc2VyKHVzZXJFbWFpbDogc3RyaW5nKTogUHJvbWlzZTxPcmRlcltdPiB7XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuICByZXR1cm4gT3JkZXJTdG9yYWdlLmdldEJ5VXNlcih1c2VyRW1haWwpXG59XG5cbi8qKlxuICogR2V0IG9yZGVycyBieSBzdGF0dXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldE9yZGVyc0J5U3RhdHVzKHN0YXR1czogc3RyaW5nKTogUHJvbWlzZTxPcmRlcltdPiB7XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuICByZXR1cm4gT3JkZXJTdG9yYWdlLmdldEJ5U3RhdHVzKHN0YXR1cylcbn1cblxuLyoqXG4gKiBVcGRhdGUgb3JkZXIgc3RhdHVzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiB1cGRhdGVPcmRlclN0YXR1cyhcbiAgb3JkZXJJZDogc3RyaW5nLFxuICBzdGF0dXM6IHN0cmluZyxcbiAgbm90ZXM/OiBzdHJpbmdcbik6IFByb21pc2U8T3JkZXIgfCBudWxsPiB7XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuXG4gIHRyeSB7XG4gICAgY29uc3QgdXBkYXRlZE9yZGVyID0gT3JkZXJTdG9yYWdlLnVwZGF0ZVN0YXR1cyhvcmRlcklkLCBzdGF0dXMsIG5vdGVzKVxuICAgIGlmICh1cGRhdGVkT3JkZXIpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDinIUgVXBkYXRlZCBvcmRlciAke29yZGVySWR9IHN0YXR1cyB0bzogJHtzdGF0dXN9YClcblxuICAgICAgLy8gVXBkYXRlIGNvcnJlc3BvbmRpbmcgd2FsbGV0IHRyYW5zYWN0aW9uXG4gICAgICBhd2FpdCB1cGRhdGVXYWxsZXRUcmFuc2FjdGlvbkZyb21PcmRlcih1cGRhdGVkT3JkZXIpXG4gICAgfVxuICAgIHJldHVybiB1cGRhdGVkT3JkZXJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBvcmRlciBzdGF0dXM6JywgZXJyb3IpXG4gICAgcmV0dXJuIG51bGxcbiAgfVxufVxuXG4vKipcbiAqIFByb2Nlc3MgcGVuZGluZyBvcmRlcnMgKGZvciBhZG1pbiB1c2UpXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBwcm9jZXNzUGVuZGluZ09yZGVycygpOiBQcm9taXNlPHtcbiAgcHJvY2Vzc2VkOiBudW1iZXJcbiAgZmFpbGVkOiBudW1iZXJcbiAgZXJyb3JzOiBzdHJpbmdbXVxufT4ge1xuICBjb25zdCBwZW5kaW5nT3JkZXJzID0gYXdhaXQgZ2V0T3JkZXJzQnlTdGF0dXMoJ3BlbmRpbmcnKVxuICBsZXQgcHJvY2Vzc2VkID0gMFxuICBsZXQgZmFpbGVkID0gMFxuICBjb25zdCBlcnJvcnM6IHN0cmluZ1tdID0gW11cblxuICBmb3IgKGNvbnN0IG9yZGVyIG9mIHBlbmRpbmdPcmRlcnMpIHtcbiAgICB0cnkge1xuICAgICAgLy8gVHJ5IHRvIGFzc2lnbiBkaWdpdGFsIGNvZGVzIGlmIG5vdCBhbHJlYWR5IGFzc2lnbmVkXG4gICAgICBpZiAob3JkZXIuZGlnaXRhbENvZGVzLmxlbmd0aCA9PT0gMCAmJiBvcmRlci5wYWNrYWdlSWQpIHtcbiAgICAgICAgYXdhaXQgYXNzaWduRGlnaXRhbENvZGVzVG9PcmRlcihvcmRlci5pZCwgb3JkZXIucHJvZHVjdElkLCBvcmRlci5wYWNrYWdlSWQsIG9yZGVyLnF1YW50aXR5KVxuICAgICAgICBwcm9jZXNzZWQrK1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gSnVzdCBtYXJrIGFzIGNvbXBsZXRlZCBpZiBjb2RlcyBhbHJlYWR5IGFzc2lnbmVkXG4gICAgICAgIGF3YWl0IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVyLmlkLCAnY29tcGxldGVkJywgJ09yZGVyIHByb2Nlc3NlZCBzdWNjZXNzZnVsbHknKVxuICAgICAgICBwcm9jZXNzZWQrK1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBmYWlsZWQrK1xuICAgICAgZXJyb3JzLnB1c2goYE9yZGVyICR7b3JkZXIuaWR9OiAke2Vycm9yfWApXG4gICAgICBhd2FpdCB1cGRhdGVPcmRlclN0YXR1cyhvcmRlci5pZCwgJ2ZhaWxlZCcsIGBQcm9jZXNzaW5nIGZhaWxlZDogJHtlcnJvcn1gKVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiB7IHByb2Nlc3NlZCwgZmFpbGVkLCBlcnJvcnMgfVxufVxuXG4vKipcbiAqIEdldCBvcmRlciBzdGF0aXN0aWNzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRPcmRlclN0YXRzKCk6IFByb21pc2U8e1xuICB0b3RhbDogbnVtYmVyXG4gIHBlbmRpbmc6IG51bWJlclxuICBjb21wbGV0ZWQ6IG51bWJlclxuICBmYWlsZWQ6IG51bWJlclxuICB0b3RhbFJldmVudWU6IG51bWJlclxufT4ge1xuICBjb25zdCBvcmRlcnMgPSBhd2FpdCBnZXRPcmRlcnMoKVxuICBcbiAgY29uc3Qgc3RhdHMgPSB7XG4gICAgdG90YWw6IG9yZGVycy5sZW5ndGgsXG4gICAgcGVuZGluZzogb3JkZXJzLmZpbHRlcihvID0+IG8uc3RhdHVzID09PSAncGVuZGluZycpLmxlbmd0aCxcbiAgICBjb21wbGV0ZWQ6IG9yZGVycy5maWx0ZXIobyA9PiBvLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcpLmxlbmd0aCxcbiAgICBmYWlsZWQ6IG9yZGVycy5maWx0ZXIobyA9PiBvLnN0YXR1cyA9PT0gJ2ZhaWxlZCcpLmxlbmd0aCxcbiAgICB0b3RhbFJldmVudWU6IG9yZGVyc1xuICAgICAgLmZpbHRlcihvID0+IG8uc3RhdHVzID09PSAnY29tcGxldGVkJylcbiAgICAgIC5yZWR1Y2UoKHN1bSwgbykgPT4gc3VtICsgby50b3RhbFByaWNlLCAwKVxuICB9XG5cbiAgcmV0dXJuIHN0YXRzXG59XG5cbi8qKlxuICogQ2FuY2VsIG9yZGVyIChpZiBzdGlsbCBwZW5kaW5nKVxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY2FuY2VsT3JkZXIob3JkZXJJZDogc3RyaW5nLCByZWFzb24/OiBzdHJpbmcpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgY29uc3Qgb3JkZXIgPSBhd2FpdCBnZXRPcmRlckJ5SWQob3JkZXJJZClcbiAgaWYgKCFvcmRlciB8fCBvcmRlci5zdGF0dXMgIT09ICdwZW5kaW5nJykge1xuICAgIHJldHVybiBmYWxzZVxuICB9XG5cbiAgY29uc3QgdXBkYXRlZCA9IGF3YWl0IHVwZGF0ZU9yZGVyU3RhdHVzKG9yZGVySWQsICdjYW5jZWxsZWQnLCByZWFzb24gfHwgJ09yZGVyIGNhbmNlbGxlZCBieSB1c2VyJylcbiAgcmV0dXJuICEhdXBkYXRlZFxufVxuXG4vKipcbiAqIFJlZnVuZCBvcmRlciAocmVsZWFzZSBkaWdpdGFsIGNvZGVzIGJhY2sgdG8gcG9vbClcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHJlZnVuZE9yZGVyKG9yZGVySWQ6IHN0cmluZywgcmVhc29uPzogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIGNvbnN0IG9yZGVyID0gYXdhaXQgZ2V0T3JkZXJCeUlkKG9yZGVySWQpXG4gIGlmICghb3JkZXIpIHJldHVybiBmYWxzZVxuXG4gIHRyeSB7XG4gICAgLy8gUmVsZWFzZSBkaWdpdGFsIGNvZGVzIGJhY2sgdG8gdGhlIHByb2R1Y3RcbiAgICBpZiAob3JkZXIuZGlnaXRhbENvZGVzLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IHByb2R1Y3QgPSBQcm9kdWN0U3RvcmFnZS5nZXRCeUlkKG9yZGVyLnByb2R1Y3RJZClcbiAgICAgIGlmIChwcm9kdWN0KSB7XG4gICAgICAgIGNvbnN0IHBrZyA9IHByb2R1Y3QucGFja2FnZXMuZmluZChwID0+IHAuaWQgPT09IG9yZGVyLnBhY2thZ2VJZClcbiAgICAgICAgaWYgKHBrZyAmJiBwa2cuZGlnaXRhbENvZGVzKSB7XG4gICAgICAgICAgLy8gTWFyayBjb2RlcyBhcyB1bnVzZWQgYW5kIHJlbW92ZSBvcmRlciBhc3NpZ25tZW50XG4gICAgICAgICAgb3JkZXIuZGlnaXRhbENvZGVzLmZvckVhY2gob3JkZXJDb2RlID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHByb2R1Y3RDb2RlID0gcGtnLmRpZ2l0YWxDb2RlcyEuZmluZChwYyA9PiBwYy5pZCA9PT0gb3JkZXJDb2RlLmlkKVxuICAgICAgICAgICAgaWYgKHByb2R1Y3RDb2RlKSB7XG4gICAgICAgICAgICAgIHByb2R1Y3RDb2RlLnVzZWQgPSBmYWxzZVxuICAgICAgICAgICAgICBwcm9kdWN0Q29kZS5hc3NpZ25lZFRvT3JkZXJJZCA9IG51bGxcbiAgICAgICAgICAgICAgcHJvZHVjdENvZGUudXNlZEF0ID0gdW5kZWZpbmVkXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfSlcbiAgICAgICAgICBcbiAgICAgICAgICAvLyBVcGRhdGUgcHJvZHVjdCBpbiBzdG9yYWdlXG4gICAgICAgICAgUHJvZHVjdFN0b3JhZ2UudXBkYXRlKG9yZGVyLnByb2R1Y3RJZCwgcHJvZHVjdClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIFVwZGF0ZSBvcmRlciBzdGF0dXNcbiAgICBhd2FpdCB1cGRhdGVPcmRlclN0YXR1cyhvcmRlcklkLCAncmVmdW5kZWQnLCByZWFzb24gfHwgJ09yZGVyIHJlZnVuZGVkJylcbiAgICBcbiAgICByZXR1cm4gdHJ1ZVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlZnVuZGluZyBvcmRlcjonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vKipcbiAqIENyZWF0ZSBhIHdhbGxldCB0cmFuc2FjdGlvbiBmcm9tIGFuIG9yZGVyIGZvciB1bmlmaWVkIGRpc3BsYXkgaW4gdGhlIHdhbGxldFxuICovXG5hc3luYyBmdW5jdGlvbiBjcmVhdGVXYWxsZXRUcmFuc2FjdGlvbkZyb21PcmRlcihvcmRlcjogT3JkZXIpOiBQcm9taXNlPHZvaWQ+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCB0cmFuc2FjdGlvbjogT21pdDxUcmFuc2FjdGlvbiwgJ2lkJyB8ICdkYXRlJz4gPSB7XG4gICAgICB1c2VySWQ6IG9yZGVyLnVzZXJEZXRhaWxzLmVtYWlsLFxuICAgICAgd2FsbGV0SWQ6IGB3YWxsZXRfJHtvcmRlci51c2VyRGV0YWlscy5lbWFpbH1gLFxuICAgICAgdHlwZTogXCJwdXJjaGFzZVwiLFxuICAgICAgYW1vdW50OiBvcmRlci50b3RhbFByaWNlLFxuICAgICAgY3VycmVuY3k6IG9yZGVyLmN1cnJlbmN5LFxuICAgICAgZGVzY3JpcHRpb246IGDwn5uSICR7b3JkZXIucHJvZHVjdE5hbWV9IC0gJHtvcmRlci5wYWNrYWdlTmFtZX1gLFxuICAgICAgcmVmZXJlbmNlTnVtYmVyOiBvcmRlci5pZCxcbiAgICAgIHN0YXR1czogb3JkZXIuc3RhdHVzID09PSAnY29tcGxldGVkJyA/ICdjb21wbGV0ZWQnIDpcbiAgICAgICAgICAgICAgb3JkZXIuc3RhdHVzID09PSAncGVuZGluZycgPyAncGVuZGluZycgOlxuICAgICAgICAgICAgICBvcmRlci5zdGF0dXMgPT09ICdmYWlsZWQnID8gJ2ZhaWxlZCcgOiAncGVuZGluZycsXG4gICAgICBvcmRlcklkOiBvcmRlci5pZCxcbiAgICAgIGhhc0RpZ2l0YWxDb250ZW50OiBvcmRlci5kaWdpdGFsQ29kZXMgJiYgb3JkZXIuZGlnaXRhbENvZGVzLmxlbmd0aCA+IDAsXG4gICAgICBkaWdpdGFsQ29udGVudDogb3JkZXIuZGlnaXRhbENvZGVzICYmIG9yZGVyLmRpZ2l0YWxDb2Rlcy5sZW5ndGggPiAwID8ge1xuICAgICAgICBzdGF0dXM6IG9yZGVyLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAncmVhZHknIDogJ3BlbmRpbmcnLFxuICAgICAgICBjb250ZW50czogb3JkZXIuZGlnaXRhbENvZGVzLm1hcChjb2RlID0+ICh7XG4gICAgICAgICAgaWQ6IGNvZGUuaWQsXG4gICAgICAgICAgdHlwZTogJ2dhbWVfY29kZScsXG4gICAgICAgICAgdGl0bGU6IGAke29yZGVyLnBhY2thZ2VOYW1lfSAtINmD2YjYryDYsdmC2YXZimAsXG4gICAgICAgICAgY29udGVudDogY29kZS5rZXksXG4gICAgICAgICAgaW5zdHJ1Y3Rpb25zOiBcItin2LPYqtiu2K/ZhSDZh9iw2Kcg2KfZhNmD2YjYryDZgdmKINin2YTYqti32KjZitmCINin2YTZhdit2K/Yr1wiLFxuICAgICAgICAgIGlzUmV2ZWFsZWQ6IGZhbHNlLFxuICAgICAgICAgIGRlbGl2ZXJlZEF0OiBvcmRlci51cGRhdGVkQXQgfHwgb3JkZXIuY3JlYXRlZEF0XG4gICAgICAgIH0pKSxcbiAgICAgICAgZGVsaXZlcnlNZXRob2Q6ICdpbnN0YW50JyxcbiAgICAgICAgbGFzdFVwZGF0ZWQ6IG9yZGVyLnVwZGF0ZWRBdCB8fCBvcmRlci5jcmVhdGVkQXRcbiAgICAgIH0gOiB1bmRlZmluZWQsXG4gICAgICBjcmVhdGVkQXQ6IG9yZGVyLmNyZWF0ZWRBdCxcbiAgICAgIHVwZGF0ZWRBdDogb3JkZXIudXBkYXRlZEF0IHx8IG9yZGVyLmNyZWF0ZWRBdFxuICAgIH1cblxuICAgIC8vIENyZWF0ZSBmdWxsIHRyYW5zYWN0aW9uIHdpdGggSUQgYW5kIGRhdGVcbiAgICBjb25zdCBmdWxsVHJhbnNhY3Rpb246IFRyYW5zYWN0aW9uID0ge1xuICAgICAgLi4udHJhbnNhY3Rpb24sXG4gICAgICBpZDogYHR4bl9vcmRlcl8ke29yZGVyLmlkfWAsXG4gICAgICBkYXRlOiBvcmRlci5jcmVhdGVkQXRcbiAgICB9XG5cbiAgICAvLyBTYXZlIHRvIGxvY2FsU3RvcmFnZSBmb3Igd2FsbGV0IGRpc3BsYXlcbiAgICBjb25zdCBleGlzdGluZ1RyYW5zYWN0aW9ucyA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3dhbGxldFRyYW5zYWN0aW9ucycpIHx8ICdbXScpXG5cbiAgICAvLyBDaGVjayBpZiB0cmFuc2FjdGlvbiBhbHJlYWR5IGV4aXN0cyB0byBhdm9pZCBkdXBsaWNhdGVzXG4gICAgY29uc3QgZXhpc3RpbmdJbmRleCA9IGV4aXN0aW5nVHJhbnNhY3Rpb25zLmZpbmRJbmRleCgodDogVHJhbnNhY3Rpb24pID0+IHQub3JkZXJJZCA9PT0gb3JkZXIuaWQpXG4gICAgaWYgKGV4aXN0aW5nSW5kZXggPj0gMCkge1xuICAgICAgLy8gVXBkYXRlIGV4aXN0aW5nIHRyYW5zYWN0aW9uXG4gICAgICBleGlzdGluZ1RyYW5zYWN0aW9uc1tleGlzdGluZ0luZGV4XSA9IGZ1bGxUcmFuc2FjdGlvblxuICAgIH0gZWxzZSB7XG4gICAgICAvLyBBZGQgbmV3IHRyYW5zYWN0aW9uXG4gICAgICBleGlzdGluZ1RyYW5zYWN0aW9ucy51bnNoaWZ0KGZ1bGxUcmFuc2FjdGlvbilcbiAgICB9XG5cbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnd2FsbGV0VHJhbnNhY3Rpb25zJywgSlNPTi5zdHJpbmdpZnkoZXhpc3RpbmdUcmFuc2FjdGlvbnMpKVxuXG4gICAgLy8gRGlzcGF0Y2ggZXZlbnQgdG8gbm90aWZ5IHdhbGxldCBwYWdlIG9mIHRyYW5zYWN0aW9uIHVwZGF0ZVxuICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgndHJhbnNhY3Rpb25zVXBkYXRlZCcsIHtcbiAgICAgIGRldGFpbDogeyB0cmFuc2FjdGlvbjogZnVsbFRyYW5zYWN0aW9uLCBvcmRlciB9XG4gICAgfSkpXG5cbiAgICBjb25zb2xlLmxvZyhg4pyFIENyZWF0ZWQgd2FsbGV0IHRyYW5zYWN0aW9uIGZvciBvcmRlcjogJHtvcmRlci5pZH1gKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHdhbGxldCB0cmFuc2FjdGlvbiBmcm9tIG9yZGVyOicsIGVycm9yKVxuICB9XG59XG5cbi8qKlxuICogVXBkYXRlIHdhbGxldCB0cmFuc2FjdGlvbiB3aGVuIG9yZGVyIHN0YXR1cyBjaGFuZ2VzXG4gKi9cbmFzeW5jIGZ1bmN0aW9uIHVwZGF0ZVdhbGxldFRyYW5zYWN0aW9uRnJvbU9yZGVyKG9yZGVyOiBPcmRlcik6IFByb21pc2U8dm9pZD4ge1xuICB0cnkge1xuICAgIGNvbnN0IGV4aXN0aW5nVHJhbnNhY3Rpb25zID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgnd2FsbGV0VHJhbnNhY3Rpb25zJykgfHwgJ1tdJylcblxuICAgIC8vIEZpbmQgdGhlIHRyYW5zYWN0aW9uIGZvciB0aGlzIG9yZGVyXG4gICAgY29uc3QgdHJhbnNhY3Rpb25JbmRleCA9IGV4aXN0aW5nVHJhbnNhY3Rpb25zLmZpbmRJbmRleCgodDogVHJhbnNhY3Rpb24pID0+IHQub3JkZXJJZCA9PT0gb3JkZXIuaWQpXG5cbiAgICBpZiAodHJhbnNhY3Rpb25JbmRleCA+PSAwKSB7XG4gICAgICAvLyBVcGRhdGUgdGhlIGV4aXN0aW5nIHRyYW5zYWN0aW9uXG4gICAgICBjb25zdCB1cGRhdGVkVHJhbnNhY3Rpb246IFRyYW5zYWN0aW9uID0ge1xuICAgICAgICAuLi5leGlzdGluZ1RyYW5zYWN0aW9uc1t0cmFuc2FjdGlvbkluZGV4XSxcbiAgICAgICAgc3RhdHVzOiBvcmRlci5zdGF0dXMgPT09ICdjb21wbGV0ZWQnID8gJ2NvbXBsZXRlZCcgOlxuICAgICAgICAgICAgICAgIG9yZGVyLnN0YXR1cyA9PT0gJ3BlbmRpbmcnID8gJ3BlbmRpbmcnIDpcbiAgICAgICAgICAgICAgICBvcmRlci5zdGF0dXMgPT09ICdmYWlsZWQnID8gJ2ZhaWxlZCcgOlxuICAgICAgICAgICAgICAgIG9yZGVyLnN0YXR1cyA9PT0gJ2NhbmNlbGxlZCcgPyAnY2FuY2VsbGVkJyA6ICdwZW5kaW5nJyxcbiAgICAgICAgaGFzRGlnaXRhbENvbnRlbnQ6IG9yZGVyLmRpZ2l0YWxDb2RlcyAmJiBvcmRlci5kaWdpdGFsQ29kZXMubGVuZ3RoID4gMCxcbiAgICAgICAgZGlnaXRhbENvbnRlbnQ6IG9yZGVyLmRpZ2l0YWxDb2RlcyAmJiBvcmRlci5kaWdpdGFsQ29kZXMubGVuZ3RoID4gMCA/IHtcbiAgICAgICAgICBzdGF0dXM6IG9yZGVyLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgPyAncmVhZHknIDogJ3BlbmRpbmcnLFxuICAgICAgICAgIGNvbnRlbnRzOiBvcmRlci5kaWdpdGFsQ29kZXMubWFwKGNvZGUgPT4gKHtcbiAgICAgICAgICAgIGlkOiBjb2RlLmlkLFxuICAgICAgICAgICAgdHlwZTogJ2dhbWVfY29kZScsXG4gICAgICAgICAgICB0aXRsZTogYCR7b3JkZXIucGFja2FnZU5hbWV9IC0g2YPZiNivINix2YLZhdmKYCxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGNvZGUua2V5LFxuICAgICAgICAgICAgaW5zdHJ1Y3Rpb25zOiBcItin2LPYqtiu2K/ZhSDZh9iw2Kcg2KfZhNmD2YjYryDZgdmKINin2YTYqti32KjZitmCINin2YTZhdit2K/Yr1wiLFxuICAgICAgICAgICAgaXNSZXZlYWxlZDogZmFsc2UsXG4gICAgICAgICAgICBkZWxpdmVyZWRBdDogb3JkZXIudXBkYXRlZEF0IHx8IG9yZGVyLmNyZWF0ZWRBdFxuICAgICAgICAgIH0pKSxcbiAgICAgICAgICBkZWxpdmVyeU1ldGhvZDogJ2luc3RhbnQnLFxuICAgICAgICAgIGxhc3RVcGRhdGVkOiBvcmRlci51cGRhdGVkQXQgfHwgb3JkZXIuY3JlYXRlZEF0XG4gICAgICAgIH0gOiB1bmRlZmluZWQsXG4gICAgICAgIHVwZGF0ZWRBdDogb3JkZXIudXBkYXRlZEF0IHx8IG9yZGVyLmNyZWF0ZWRBdFxuICAgICAgfVxuXG4gICAgICBleGlzdGluZ1RyYW5zYWN0aW9uc1t0cmFuc2FjdGlvbkluZGV4XSA9IHVwZGF0ZWRUcmFuc2FjdGlvblxuICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3dhbGxldFRyYW5zYWN0aW9ucycsIEpTT04uc3RyaW5naWZ5KGV4aXN0aW5nVHJhbnNhY3Rpb25zKSlcblxuICAgICAgLy8gRGlzcGF0Y2ggZXZlbnQgdG8gbm90aWZ5IHdhbGxldCBwYWdlXG4gICAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ3RyYW5zYWN0aW9uc1VwZGF0ZWQnLCB7XG4gICAgICAgIGRldGFpbDogeyB0cmFuc2FjdGlvbjogdXBkYXRlZFRyYW5zYWN0aW9uLCBvcmRlciB9XG4gICAgICB9KSlcblxuICAgICAgY29uc29sZS5sb2coYOKchSBVcGRhdGVkIHdhbGxldCB0cmFuc2FjdGlvbiBmb3Igb3JkZXI6ICR7b3JkZXIuaWR9YClcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gVHJhbnNhY3Rpb24gZG9lc24ndCBleGlzdCwgY3JlYXRlIGl0XG4gICAgICBhd2FpdCBjcmVhdGVXYWxsZXRUcmFuc2FjdGlvbkZyb21PcmRlcihvcmRlcilcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgd2FsbGV0IHRyYW5zYWN0aW9uIGZyb20gb3JkZXI6JywgZXJyb3IpXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJPcmRlclN0b3JhZ2UiLCJQcm9kdWN0U3RvcmFnZSIsImFzc2lnbkRpZ2l0YWxDb2RlIiwiZ2V0QXZhaWxhYmxlQ29kZXMiLCJjcmVhdGVPcmRlckZyb21Qcm9kdWN0IiwiZm9ybURhdGEiLCJ1c2VyRGV0YWlscyIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInByb2R1Y3QiLCJnZXRCeUlkIiwidGVtcGxhdGVJZCIsIkVycm9yIiwic2VsZWN0ZWRQYWNrYWdlIiwicGFja2FnZXMiLCJmaW5kIiwicGtnIiwiaWQiLCJpc0FjdGl2ZSIsImRpZ2l0YWxDb2RlcyIsImxlbmd0aCIsImF2YWlsYWJsZUNvZGVzIiwiZmlsdGVyIiwiY29kZSIsInVzZWQiLCJxdWFudGl0eSIsInF1YW50aXR5TGltaXQiLCJ1bmRlZmluZWQiLCJ0b3RhbENvc3QiLCJwcmljZSIsImhhc1N1ZmZpY2llbnRCYWxhbmNlIiwiY2hlY2tXYWxsZXRCYWxhbmNlIiwiZW1haWwiLCJ0b0ZpeGVkIiwib3JkZXJEYXRhIiwicHJvZHVjdElkIiwicHJvZHVjdE5hbWUiLCJuYW1lIiwicGFja2FnZUlkIiwicGFja2FnZU5hbWUiLCJ1bml0UHJpY2UiLCJ0b3RhbFByaWNlIiwiY3VycmVuY3kiLCJzdGF0dXMiLCJjdXN0b21GaWVsZHMiLCJwcm9jZXNzaW5nVHlwZSIsImRlbGl2ZXJ5VHlwZSIsInRpbWVsaW5lIiwiRGF0ZSIsIm5vdyIsInRpbWVzdGFtcCIsIm1lc3NhZ2UiLCJpc1Zpc2libGUiLCJuZXdPcmRlciIsImNyZWF0ZSIsImVuc3VyZURpZ2l0YWxDb2Rlc0F2YWlsYWJsZSIsImFzc2lnbkRpZ2l0YWxDb2Rlc1RvT3JkZXIiLCJjcmVhdGVXYWxsZXRUcmFuc2FjdGlvbkZyb21PcmRlciIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsInAiLCJzYW1wbGVDb2RlcyIsImtleSIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsInRvVXBwZXJDYXNlIiwiYXNzaWduZWRUb09yZGVySWQiLCJjcmVhdGVkQXQiLCJ1cGRhdGUiLCJvcmRlcklkIiwiYXNzaWduZWRDb2RlcyIsImkiLCJwdXNoIiwiYWRkRGlnaXRhbENvZGVzIiwidXBkYXRlU3RhdHVzIiwiZ2V0T3JkZXJzIiwiZ2V0QWxsIiwiZ2V0T3JkZXJCeUlkIiwiZ2V0T3JkZXJzQnlVc2VyIiwidXNlckVtYWlsIiwiZ2V0QnlVc2VyIiwiZ2V0T3JkZXJzQnlTdGF0dXMiLCJnZXRCeVN0YXR1cyIsInVwZGF0ZU9yZGVyU3RhdHVzIiwibm90ZXMiLCJ1cGRhdGVkT3JkZXIiLCJ1cGRhdGVXYWxsZXRUcmFuc2FjdGlvbkZyb21PcmRlciIsInByb2Nlc3NQZW5kaW5nT3JkZXJzIiwicGVuZGluZ09yZGVycyIsInByb2Nlc3NlZCIsImZhaWxlZCIsImVycm9ycyIsIm9yZGVyIiwiZ2V0T3JkZXJTdGF0cyIsIm9yZGVycyIsInN0YXRzIiwidG90YWwiLCJwZW5kaW5nIiwibyIsImNvbXBsZXRlZCIsInRvdGFsUmV2ZW51ZSIsInJlZHVjZSIsInN1bSIsImNhbmNlbE9yZGVyIiwicmVhc29uIiwidXBkYXRlZCIsInJlZnVuZE9yZGVyIiwiZm9yRWFjaCIsIm9yZGVyQ29kZSIsInByb2R1Y3RDb2RlIiwicGMiLCJ1c2VkQXQiLCJ0cmFuc2FjdGlvbiIsInVzZXJJZCIsIndhbGxldElkIiwidHlwZSIsImFtb3VudCIsImRlc2NyaXB0aW9uIiwicmVmZXJlbmNlTnVtYmVyIiwiaGFzRGlnaXRhbENvbnRlbnQiLCJkaWdpdGFsQ29udGVudCIsImNvbnRlbnRzIiwibWFwIiwidGl0bGUiLCJjb250ZW50IiwiaW5zdHJ1Y3Rpb25zIiwiaXNSZXZlYWxlZCIsImRlbGl2ZXJlZEF0IiwidXBkYXRlZEF0IiwiZGVsaXZlcnlNZXRob2QiLCJsYXN0VXBkYXRlZCIsImZ1bGxUcmFuc2FjdGlvbiIsImRhdGUiLCJleGlzdGluZ1RyYW5zYWN0aW9ucyIsIkpTT04iLCJwYXJzZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJleGlzdGluZ0luZGV4IiwiZmluZEluZGV4IiwidCIsInVuc2hpZnQiLCJzZXRJdGVtIiwic3RyaW5naWZ5Iiwid2luZG93IiwiZGlzcGF0Y2hFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwidHJhbnNhY3Rpb25JbmRleCIsInVwZGF0ZWRUcmFuc2FjdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/orderService.ts\n"));

/***/ })

});