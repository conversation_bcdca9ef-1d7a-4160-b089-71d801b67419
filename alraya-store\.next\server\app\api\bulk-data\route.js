/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/bulk-data/route";
exports.ids = ["app/api/bulk-data/route"];
exports.modules = {

/***/ "(rsc)/./app/api/bulk-data/route.ts":
/*!************************************!*\
  !*** ./app/api/bulk-data/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_data_gameCards__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/gameCards */ \"(rsc)/./lib/data/gameCards.ts\");\n/* harmony import */ var _lib_data_slides__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data/slides */ \"(rsc)/./lib/data/slides.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(rsc)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_data_currencies__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/data/currencies */ \"(rsc)/./lib/data/currencies.ts\");\n\n// Import existing data sources\n\n\n\n\n/**\n * GET /api/bulk-data\n * \n * Single endpoint that fetches ALL application data in one request.\n * This eliminates the need for multiple API calls and enables true offline functionality.\n * \n * Returns:\n * - Products (all active products with packages and fields)\n * - Currencies (available currencies with display info)\n * - Exchange rates (current rates for all currency pairs)\n * - Game cards (static game card data)\n * - Slides (promotional slides)\n * - User preferences (if user is authenticated)\n * - Wallet data (if user is authenticated)\n * - Contact info and other static content\n */ async function GET(request) {\n    try {\n        console.log('🚀 Loading bulk application data...');\n        // Get user ID from request if authenticated (optional)\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get('userId');\n        // Parallel data fetching for optimal performance\n        const [products, currencies, exchangeRates, walletData, userPreferences] = await Promise.all([\n            // Load products\n            loadProducts(),\n            // Load currencies\n            loadCurrencies(),\n            // Load exchange rates\n            loadExchangeRates(),\n            // Load wallet data (if user authenticated)\n            userId ? loadWalletData(userId) : Promise.resolve(undefined),\n            // Load user preferences (if user authenticated)\n            userId ? loadUserPreferences(userId) : Promise.resolve(undefined)\n        ]);\n        // Combine all data\n        const bulkData = {\n            products,\n            currencies,\n            exchangeRates,\n            gameCards: _lib_data_gameCards__WEBPACK_IMPORTED_MODULE_1__.gameCards,\n            slides: _lib_data_slides__WEBPACK_IMPORTED_MODULE_2__.slides,\n            walletData,\n            userPreferences\n        };\n        const response = {\n            success: true,\n            data: bulkData,\n            timestamp: new Date(),\n            version: '1.0.0'\n        };\n        console.log('✅ Bulk data loaded successfully:', {\n            products: products.length,\n            currencies: currencies.length,\n            gameCards: _lib_data_gameCards__WEBPACK_IMPORTED_MODULE_1__.gameCards.length,\n            slides: _lib_data_slides__WEBPACK_IMPORTED_MODULE_2__.slides.length,\n            hasWalletData: !!walletData,\n            hasUserPreferences: !!userPreferences\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('❌ Failed to load bulk data:', error);\n        const errorResponse = {\n            success: false,\n            data: {},\n            timestamp: new Date(),\n            error: error instanceof Error ? error.message : 'Unknown error occurred'\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(errorResponse, {\n            status: 500\n        });\n    }\n}\n/**\n * Load all active products with their packages and fields\n */ async function loadProducts() {\n    try {\n        const products = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.getProducts)({\n            isActive: true\n        });\n        return products;\n    } catch (error) {\n        console.error('Failed to load products:', error);\n        return [];\n    }\n}\n/**\n * Load available currencies\n */ async function loadCurrencies() {\n    try {\n        // Convert CURRENCIES object to array format expected by the app\n        const currencyArray = Object.entries(_lib_data_currencies__WEBPACK_IMPORTED_MODULE_4__.CURRENCIES).map(([code, info])=>({\n                code,\n                name: info.name,\n                symbol: info.symbol,\n                isRTL: info.isRTL\n            }));\n        return currencyArray;\n    } catch (error) {\n        console.error('Failed to load currencies:', error);\n        return [];\n    }\n}\n/**\n * Load current exchange rates\n */ async function loadExchangeRates() {\n    try {\n        // Mock exchange rates - in production this would fetch from your exchange rate service\n        const mockRates = {\n            'USD': 1,\n            'SDG': 450.00,\n            'EGP': 30.80\n        };\n        return mockRates;\n    } catch (error) {\n        console.error('Failed to load exchange rates:', error);\n        return {\n            'USD': 1\n        } // Fallback to USD base\n        ;\n    }\n}\n/**\n * Load wallet data for authenticated user\n */ async function loadWalletData(userId) {\n    try {\n        // TODO: Implement actual wallet data loading from Supabase\n        // For now, return undefined to use existing wallet hook behavior\n        return undefined;\n    } catch (error) {\n        console.error('Failed to load wallet data:', error);\n        return undefined;\n    }\n}\n/**\n * Load user preferences for authenticated user\n */ async function loadUserPreferences(userId) {\n    try {\n        // TODO: Implement actual user preferences loading from Supabase\n        // Return default preferences for now\n        return {\n            preferredCurrency: 'USD',\n            theme: 'dark',\n            language: 'ar',\n            notifications: true,\n            autoRefresh: false\n        };\n    } catch (error) {\n        console.error('Failed to load user preferences:', error);\n        return undefined;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/bulk-data/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/data/currencies.ts":
/*!********************************!*\
  !*** ./lib/data/currencies.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENCIES: () => (/* binding */ CURRENCIES),\n/* harmony export */   DEFAULT_CURRENCY: () => (/* binding */ DEFAULT_CURRENCY),\n/* harmony export */   LEGACY_CURRENCIES: () => (/* binding */ LEGACY_CURRENCIES),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getAvailableCurrencies: () => (/* binding */ getAvailableCurrencies),\n/* harmony export */   getCurrencyInfo: () => (/* binding */ getCurrencyInfo),\n/* harmony export */   getEnabledCurrencies: () => (/* binding */ getEnabledCurrencies),\n/* harmony export */   getPrimaryCurrency: () => (/* binding */ getPrimaryCurrency),\n/* harmony export */   isCurrencyConversionEnabled: () => (/* binding */ isCurrencyConversionEnabled),\n/* harmony export */   isMultiCurrencyEnabled: () => (/* binding */ isMultiCurrencyEnabled)\n/* harmony export */ });\n/* harmony import */ var _lib_utils_currency__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/utils/currency */ \"(rsc)/./lib/utils/currency.ts\");\n\n// =====================================================\n// LEGACY CURRENCY SUPPORT (BACKWARD COMPATIBILITY)\n// =====================================================\n// Legacy hardcoded currencies - kept for backward compatibility\nconst LEGACY_CURRENCIES = {\n    SDG: {\n        code: \"SDG\",\n        name: \"الجنيه السوداني\",\n        symbol: \"ج.س.\"\n    },\n    EGP: {\n        code: \"EGP\",\n        name: \"الجنيه المصري\",\n        symbol: \"ج.م.\"\n    }\n};\n// Backward compatibility - will be replaced with database-driven currencies\nconst CURRENCIES = {\n    SDG: {\n        code: \"SDG\",\n        name: \"الجنيه السوداني\",\n        symbol: \"ج.س.\",\n        isRTL: true\n    },\n    EGP: {\n        code: \"EGP\",\n        name: \"الجنيه المصري\",\n        symbol: \"ج.م.\",\n        isRTL: true\n    },\n    USD: {\n        code: \"USD\",\n        name: \"الدولار الأمريكي\",\n        symbol: \"$\",\n        isRTL: false\n    }\n};\nconst DEFAULT_CURRENCY = \"USD\";\n// =====================================================\n// ENHANCED CURRENCY FUNCTIONS\n// =====================================================\n/**\r\n * Format currency amount (enhanced version)\r\n * @deprecated Use formatCurrency from lib/utils/currency.ts instead\r\n */ function formatCurrency(amount, currency) {\n    // Use the new enhanced formatting function\n    return (0,_lib_utils_currency__WEBPACK_IMPORTED_MODULE_0__.formatCurrency)(amount, currency);\n}\n/**\r\n * Get currency info (legacy support)\r\n * @deprecated Use getCurrencyDisplayInfo from lib/utils/currency.ts instead\r\n */ function getCurrencyInfo(currency) {\n    return (0,_lib_utils_currency__WEBPACK_IMPORTED_MODULE_0__.getCurrencyDisplayInfo)(currency);\n}\n/**\r\n * Get available currencies for current client\r\n * TODO: Replace with database lookup in production\r\n */ async function getAvailableCurrencies() {\n    // In production, this would fetch from Supabase\n    return Object.values(CURRENCIES);\n}\n/**\r\n * Get enabled currencies based on client settings\r\n * TODO: Replace with client configuration lookup\r\n */ async function getEnabledCurrencies() {\n    // In production, this would fetch from client_currency_settings table\n    // USD is always first as the default currency\n    return [\n        \"USD\",\n        \"SDG\",\n        \"EGP\"\n    ];\n}\n/**\r\n * Get primary currency for revenue reporting\r\n * TODO: Replace with client configuration lookup\r\n */ async function getPrimaryCurrency() {\n    // In production, this would fetch from client_currency_settings table\n    return \"USD\";\n}\n/**\r\n * Check if multi-currency is enabled\r\n * TODO: Replace with client configuration lookup\r\n */ async function isMultiCurrencyEnabled() {\n    // In production, this would fetch from client_currency_settings table\n    return true;\n}\n/**\r\n * Check if currency conversion is enabled\r\n * TODO: Replace with client configuration lookup\r\n */ async function isCurrencyConversionEnabled() {\n    // In production, this would fetch from client_currency_settings table\n    return true;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data/currencies.ts\n");

/***/ }),

/***/ "(rsc)/./lib/data/defaultProductTemplates.ts":
/*!*********************************************!*\
  !*** ./lib/data/defaultProductTemplates.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTemplates: () => (/* binding */ defaultTemplates),\n/* harmony export */   deleteProductTemplate: () => (/* binding */ deleteProductTemplate),\n/* harmony export */   forceReinitializeTemplates: () => (/* binding */ forceReinitializeTemplates),\n/* harmony export */   freeFireTemplate: () => (/* binding */ freeFireTemplate),\n/* harmony export */   googlePlayTemplate: () => (/* binding */ googlePlayTemplate),\n/* harmony export */   initializeDefaultTemplates: () => (/* binding */ initializeDefaultTemplates),\n/* harmony export */   loadProductTemplates: () => (/* binding */ loadProductTemplates),\n/* harmony export */   pubgMobileTemplate: () => (/* binding */ pubgMobileTemplate),\n/* harmony export */   saveProductTemplate: () => (/* binding */ saveProductTemplate),\n/* harmony export */   tiktokCoinsTemplate: () => (/* binding */ tiktokCoinsTemplate)\n/* harmony export */ });\n// =====================================================\n// DEFAULT PRODUCT TEMPLATES\n// =====================================================\n// ## TODO: Replace with Supabase data loading\n// These templates will be used to initialize the system with sample products\n/**\n * Generate consistent ID for templates (fixed IDs for stability)\n */ function generateId(prefix = '', suffix = '') {\n    // Use consistent IDs instead of random ones to avoid localStorage issues\n    return `${prefix}${Date.now().toString(36)}${suffix}${Math.random().toString(36).substr(2, 4)}`;\n}\n/**\n * Generate fixed ID for template components (for consistency)\n */ function fixedId(id) {\n    return id;\n}\n/**\n * PUBG Mobile UC Top-up Template\n */ const pubgMobileTemplate = {\n    id: \"pubg-mobile-uc\",\n    name: \"شحن يوسي PUBG Mobile\",\n    nameEnglish: \"PUBG Mobile UC Top-up\",\n    description: \"شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار\",\n    descriptionEnglish: \"Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 25,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"pubg-player-id\"),\n            type: \"text\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                minLength: 8,\n                maxLength: 12,\n                pattern: \"^[0-9]+$\"\n            },\n            sortOrder: 0,\n            isActive: true\n        },\n        {\n            id: fixedId(\"pubg-server\"),\n            type: \"dropdown\",\n            name: \"server\",\n            label: \"الخادم\",\n            placeholder: \"اختر الخادم...\",\n            required: true,\n            options: [\n                \"الشرق الأوسط\",\n                \"أوروبا\",\n                \"آسيا\",\n                \"أمريكا الشمالية\",\n                \"أمريكا الجنوبية\"\n            ],\n            sortOrder: 1,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"pubg-uc-60\"),\n            name: \"60 يوسي\",\n            amount: \"60 UC\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-325\"),\n            name: \"325 يوسي\",\n            amount: \"325 UC\",\n            price: 25,\n            originalPrice: 30,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-660\"),\n            name: \"660 يوسي\",\n            amount: \"660 UC\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-1800\"),\n            name: \"1800 يوسي\",\n            amount: \"1800 UC\",\n            price: 120,\n            originalPrice: 150,\n            discount: 20,\n            popular: false,\n            isActive: true,\n            sortOrder: 3,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        }\n    ],\n    features: [\n        \"🚀 تسليم فوري للأكواد\",\n        \"💯 ضمان الجودة والأمان\",\n        \"🔒 معاملات آمنة ومشفرة\",\n        \"📱 يعمل على جميع الأجهزة\",\n        \"🎮 دعم فني متخصص\",\n        \"💳 طرق دفع متعددة\"\n    ],\n    tags: [\n        \"pubg\",\n        \"mobile\",\n        \"uc\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Free Fire Diamonds Template\n */ const freeFireTemplate = {\n    id: \"free-fire-diamonds\",\n    name: \"شحن جواهر Free Fire\",\n    nameEnglish: \"Free Fire Diamonds Top-up\",\n    description: \"شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار\",\n    descriptionEnglish: \"Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.\",\n        expiryDays: 7\n    },\n    fields: [\n        {\n            id: fixedId(\"ff-player-id\"),\n            type: \"number\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            labelEnglish: \"Player ID\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                min: 100000000,\n                max: 9999999999\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"ff-diamonds-100\"),\n            name: \"100 جوهرة\",\n            amount: \"100 💎\",\n            price: 10,\n            originalPrice: 12,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-520\"),\n            name: \"520 جوهرة\",\n            amount: \"520 💎\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-1080\"),\n            name: \"1080 جوهرة\",\n            amount: \"1080 💎\",\n            price: 100,\n            originalPrice: 120,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💎 جواهر أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎮 دعم فني 24/7\"\n    ],\n    tags: [\n        \"free fire\",\n        \"diamonds\",\n        \"جواهر\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Google Play Gift Card Template\n */ const googlePlayTemplate = {\n    id: \"google-play-gift-card\",\n    name: \"بطاقة هدايا Google Play\",\n    nameEnglish: \"Google Play Gift Card\",\n    description: \"بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي\",\n    descriptionEnglish: \"Digital Google Play Gift Cards - Use them to buy apps, games, and digital content\",\n    category: \"بطاقات الهدايا\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"coupon\",\n        deliveryInstructions: \"استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.\",\n        expiryDays: 365\n    },\n    fields: [\n        {\n            id: fixedId(\"gp-email\"),\n            type: \"email\",\n            name: \"email\",\n            label: \"البريد الإلكتروني\",\n            labelEnglish: \"Email Address\",\n            placeholder: \"أدخل بريدك الإلكتروني...\",\n            required: true,\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"gp-usd-10\"),\n            name: \"$10 USD\",\n            nameArabic: \"10 دولار\",\n            amount: \"$10 USD\",\n            price: 10,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-25\"),\n            name: \"$25 USD\",\n            nameArabic: \"25 دولار\",\n            amount: \"$25 USD\",\n            price: 25,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-50\"),\n            name: \"$50 USD\",\n            nameArabic: \"50 دولار\",\n            amount: \"$50 USD\",\n            price: 50,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🎁 بطاقة هدايا رقمية\",\n        \"🚀 تسليم فوري\",\n        \"🌍 صالحة عالمياً\",\n        \"📱 لجميع أجهزة Android\",\n        \"🔒 آمنة ومضمونة\"\n    ],\n    tags: [\n        \"google play\",\n        \"gift card\",\n        \"بطاقة هدايا\",\n        \"تطبيقات\"\n    ],\n    isActive: true,\n    isFeatured: false,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * TikTok Coins Template\n */ const tiktokCoinsTemplate = {\n    id: \"tiktok-coins\",\n    name: \"شحن عملات TikTok\",\n    nameEnglish: \"TikTok Coins Top-up\",\n    description: \"شحن فوري لعملات TikTok - ادعم المبدعين المفضلين لديك واحصل على المزيد من المزايا\",\n    descriptionEnglish: \"Instant TikTok Coins top-up - Support your favorite creators and get more features\",\n    category: \"وسائل التواصل\",\n    basePrice: 5,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"gift_code\",\n        deliveryInstructions: \"سيتم إضافة العملات إلى حسابك في TikTok فوراً بعد الدفع.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"tiktok-username\"),\n            type: \"text\",\n            name: \"username\",\n            label: \"اسم المستخدم في TikTok\",\n            placeholder: \"أدخل اسم المستخدم...\",\n            required: true,\n            validation: {\n                minLength: 3,\n                maxLength: 30\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"tiktok-coins-100\"),\n            name: \"100 عملة\",\n            amount: \"100 Coins\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-500\"),\n            name: \"500 عملة\",\n            amount: \"500 Coins\",\n            price: 20,\n            originalPrice: 25,\n            discount: 20,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-1000\"),\n            name: \"1000 عملة\",\n            amount: \"1000 Coins\",\n            price: 35,\n            originalPrice: 45,\n            discount: 22,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💰 عملات أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎁 ادعم المبدعين المفضلين\"\n    ],\n    tags: [\n        \"tiktok\",\n        \"coins\",\n        \"عملات\",\n        \"شحن\",\n        \"وسائل التواصل\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * All default templates\n */ const defaultTemplates = [\n    pubgMobileTemplate,\n    freeFireTemplate,\n    googlePlayTemplate,\n    tiktokCoinsTemplate\n];\n/**\n * Initialize default templates in localStorage (client-side only)\n * ## TODO: Replace with Supabase initialization\n */ function initializeDefaultTemplates() {\n    // Check if we're in a browser environment\n    if (true) {\n        console.log('Skipping localStorage initialization on server-side');\n        return;\n    }\n    try {\n        const existingTemplates = localStorage.getItem('productTemplates');\n        if (!existingTemplates) {\n            console.log('Initializing default product templates...');\n            localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            console.log(`Initialized ${defaultTemplates.length} default templates`);\n        } else {\n            // Validate existing templates\n            try {\n                const parsed = JSON.parse(existingTemplates);\n                if (!Array.isArray(parsed) || parsed.length === 0) {\n                    console.log('Invalid templates found, reinitializing...');\n                    localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n                } else {\n                    console.log(`Found ${parsed.length} existing templates in localStorage`);\n                }\n            } catch (parseError) {\n                console.log('Corrupted templates found, reinitializing...');\n                localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default templates:', error);\n    }\n}\n/**\n * Force reinitialize templates (useful for debugging)\n */ function forceReinitializeTemplates() {\n    if (true) return;\n    try {\n        console.log('Force reinitializing product templates...');\n        localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n        console.log(`Reinitialized ${defaultTemplates.length} templates`);\n    } catch (error) {\n        console.error('Error force reinitializing templates:', error);\n    }\n}\n/**\n * Load product templates from localStorage\n * ## TODO: Replace with Supabase query\n */ function loadProductTemplates() {\n    try {\n        const savedTemplates = localStorage.getItem('productTemplates');\n        if (savedTemplates) {\n            return JSON.parse(savedTemplates);\n        }\n        return [];\n    } catch (error) {\n        console.error('Error loading product templates:', error);\n        return [];\n    }\n}\n/**\n * Save product template to localStorage\n * ## TODO: Replace with Supabase insert/update\n */ function saveProductTemplate(template) {\n    try {\n        const templates = loadProductTemplates();\n        const existingIndex = templates.findIndex((t)=>t.id === template.id);\n        if (existingIndex >= 0) {\n            templates[existingIndex] = template;\n        } else {\n            templates.push(template);\n        }\n        localStorage.setItem('productTemplates', JSON.stringify(templates));\n    } catch (error) {\n        console.error('Error saving product template:', error);\n        throw error;\n    }\n}\n/**\n * Delete product template from localStorage\n * ## TODO: Replace with Supabase delete\n */ function deleteProductTemplate(templateId) {\n    try {\n        const templates = loadProductTemplates();\n        const filteredTemplates = templates.filter((t)=>t.id !== templateId);\n        localStorage.setItem('productTemplates', JSON.stringify(filteredTemplates));\n    } catch (error) {\n        console.error('Error deleting product template:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data/defaultProductTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./lib/data/gameCards.ts":
/*!*******************************!*\
  !*** ./lib/data/gameCards.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gameCards: () => (/* binding */ gameCards)\n/* harmony export */ });\nconst gameCards = [\n    {\n        title: \"FREE FIRE\",\n        subtitle: \"شحن جواهر\",\n        gradient: \"from-blue-500 via-blue-600 to-purple-600\",\n        icon: \"🔥\"\n    },\n    {\n        title: \"شحن عبر الـ ID\",\n        subtitle: \"شحن فوري\",\n        gradient: \"from-orange-500 via-red-500 to-red-600\",\n        icon: \"⚡\"\n    },\n    {\n        title: \"شحن جواهر بدوم\",\n        subtitle: \"FREE FIRE\",\n        gradient: \"from-green-500 via-emerald-500 to-blue-600\",\n        icon: \"💎\"\n    },\n    {\n        title: \"شحن جواهر فوري\",\n        subtitle: \"FREE FIRE\",\n        gradient: \"from-purple-500 via-pink-500 to-pink-600\",\n        icon: \"🎮\"\n    },\n    {\n        title: \"PUBG Mobile\",\n        subtitle: \"شحن UC\",\n        gradient: \"from-yellow-500 via-orange-500 to-red-600\",\n        icon: \"🎯\"\n    },\n    {\n        title: \"Call of Duty\",\n        subtitle: \"شحن CP\",\n        gradient: \"from-gray-600 via-gray-700 to-black\",\n        icon: \"🔫\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data/gameCards.ts\n");

/***/ }),

/***/ "(rsc)/./lib/data/slides.ts":
/*!****************************!*\
  !*** ./lib/data/slides.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   slides: () => (/* binding */ slides)\n/* harmony export */ });\nconst slides = [\n    {\n        id: 1,\n        title: \"يمكنك الآن شحن جميع الألعاب\",\n        subtitle: \"من خلال تطبيق واحد فقط\",\n        buttonText: \"ابدأ اللعب الآن\",\n        gradient: \"from-pink-100 via-yellow-200 to-yellow-400\",\n        image: \"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg\"\n    },\n    {\n        id: 2,\n        title: \"أسرع خدمة شحن في السودان\",\n        subtitle: \"شحن فوري خلال دقائق معدودة\",\n        buttonText: \"اشحن الآن\",\n        gradient: \"from-blue-100 via-purple-200 to-purple-400\",\n        image: \"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg\"\n    },\n    {\n        id: 3,\n        title: \"عروض حصرية وخصومات مميزة\",\n        subtitle: \"وفر أكثر مع عروضنا الخاصة\",\n        buttonText: \"اكتشف العروض\",\n        gradient: \"from-green-100 via-emerald-200 to-emerald-400\",\n        image: \"https://c4.wallpaperflare.com/wallpaper/1003/987/595/pubg-player-unknown-battleground-players-hd-wallpaper-preview.jpg\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/data/slides.ts\n");

/***/ }),

/***/ "(rsc)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(rsc)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .select(`\n      *,\n      product_packages(*),\n      custom_fields(*)\n    `)\n    .eq('is_active', filters?.isActive ?? true)\n    .order('created_at', { ascending: false })\n  \n  if (error) throw error\n  return data.map(transformProductFromDB)\n  */ // Temporary: Load from localStorage with default initialization (client-side only)\n    try {\n        (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.initializeDefaultTemplates)();\n        // On server-side, return default templates directly\n        if (true) {\n            console.log('Server-side: returning default templates');\n            return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.defaultTemplates, filters);\n        }\n        // On client-side, load from localStorage\n        const products = (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.loadProductTemplates)();\n        const validProducts = Array.isArray(products) ? products : [];\n        return applyFilters(validProducts, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .select(`\n      *,\n      product_packages(*),\n      custom_fields(*)\n    `)\n    .eq('id', id)\n    .single()\n\n  if (error) throw error\n  return transformProductFromDB(data)\n  */ try {\n        console.log(`🔍 Looking for product with ID: \"${id}\"`);\n        // Temporary: Load from localStorage\n        const products = await getProducts();\n        console.log(`📦 Total products available: ${products.length}`);\n        console.log(`📋 Available product IDs: ${products.map((p)=>p.id).join(', ')}`);\n        const product = products.find((p)=>p.id === id);\n        if (product) {\n            console.log(`✅ Found product: \"${product.name}\" (Active: ${product.isActive})`);\n            return product;\n        } else {\n            console.log(`❌ Product with ID \"${id}\" not found`);\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data: productData, error: productError } = await supabase\n    .from('products')\n    .insert({\n      name: product.name,\n      name_english: product.nameEnglish,\n      description: product.description,\n      category: product.category,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (productError) throw productError\n  \n  // Insert packages\n  if (product.packages.length > 0) {\n    const { error: packagesError } = await supabase\n      .from('product_packages')\n      .insert(product.packages.map(pkg => ({\n        product_id: productData.id,\n        name: pkg.name,\n        // ... other package fields\n      })))\n    \n    if (packagesError) throw packagesError\n  }\n  \n  // Insert custom fields\n  if (product.fields.length > 0) {\n    const { error: fieldsError } = await supabase\n      .from('custom_fields')\n      .insert(product.fields.map(field => ({\n        product_id: productData.id,\n        field_type: field.type,\n        // ... other field properties\n      })))\n    \n    if (fieldsError) throw fieldsError\n  }\n  \n  return getProductById(productData.id)\n  */ // Temporary: Save to localStorage\n    const newProduct = {\n        ...product,\n        id: generateId(),\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.saveProductTemplate)(newProduct);\n    return newProduct;\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .update({\n      name: updates.name,\n      description: updates.description,\n      // ... other fields\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', id)\n    .select()\n    .single()\n  \n  if (error) throw error\n  \n  // Update packages and fields if provided\n  // ... handle packages and fields updates\n  \n  return getProductById(id)\n  */ // Temporary: Update in localStorage\n    const products = (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.loadProductTemplates)();\n    const index = products.findIndex((p)=>p.id === id);\n    if (index === -1) throw new Error('Product not found');\n    const updatedProduct = {\n        ...products[index],\n        ...updates,\n        updatedAt: new Date()\n    };\n    (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.saveProductTemplate)(updatedProduct);\n    return updatedProduct;\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // ## TODO: Replace with Supabase cascade delete\n    /*\n  const { error } = await supabase\n    .from('products')\n    .delete()\n    .eq('id', id)\n  \n  if (error) throw error\n  */ // Temporary: Remove from localStorage\n    (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.deleteProductTemplate)(id);\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return product?.packages || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map(([category, count])=>({\n            category,\n            count\n        })).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/services/productService.ts\n");

/***/ }),

/***/ "(rsc)/./lib/utils/currency.ts":
/*!*******************************!*\
  !*** ./lib/utils/currency.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CURRENCY_DECIMAL_PLACES: () => (/* binding */ CURRENCY_DECIMAL_PLACES),
/* harmony export */   MAJOR_CURRENCIES: () => (/* binding */ MAJOR_CURRENCIES),
/* harmony export */   MIDDLE_EAST_CURRENCIES: () => (/* binding */ MIDDLE_EAST_CURRENCIES),
/* harmony export */   RTL_CURRENCIES: () => (/* binding */ RTL_CURRENCIES),
/* harmony export */   calculateConversionWithFees: () => (/* binding */ calculateConversionWithFees),
/* harmony export */   calculateCrossRate: () => (/* binding */ calculateCrossRate),
/* harmony export */   convertCurrencyAmount: () => (/* binding */ convertCurrencyAmount),
/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),
/* harmony export */   getCurrencyDisplayInfo: () => (/* binding */ getCurrencyDisplayInfo),
/* harmony export */   getDefaultCurrency: () => (/* binding */ getDefaultCurrency),
/* harmony export */   getEnabledCurrencies: () => (/* binding */ getEnabledCurrencies),
/* harmony export */   parseCurrencyAmount: () => (/* binding */ parseCurrencyAmount),
/* harmony export */   validateConversionAmount: () => (/* binding */ validateConversionAmount),
/* harmony export */   validateCurrencyCode: () => (/* binding */ validateCurrencyCode),
/* harmony export */   validateExchangeRate: () => (/* binding */ validateExchangeRate)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const formatCurrency = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call formatCurrency() from the server but formatCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"formatCurrency",
);const getCurrencyDisplayInfo = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call getCurrencyDisplayInfo() from the server but getCurrencyDisplayInfo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"getCurrencyDisplayInfo",
);const parseCurrencyAmount = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call parseCurrencyAmount() from the server but parseCurrencyAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"parseCurrencyAmount",
);const convertCurrencyAmount = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call convertCurrencyAmount() from the server but convertCurrencyAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"convertCurrencyAmount",
);const calculateConversionWithFees = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call calculateConversionWithFees() from the server but calculateConversionWithFees is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"calculateConversionWithFees",
);const calculateCrossRate = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call calculateCrossRate() from the server but calculateCrossRate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"calculateCrossRate",
);const validateCurrencyCode = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call validateCurrencyCode() from the server but validateCurrencyCode is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"validateCurrencyCode",
);const validateExchangeRate = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call validateExchangeRate() from the server but validateExchangeRate is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"validateExchangeRate",
);const validateConversionAmount = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call validateConversionAmount() from the server but validateConversionAmount is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"validateConversionAmount",
);const getDefaultCurrency = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call getDefaultCurrency() from the server but getDefaultCurrency is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"getDefaultCurrency",
);const getEnabledCurrencies = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call getEnabledCurrencies() from the server but getEnabledCurrencies is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"getEnabledCurrencies",
);const CURRENCY_DECIMAL_PLACES = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call CURRENCY_DECIMAL_PLACES() from the server but CURRENCY_DECIMAL_PLACES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"CURRENCY_DECIMAL_PLACES",
);const RTL_CURRENCIES = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call RTL_CURRENCIES() from the server but RTL_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"RTL_CURRENCIES",
);const MAJOR_CURRENCIES = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call MAJOR_CURRENCIES() from the server but MAJOR_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"MAJOR_CURRENCIES",
);const MIDDLE_EAST_CURRENCIES = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call MIDDLE_EAST_CURRENCIES() from the server but MIDDLE_EAST_CURRENCIES is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\VS-projects\\try\\alraya-store\\lib\\utils\\currency.ts",
"MIDDLE_EAST_CURRENCIES",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbulk-data%2Froute&page=%2Fapi%2Fbulk-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbulk-data%2Froute.ts&appDir=D%3A%5CVS-projects%5Ctry%5Calraya-store%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVS-projects%5Ctry%5Calraya-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbulk-data%2Froute&page=%2Fapi%2Fbulk-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbulk-data%2Froute.ts&appDir=D%3A%5CVS-projects%5Ctry%5Calraya-store%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVS-projects%5Ctry%5Calraya-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_VS_projects_try_alraya_store_app_api_bulk_data_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/bulk-data/route.ts */ \"(rsc)/./app/api/bulk-data/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/bulk-data/route\",\n        pathname: \"/api/bulk-data\",\n        filename: \"route\",\n        bundlePath: \"app/api/bulk-data/route\"\n    },\n    resolvedPagePath: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\api\\\\bulk-data\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_VS_projects_try_alraya_store_app_api_bulk_data_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbulk-data%2Froute&page=%2Fapi%2Fbulk-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbulk-data%2Froute.ts&appDir=D%3A%5CVS-projects%5Ctry%5Calraya-store%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVS-projects%5Ctry%5Calraya-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/utils/currency.ts */ \"(rsc)/./lib/utils/currency.ts\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNWUy1wcm9qZWN0cyU1QyU1Q3RyeSU1QyU1Q2FscmF5YS1zdG9yZSU1QyU1Q2xpYiU1QyU1Q3V0aWxzJTVDJTVDY3VycmVuY3kudHMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJmb3JtYXRDdXJyZW5jeSUyMiUyQyUyMmdldEN1cnJlbmN5RGlzcGxheUluZm8lMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUE2SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZm9ybWF0Q3VycmVuY3lcIixcImdldEN1cnJlbmN5RGlzcGxheUluZm9cIl0gKi8gXCJEOlxcXFxWUy1wcm9qZWN0c1xcXFx0cnlcXFxcYWxyYXlhLXN0b3JlXFxcXGxpYlxcXFx1dGlsc1xcXFxjdXJyZW5jeS50c1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./lib/utils/currency.ts":
/*!*******************************!*\
  !*** ./lib/utils/currency.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENCY_DECIMAL_PLACES: () => (/* binding */ CURRENCY_DECIMAL_PLACES),\n/* harmony export */   MAJOR_CURRENCIES: () => (/* binding */ MAJOR_CURRENCIES),\n/* harmony export */   MIDDLE_EAST_CURRENCIES: () => (/* binding */ MIDDLE_EAST_CURRENCIES),\n/* harmony export */   RTL_CURRENCIES: () => (/* binding */ RTL_CURRENCIES),\n/* harmony export */   calculateConversionWithFees: () => (/* binding */ calculateConversionWithFees),\n/* harmony export */   calculateCrossRate: () => (/* binding */ calculateCrossRate),\n/* harmony export */   convertCurrencyAmount: () => (/* binding */ convertCurrencyAmount),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getCurrencyDisplayInfo: () => (/* binding */ getCurrencyDisplayInfo),\n/* harmony export */   getDefaultCurrency: () => (/* binding */ getDefaultCurrency),\n/* harmony export */   getEnabledCurrencies: () => (/* binding */ getEnabledCurrencies),\n/* harmony export */   parseCurrencyAmount: () => (/* binding */ parseCurrencyAmount),\n/* harmony export */   validateConversionAmount: () => (/* binding */ validateConversionAmount),\n/* harmony export */   validateCurrencyCode: () => (/* binding */ validateCurrencyCode),\n/* harmony export */   validateExchangeRate: () => (/* binding */ validateExchangeRate)\n/* harmony export */ });\n/* __next_internal_client_entry_do_not_use__ formatCurrency,getCurrencyDisplayInfo,parseCurrencyAmount,convertCurrencyAmount,calculateConversionWithFees,calculateCrossRate,validateCurrencyCode,validateExchangeRate,validateConversionAmount,getDefaultCurrency,getEnabledCurrencies,CURRENCY_DECIMAL_PLACES,RTL_CURRENCIES,MAJOR_CURRENCIES,MIDDLE_EAST_CURRENCIES auto */ // =====================================================\n// CURRENCY FORMATTING AND DISPLAY\n// =====================================================\n/**\n * Format currency amount with proper symbol and locale\n */ function formatCurrency(amount, currency, options) {\n    const { showSymbol = true, decimalPlaces, locale = 'en-US' } = options || {};\n    let currencyInfo;\n    if (typeof currency === 'string') {\n        // If currency is just a code, we need to get the info\n        // This will be replaced with database lookup in production\n        currencyInfo = getCurrencyDisplayInfo(currency);\n    } else {\n        currencyInfo = currency;\n    }\n    const decimals = decimalPlaces ?? 2;\n    const formattedAmount = amount.toLocaleString(locale, {\n        minimumFractionDigits: decimals,\n        maximumFractionDigits: decimals\n    });\n    if (!showSymbol) {\n        return formattedAmount;\n    }\n    const symbol = currencyInfo.symbol || currencyInfo.code;\n    // Handle RTL currencies\n    if (currencyInfo.isRTL) {\n        return `${formattedAmount} ${symbol}`;\n    } else {\n        return `${symbol}${formattedAmount}`;\n    }\n}\n/**\n * Get currency display information\n * TODO: Replace with database lookup in production\n */ function getCurrencyDisplayInfo(currencyCode) {\n    // Temporary hardcoded mapping - will be replaced with database lookup\n    const currencyMap = {\n        'USD': {\n            code: 'USD',\n            name: 'US Dollar',\n            symbol: '$',\n            arabicName: 'الدولار الأمريكي',\n            isRTL: false\n        },\n        'SDG': {\n            code: 'SDG',\n            name: 'Sudanese Pound',\n            symbol: 'ج.س.',\n            arabicName: 'الجنيه السوداني',\n            isRTL: true\n        },\n        'EGP': {\n            code: 'EGP',\n            name: 'Egyptian Pound',\n            symbol: 'ج.م.',\n            arabicName: 'الجنيه المصري',\n            isRTL: true\n        },\n        'SAR': {\n            code: 'SAR',\n            name: 'Saudi Riyal',\n            symbol: 'ر.س',\n            arabicName: 'الريال السعودي',\n            isRTL: true\n        },\n        'AED': {\n            code: 'AED',\n            name: 'UAE Dirham',\n            symbol: 'د.إ',\n            arabicName: 'الدرهم الإماراتي',\n            isRTL: true\n        },\n        'EUR': {\n            code: 'EUR',\n            name: 'Euro',\n            symbol: '€',\n            arabicName: 'اليورو',\n            isRTL: false\n        },\n        'GBP': {\n            code: 'GBP',\n            name: 'British Pound',\n            symbol: '£',\n            arabicName: 'الجنيه الإسترليني',\n            isRTL: false\n        }\n    };\n    return currencyMap[currencyCode] || {\n        code: currencyCode,\n        name: currencyCode,\n        symbol: currencyCode,\n        isRTL: false\n    };\n}\n/**\n * Parse currency amount from formatted string\n */ function parseCurrencyAmount(formattedAmount, currency) {\n    const currencyInfo = getCurrencyDisplayInfo(currency);\n    // Remove currency symbol and spaces\n    let cleanAmount = formattedAmount.replace(currencyInfo.symbol, '').replace(/\\s/g, '').replace(/,/g, '');\n    const amount = parseFloat(cleanAmount);\n    return isNaN(amount) ? 0 : amount;\n}\n// =====================================================\n// CURRENCY CONVERSION UTILITIES\n// =====================================================\n/**\n * Convert amount between currencies using exchange rate\n */ function convertCurrencyAmount(amount, fromCurrency, toCurrency, exchangeRate) {\n    if (fromCurrency === toCurrency) {\n        return amount;\n    }\n    return amount * exchangeRate;\n}\n/**\n * Calculate conversion with fees\n */ function calculateConversionWithFees(amount, exchangeRate, feeRate = 0) {\n    const convertedAmount = amount * exchangeRate;\n    const fee = convertedAmount * feeRate;\n    const totalReceived = convertedAmount - fee;\n    return {\n        convertedAmount,\n        fee,\n        totalReceived\n    };\n}\n/**\n * Get cross-currency exchange rate via USD\n */ function calculateCrossRate(fromCurrencyToUSD, toCurrencyToUSD) {\n    if (fromCurrencyToUSD === 0) {\n        throw new Error('Invalid exchange rate: fromCurrencyToUSD cannot be zero');\n    }\n    return toCurrencyToUSD / fromCurrencyToUSD;\n}\n// =====================================================\n// VALIDATION UTILITIES\n// =====================================================\n/**\n * Validate currency code format\n */ function validateCurrencyCode(code) {\n    const errors = [];\n    if (!code) {\n        errors.push('Currency code is required');\n    } else if (code.length !== 3) {\n        errors.push('Currency code must be exactly 3 characters');\n    } else if (!/^[A-Z]{3}$/.test(code)) {\n        errors.push('Currency code must be 3 uppercase letters');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Validate exchange rate\n */ function validateExchangeRate(rate, fromCurrency, toCurrency) {\n    const errors = [];\n    const warnings = [];\n    if (rate <= 0) {\n        errors.push('Exchange rate must be positive');\n    }\n    if (fromCurrency === toCurrency && rate !== 1) {\n        errors.push('Exchange rate for same currency must be 1.0');\n    }\n    // Warn about unusual rates\n    if (rate > 10000) {\n        warnings.push('Exchange rate seems unusually high');\n    } else if (rate < 0.0001) {\n        warnings.push('Exchange rate seems unusually low');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings\n    };\n}\n/**\n * Validate conversion amount\n */ function validateConversionAmount(amount, currency, minimumAmounts) {\n    const errors = [];\n    if (amount <= 0) {\n        errors.push('Amount must be positive');\n    }\n    const minimumAmount = minimumAmounts?.[currency] || 0;\n    if (amount < minimumAmount) {\n        errors.push(`Minimum conversion amount for ${currency} is ${minimumAmount}`);\n    }\n    return {\n        isValid: errors.length === 0,\n        errors\n    };\n}\n/**\n * Get default currency based on environment\n */ function getDefaultCurrency() {\n    // USD is now the default currency for all new users\n    return 'USD';\n}\n/**\n * Get enabled currencies for current client\n */ function getEnabledCurrencies() {\n    // In production, this would come from client configuration\n    // USD is always first as the default currency\n    return [\n        'USD',\n        'SDG',\n        'EGP'\n    ];\n}\n// =====================================================\n// UTILITY CONSTANTS\n// =====================================================\nconst CURRENCY_DECIMAL_PLACES = {\n    'USD': 2,\n    'EUR': 2,\n    'GBP': 2,\n    'SDG': 2,\n    'EGP': 2,\n    'SAR': 2,\n    'AED': 2,\n    'BTC': 8,\n    'ETH': 6\n};\nconst RTL_CURRENCIES = [\n    'SDG',\n    'EGP',\n    'SAR',\n    'AED',\n    'IQD',\n    'JOD',\n    'KWD',\n    'LBP',\n    'LYD',\n    'MAD',\n    'OMR',\n    'QAR',\n    'SYP',\n    'TND',\n    'YER'\n];\nconst MAJOR_CURRENCIES = [\n    'USD',\n    'EUR',\n    'GBP',\n    'JPY',\n    'CHF',\n    'CAD',\n    'AUD'\n];\nconst MIDDLE_EAST_CURRENCIES = [\n    'SDG',\n    'EGP',\n    'SAR',\n    'AED',\n    'IQD',\n    'JOD',\n    'KWD',\n    'LBP',\n    'OMR',\n    'QAR'\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils/currency.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/utils/currency.ts */ \"(ssr)/./lib/utils/currency.ts\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNWUy1wcm9qZWN0cyU1QyU1Q3RyeSU1QyU1Q2FscmF5YS1zdG9yZSU1QyU1Q2xpYiU1QyU1Q3V0aWxzJTVDJTVDY3VycmVuY3kudHMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJmb3JtYXRDdXJyZW5jeSUyMiUyQyUyMmdldEN1cnJlbmN5RGlzcGxheUluZm8lMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUE2SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZm9ybWF0Q3VycmVuY3lcIixcImdldEN1cnJlbmN5RGlzcGxheUluZm9cIl0gKi8gXCJEOlxcXFxWUy1wcm9qZWN0c1xcXFx0cnlcXFxcYWxyYXlhLXN0b3JlXFxcXGxpYlxcXFx1dGlsc1xcXFxjdXJyZW5jeS50c1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CVS-projects%5C%5Ctry%5C%5Calraya-store%5C%5Clib%5C%5Cutils%5C%5Ccurrency.ts%22%2C%22ids%22%3A%5B%22formatCurrency%22%2C%22getCurrencyDisplayInfo%22%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbulk-data%2Froute&page=%2Fapi%2Fbulk-data%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbulk-data%2Froute.ts&appDir=D%3A%5CVS-projects%5Ctry%5Calraya-store%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CVS-projects%5Ctry%5Calraya-store&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();