"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 781,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 922,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 830,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 829,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 975,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 974,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1067,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1058,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1057,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"8Y1hvcddtm4Q8zWI4nTeUx6y9nU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1212,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1257,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1176,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});