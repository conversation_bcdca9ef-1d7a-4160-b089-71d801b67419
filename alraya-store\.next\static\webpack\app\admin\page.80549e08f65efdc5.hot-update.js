"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\",\n        quantityLimit: undefined\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        var _formData_packages;\n        // Validate package name\n        const nameValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackageName)(packageForm.name);\n        if (!nameValidation.isValid) {\n            toast({\n                title: \"خطأ في اسم الحزمة\",\n                description: nameValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate package price\n        const priceValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackagePrice)(packageForm.price);\n        if (!priceValidation.isValid) {\n            toast({\n                title: \"خطأ في السعر\",\n                description: priceValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate discount pricing if original price is provided\n        if (packageForm.originalPrice > 0) {\n            const discountValidation = (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.validateDiscountPricing)(packageForm.originalPrice, packageForm.price);\n            if (!discountValidation.isValid) {\n                toast({\n                    title: \"خطأ في الخصم\",\n                    description: discountValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        // Process and validate digital codes\n        const codeLines = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean);\n        const codesValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDigitalCodes)(codeLines);\n        if (!codesValidation.isValid) {\n            toast({\n                title: \"خطأ في الأكواد الرقمية\",\n                description: codesValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create digital codes with proper structure\n        const digitalCodes = (codesValidation.sanitizedCodes || []).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeDigitalCode)(key),\n                used: false,\n                assignedToOrderId: null,\n                createdAt: new Date()\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.name),\n            amount: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.amount),\n            price: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.price),\n            originalPrice: packageForm.originalPrice > 0 ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.originalPrice) : undefined,\n            description: packageForm.description ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.description) : undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            sortOrder: editingPackageIndex !== null ? editingPackageIndex : ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n            digitalCodes\n        };\n        updateFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingPackageIndex !== null ? \"تم تحديث الحزمة بنجاح\" : \"تم إضافة الحزمة بنجاح\"\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        var _formData_packages_index, _formData_packages;\n        const packageName = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : (_formData_packages_index = _formData_packages[index]) === null || _formData_packages_index === void 0 ? void 0 : _formData_packages_index.name) || \"الحزمة \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(packageName, '\"؟\\n\\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.'))) {\n            updateFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(packageName, '\" بنجاح')\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required,\n            options: field.options || []\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Dropdown option management functions\n    const addDropdownOption = ()=>{\n        const sanitizedText = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(newOptionText);\n        if (!sanitizedText) {\n            toast({\n                title: \"خطأ\",\n                description: \"يرجى إدخال نص الخيار\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Check for duplicates\n        const exists = fieldForm.options.some((opt)=>opt.label === sanitizedText);\n        if (exists) {\n            toast({\n                title: \"خطأ\",\n                description: \"هذا الخيار موجود بالفعل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const newOption = {\n            id: \"option_\".concat(Date.now()),\n            value: sanitizedText.toLowerCase().replace(/\\s+/g, '_'),\n            label: sanitizedText,\n            sortOrder: fieldForm.options.length,\n            isActive: true\n        };\n        setFieldForm((prev)=>({\n                ...prev,\n                options: [\n                    ...prev.options,\n                    newOption\n                ]\n            }));\n        setNewOptionText(\"\");\n    };\n    const removeDropdownOption = (optionId)=>{\n        setFieldForm((prev)=>({\n                ...prev,\n                options: prev.options.filter((opt)=>opt.id !== optionId)\n            }));\n    };\n    const moveDropdownOption = (optionId, direction)=>{\n        setFieldForm((prev)=>{\n            const options = [\n                ...prev.options\n            ];\n            const index = options.findIndex((opt)=>opt.id === optionId);\n            if (index === -1) return prev;\n            const newIndex = direction === 'up' ? index - 1 : index + 1;\n            if (newIndex < 0 || newIndex >= options.length) return prev[options[index], options[newIndex]] = [\n                options[newIndex],\n                options[index]\n            ];\n            // Update sort orders\n            options.forEach((opt, i)=>{\n                opt.sortOrder = i;\n            });\n            return {\n                ...prev,\n                options\n            };\n        });\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        var _formData_fields;\n        // Validate field label\n        const labelValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateFieldLabel)(fieldForm.label);\n        if (!labelValidation.isValid) {\n            toast({\n                title: \"خطأ في تسمية الحقل\",\n                description: labelValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate dropdown options if it's a dropdown field\n        if (fieldForm.type === 'dropdown') {\n            const optionsValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDropdownOptions)(fieldForm.options);\n            if (!optionsValidation.isValid) {\n                toast({\n                    title: \"خطأ في خيارات القائمة\",\n                    description: optionsValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.label),\n            placeholder: fieldForm.placeholder ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.placeholder) : undefined,\n            required: fieldForm.required,\n            isActive: true,\n            sortOrder: editingFieldIndex !== null ? editingFieldIndex : ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n            validation: {},\n            options: fieldForm.type === 'dropdown' ? fieldForm.options : undefined\n        };\n        updateFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingFieldIndex !== null ? \"تم تحديث الحقل بنجاح\" : \"تم إضافة الحقل بنجاح\"\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        var _formData_fields_index, _formData_fields;\n        const fieldLabel = ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : (_formData_fields_index = _formData_fields[index]) === null || _formData_fields_index === void 0 ? void 0 : _formData_fields_index.label) || \"الحقل \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(fieldLabel, '\"؟'))) {\n            updateFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(fieldLabel, '\" بنجاح')\n            });\n        }\n    };\n    // Handle cancel with unsaved changes warning\n    const handleCancel = ()=>{\n        if (hasUnsavedChanges) {\n            if (confirm(\"لديك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟\")) {\n                onCancel();\n            }\n        } else {\n            onCancel();\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 664,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 667,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 666,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 662,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 675,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 661,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 660,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    validationErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"يرجى تصحيح الأخطاء التالية:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationErrors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: error\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 696,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 690,\n                        columnNumber: 11\n                    }, this),\n                    validationWarnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تحذيرات:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationWarnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: warning\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 711,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 709,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 11\n                    }, this),\n                    hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تغييرات غير محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: \"لديك تغييرات غير محفوظة. تأكد من حفظ عملك قبل المغادرة.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 753,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 751,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 739,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 763,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 776,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 774,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 811,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 817,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 820,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 826,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 819,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 738,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 840,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 834,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 859,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 864,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 857,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 869,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 879,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 873,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 888,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 882,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 867,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 856,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 896,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 852,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 850,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 905,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 833,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 923,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 928,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 922,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"universal_input\" ? \"حقل إدخال\" : field.type === \"dropdown\" ? \"قائمة منسدلة\" : field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 948,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 955,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        field.type === \"dropdown\" && field.options && field.options.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm mb-1\",\n                                                                    children: \"الخيارات:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 965,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: [\n                                                                        field.options.slice(0, 3).map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-gray-600/50 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                                                children: option.label\n                                                                            }, option.id, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 968,\n                                                                                columnNumber: 31\n                                                                            }, this)),\n                                                                        field.options.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-gray-600/50 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                field.options.length - 3,\n                                                                                \" أخرى\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 973,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 966,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 964,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 945,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 988,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 982,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 997,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 944,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 938,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1006,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1007,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1013,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1005,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 921,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1029,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1036,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1034,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1022,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1040,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1021,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1057,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1056,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1055,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1066,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1067,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1065,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1077,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1078,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1076,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1064,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الحالي *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1092,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0.01\",\n                                                            value: packageForm.price,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        price: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1093,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"السعر الذي سيدفعه العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1102,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1091,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الأصلي (اختياري)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1106,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0\",\n                                                            value: packageForm.originalPrice,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        originalPrice: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1107,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"للعرض كخصم (اتركه فارغاً إذا لم يكن هناك خصم)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1116,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1105,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1090,\n                                            columnNumber: 15\n                                        }, this),\n                                        packageForm.originalPrice > 0 && packageForm.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3\",\n                                            children: packageForm.originalPrice > packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1125,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-300 text-sm\",\n                                                        children: [\n                                                            \"خصم \",\n                                                            (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.calculateDiscountPercentage)(packageForm.originalPrice, packageForm.price),\n                                                            \"% (توفير \",\n                                                            (packageForm.originalPrice - packageForm.price).toFixed(2),\n                                                            \" دولار)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1126,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1124,\n                                                columnNumber: 21\n                                            }, this) : packageForm.originalPrice === packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1133,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-300 text-sm\",\n                                                        children: \"لا يوجد خصم (السعران متساويان)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1134,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1132,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1140,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-300 text-sm\",\n                                                        children: \"خطأ: السعر الأصلي يجب أن يكون أكبر من السعر الحالي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1141,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1139,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1122,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1089,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1152,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1153,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1165,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1164,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1174,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1170,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1179,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1163,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-5 h-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"حد الكمية اليدوي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(للمنتجات بدون أكواد)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1190,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 متى تستخدم هذا:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1197,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-orange-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• للمنتجات الرقمية بدون أكواد (اشتراكات، تراخيص)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1199,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• للمنتجات الفيزيائية مع مخزون محدود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1200,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• اتركه فارغاً للمنتجات غير المحدودة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1198,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1196,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            value: packageForm.quantityLimit || '',\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        quantityLimit: e.target.value ? Number(e.target.value) : undefined\n                                                    })),\n                                            placeholder: \"اتركه فارغاً للكمية غير المحدودة\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-1\",\n                                            children: packageForm.quantityLimit ? 'سيتم عرض \"متبقي '.concat(packageForm.quantityLimit, '\" للعملاء') : 'سيتم عرض \"متوفر\" للعملاء (كمية غير محدودة)'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1216,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1189,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1226,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1225,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1062,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1240,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1247,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1054,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1053,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1262,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1261,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1260,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1269,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value,\n                                                        options: e.target.value === \"dropdown\" ? prev.options : []\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"universal_input\",\n                                                    children: \"حقل إدخال (نص، بريد إلكتروني، رقم)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1292,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"dropdown\",\n                                                    children: \"قائمة منسدلة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-1\",\n                                            children: fieldForm.type === \"universal_input\" ? \"حقل إدخال عام يمكن استخدامه للنصوص والأرقام والبريد الإلكتروني\" : \"قائمة خيارات يختار منها المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1295,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1281,\n                                    columnNumber: 13\n                                }, this),\n                                fieldForm.type === \"universal_input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1306,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1307,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1305,\n                                    columnNumber: 15\n                                }, this),\n                                fieldForm.type === \"dropdown\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-3\",\n                                            children: \"خيارات القائمة المنسدلة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1320,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newOptionText,\n                                                    onChange: (e)=>setNewOptionText(e.target.value),\n                                                    placeholder: \"أدخل خيار جديد...\",\n                                                    className: \"flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                                    onKeyPress: (e)=>{\n                                                        if (e.key === 'Enter') {\n                                                            e.preventDefault();\n                                                            addDropdownOption();\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1324,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addDropdownOption,\n                                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1342,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1337,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1323,\n                                            columnNumber: 17\n                                        }, this),\n                                        fieldForm.options.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                            children: fieldForm.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 bg-gray-700/50 rounded-lg p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-1 text-sm\",\n                                                            children: option.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1351,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>moveDropdownOption(option.id, 'up'),\n                                                                    disabled: index === 0,\n                                                                    className: \"h-6 w-6 p-0\",\n                                                                    children: \"↑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>moveDropdownOption(option.id, 'down'),\n                                                                    disabled: index === fieldForm.options.length - 1,\n                                                                    className: \"h-6 w-6 p-0\",\n                                                                    children: \"↓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1363,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>removeDropdownOption(option.id),\n                                                                    className: \"h-6 w-6 p-0 text-red-400 hover:text-red-300\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 1380,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1352,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, option.id, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1350,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 text-gray-400 text-sm border border-gray-600 rounded-lg border-dashed\",\n                                            children: \"لا توجد خيارات. أضف خيار واحد على الأقل.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1387,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1319,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1403,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1267,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1411,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1410,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1433,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1432,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1431,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1439,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1430,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1429,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 659,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"uAx9ljgi+/WsnhASeI0N9MU3dn4=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1551,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1560,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1569,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1550,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1581,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1584,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1629,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1628,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1627,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1634,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1635,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1637,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1612,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1583,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1580,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1643,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1646,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1648,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1649,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1650,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1651,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1647,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1656,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1668,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1663,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1655,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1548,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});