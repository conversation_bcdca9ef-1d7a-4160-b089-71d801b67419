"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./components/products/SimpleProductForm.tsx":
/*!***************************************************!*\
  !*** ./components/products/SimpleProductForm.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleProductForm: () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_data_currencies__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/data/currencies */ \"(app-pages-browser)/./lib/data/currencies.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ SimpleProductForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { template, onSubmit, currency, showPricing = true, disabled = false, className = \"\" } = param;\n    _s();\n    const [selectedPackage, setSelectedPackage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [customFieldValues, setCustomFieldValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [fieldErrors, setFieldErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { convertPrice } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrencyConverter)();\n    // Calculate total price\n    const calculateTotalPrice = ()=>{\n        if (!selectedPackage) return 0;\n        return selectedPackage.price * quantity;\n    };\n    // Format price with currency\n    const formatPrice = (price)=>{\n        const convertedPrice = convertPrice(price, \"USD\", currency);\n        return (0,_lib_data_currencies__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(convertedPrice, currency);\n    };\n    // Handle package selection\n    const handlePackageSelect = (pkg)=>{\n        setSelectedPackage(pkg);\n        setFieldErrors({}) // Clear errors when package changes\n        ;\n    };\n    // Handle custom field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setCustomFieldValues((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field when user starts typing\n        if (fieldErrors[fieldName]) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: \"\"\n                }));\n        }\n    };\n    // Validate form fields\n    const validateFields = ()=>{\n        const errors = {};\n        const activeFields = template.fields.filter((f)=>f.isActive);\n        activeFields.forEach((field)=>{\n            const value = customFieldValues[field.name];\n            if (field.required && (!value || value.toString().trim() === \"\")) {\n                errors[field.name] = \"\".concat(field.label, \" مطلوب\");\n            }\n            if (value && field.type === \"email\") {\n                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                if (!emailRegex.test(value)) {\n                    errors[field.name] = \"يرجى إدخال بريد إلكتروني صحيح\";\n                }\n            }\n            if (value && field.type === \"number\") {\n                if (isNaN(Number(value))) {\n                    errors[field.name] = \"يرجى إدخال رقم صحيح\";\n                }\n            }\n        });\n        return errors;\n    };\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        if (!selectedPackage) {\n            alert(\"يرجى اختيار حزمة\");\n            return;\n        }\n        // Validate all custom fields\n        const errors = validateFields();\n        if (Object.keys(errors).length > 0) {\n            setFieldErrors(errors);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const formData = {\n                templateId: template.id,\n                selectedPackage,\n                quantity,\n                customFields: customFieldValues,\n                totalPrice: calculateTotalPrice(),\n                currency\n            };\n            await onSubmit(formData);\n        } catch (error) {\n            console.error(\"Error submitting form:\", error);\n            alert(\"حدث خطأ أثناء إرسال الطلب\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Render custom field\n    const renderCustomField = (field)=>{\n        var _field_options;\n        const value = customFieldValues[field.name] || \"\";\n        const error = fieldErrors[field.name];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"block text-sm font-medium text-slate-300\",\n                    children: [\n                        field.label,\n                        field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-400 ml-1\",\n                            children: \"*\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 30\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                field.type === \"universal_input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this),\n                field.type === \"dropdown\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: \"\",\n                            children: field.placeholder || \"اختر \".concat(field.label)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this),\n                        (_field_options = field.options) === null || _field_options === void 0 ? void 0 : _field_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: option.value,\n                                children: option.label\n                            }, option.id, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, this),\n                field.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this),\n                field.type === \"email\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"email\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this),\n                field.type === \"number\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"number\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, field.id, true, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate values for rendering\n    const totalPrice = calculateTotalPrice();\n    const visibleFields = template.fields.filter((f)=>f.isActive);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                \"اختر الحزمة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4\",\n                            children: template.packages.map((pkg)=>{\n                                var _pkg_digitalCodes;\n                                const enhancedPkg = (0,_lib_types__WEBPACK_IMPORTED_MODULE_5__.enhancePackageWithDiscountInfo)(pkg);\n                                const availableCodes = ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.filter((code)=>!code.used).length) || 0;\n                                // Enhanced availability logic for different product types\n                                const isAvailable = (()=>{\n                                    if (!pkg.isActive) return false;\n                                    // Digital products with codes\n                                    if (pkg.digitalCodes && pkg.digitalCodes.length > 0) {\n                                        return availableCodes > 0;\n                                    }\n                                    // Products with manual quantity limits\n                                    if (pkg.quantityLimit !== undefined && pkg.quantityLimit !== null) {\n                                        return pkg.quantityLimit > 0;\n                                    }\n                                    // Unlimited digital products/services (no codes, no limits)\n                                    return true;\n                                })();\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>!disabled && isAvailable && handlePackageSelect(pkg),\n                                    className: \"relative p-4 rounded-lg border-2 cursor-pointer transition-all \".concat((selectedPackage === null || selectedPackage === void 0 ? void 0 : selectedPackage.id) === pkg.id ? \"border-blue-500 bg-blue-500/10\" : isAvailable ? \"border-slate-600 bg-slate-700/30 hover:border-slate-500\" : \"border-red-600/50 bg-red-900/20\", \" \").concat(disabled || !isAvailable ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: pkg.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-300 text-sm\",\n                                                        children: pkg.amount\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm mt-1\",\n                                                        children: pkg.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    (()=>{\n                                                        // Digital products with codes\n                                                        if (pkg.digitalCodes && pkg.digitalCodes.length > 0) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(availableCodes === 0 ? \"text-red-400\" : availableCodes < 5 ? \"text-yellow-400\" : \"text-green-400\"),\n                                                                children: availableCodes === 0 ? \"نفدت الكمية\" : availableCodes < 5 ? \"متبقي \".concat(availableCodes) : \"متوفر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        }\n                                                        // Products with manual quantity limits\n                                                        if (pkg.quantityLimit !== undefined && pkg.quantityLimit !== null) {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs mt-1 \".concat(pkg.quantityLimit === 0 ? \"text-red-400\" : pkg.quantityLimit < 5 ? \"text-yellow-400\" : \"text-green-400\"),\n                                                                children: pkg.quantityLimit === 0 ? \"نفدت الكمية\" : pkg.quantityLimit < 5 ? \"متبقي \".concat(pkg.quantityLimit) : \"متوفر\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 29\n                                                            }, this);\n                                                        }\n                                                        // Unlimited digital products/services - show as available\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs mt-1 text-green-400\",\n                                                            children: \"متوفر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 27\n                                                        }, this);\n                                                    })()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    enhancedPkg.hasDiscount && pkg.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm line-through\",\n                                                        children: formatPrice(pkg.originalPrice)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: formatPrice(pkg.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    enhancedPkg.hasDiscount && enhancedPkg.discountPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"destructive\",\n                                                        className: \"mt-1\",\n                                                        children: [\n                                                            \"خصم \",\n                                                            enhancedPkg.discountPercentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, this)\n                                }, pkg.id, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            visibleFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white\",\n                            children: \"معلومات إضافية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: visibleFields.map(renderCustomField)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 354,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 350,\n                columnNumber: 9\n            }, this),\n            selectedPackage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSubmit,\n                        disabled: disabled || isLoading,\n                        className: \"w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold py-4 text-lg\",\n                        size: \"lg\",\n                        children: isLoading ? \"جاري المعالجة...\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                template.processingType === \"instant\" ? \"اشتري الآن\" : \"أضف للسلة\",\n                                showPricing && \" - \".concat(formatPrice(totalPrice))\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 366,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 365,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 364,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"0g13qWIc+cKc3tL01+RBo0otGh8=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrencyConverter\n    ];\n});\n_c = SimpleProductForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvZHVjdHMvU2ltcGxlUHJvZHVjdEZvcm0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFa0Q7QUFDNkI7QUFDaEM7QUFDRjtBQUNxQjtBQUNvRTtBQUNoRjtBQUNXO0FBVzFELFNBQVNZLGtCQUFrQixLQU9UO1FBUFMsRUFDaENDLFFBQVEsRUFDUkMsUUFBUSxFQUNSQyxRQUFRLEVBQ1JDLGNBQWMsSUFBSSxFQUNsQkMsV0FBVyxLQUFLLEVBQ2hCQyxZQUFZLEVBQUUsRUFDUyxHQVBTOztJQVFoQyxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUduQiwrQ0FBUUEsQ0FBd0I7SUFDOUUsTUFBTSxDQUFDb0IsVUFBVUMsWUFBWSxHQUFHckIsK0NBQVFBLENBQUM7SUFDekMsTUFBTSxDQUFDc0IsbUJBQW1CQyxxQkFBcUIsR0FBR3ZCLCtDQUFRQSxDQUFzQixDQUFDO0lBQ2pGLE1BQU0sQ0FBQ3dCLGFBQWFDLGVBQWUsR0FBR3pCLCtDQUFRQSxDQUF5QixDQUFDO0lBQ3hFLE1BQU0sQ0FBQzBCLFdBQVdDLGFBQWEsR0FBRzNCLCtDQUFRQSxDQUFDO0lBRTNDLE1BQU0sRUFBRTRCLFlBQVksRUFBRSxHQUFHbEIsK0VBQW9CQTtJQUU3Qyx3QkFBd0I7SUFDeEIsTUFBTW1CLHNCQUFzQjtRQUMxQixJQUFJLENBQUNYLGlCQUFpQixPQUFPO1FBQzdCLE9BQU9BLGdCQUFnQlksS0FBSyxHQUFHVjtJQUNqQztJQUVBLDZCQUE2QjtJQUM3QixNQUFNVyxjQUFjLENBQUNEO1FBQ25CLE1BQU1FLGlCQUFpQkosYUFBYUUsT0FBTyxPQUFPaEI7UUFDbEQsT0FBT0wsb0VBQWNBLENBQUN1QixnQkFBZ0JsQjtJQUN4QztJQUVBLDJCQUEyQjtJQUMzQixNQUFNbUIsc0JBQXNCLENBQUNDO1FBQzNCZixtQkFBbUJlO1FBQ25CVCxlQUFlLENBQUMsR0FBRyxvQ0FBb0M7O0lBQ3pEO0lBRUEsOEJBQThCO0lBQzlCLE1BQU1VLG9CQUFvQixDQUFDQyxXQUFtQkM7UUFDNUNkLHFCQUFxQmUsQ0FBQUEsT0FBUztnQkFDNUIsR0FBR0EsSUFBSTtnQkFDUCxDQUFDRixVQUFVLEVBQUVDO1lBQ2Y7UUFFQSxxREFBcUQ7UUFDckQsSUFBSWIsV0FBVyxDQUFDWSxVQUFVLEVBQUU7WUFDMUJYLGVBQWVhLENBQUFBLE9BQVM7b0JBQ3RCLEdBQUdBLElBQUk7b0JBQ1AsQ0FBQ0YsVUFBVSxFQUFFO2dCQUNmO1FBQ0Y7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNRyxpQkFBaUI7UUFDckIsTUFBTUMsU0FBaUMsQ0FBQztRQUN4QyxNQUFNQyxlQUFlN0IsU0FBUzhCLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxRQUFRO1FBRTNESixhQUFhSyxPQUFPLENBQUNDLENBQUFBO1lBQ25CLE1BQU1WLFFBQVFmLGlCQUFpQixDQUFDeUIsTUFBTUMsSUFBSSxDQUFDO1lBRTNDLElBQUlELE1BQU1FLFFBQVEsSUFBSyxFQUFDWixTQUFTQSxNQUFNYSxRQUFRLEdBQUdDLElBQUksT0FBTyxFQUFDLEdBQUk7Z0JBQ2hFWCxNQUFNLENBQUNPLE1BQU1DLElBQUksQ0FBQyxHQUFHLEdBQWUsT0FBWkQsTUFBTUssS0FBSyxFQUFDO1lBQ3RDO1lBRUEsSUFBSWYsU0FBU1UsTUFBTU0sSUFBSSxLQUFLLFNBQVM7Z0JBQ25DLE1BQU1DLGFBQWE7Z0JBQ25CLElBQUksQ0FBQ0EsV0FBV0MsSUFBSSxDQUFDbEIsUUFBUTtvQkFDM0JHLE1BQU0sQ0FBQ08sTUFBTUMsSUFBSSxDQUFDLEdBQUc7Z0JBQ3ZCO1lBQ0Y7WUFFQSxJQUFJWCxTQUFTVSxNQUFNTSxJQUFJLEtBQUssVUFBVTtnQkFDcEMsSUFBSUcsTUFBTUMsT0FBT3BCLFNBQVM7b0JBQ3hCRyxNQUFNLENBQUNPLE1BQU1DLElBQUksQ0FBQyxHQUFHO2dCQUN2QjtZQUNGO1FBQ0Y7UUFFQSxPQUFPUjtJQUNUO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU1rQixlQUFlO1FBQ25CLElBQUksQ0FBQ3hDLGlCQUFpQjtZQUNwQnlDLE1BQU07WUFDTjtRQUNGO1FBRUEsNkJBQTZCO1FBQzdCLE1BQU1uQixTQUFTRDtRQUVmLElBQUlxQixPQUFPQyxJQUFJLENBQUNyQixRQUFRc0IsTUFBTSxHQUFHLEdBQUc7WUFDbENyQyxlQUFlZTtZQUNmO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZiLGFBQWE7WUFFYixNQUFNb0MsV0FBNEI7Z0JBQ2hDQyxZQUFZcEQsU0FBU3FELEVBQUU7Z0JBQ3ZCL0M7Z0JBQ0FFO2dCQUNBOEMsY0FBYzVDO2dCQUNkNkMsWUFBWXRDO2dCQUNaZjtZQUNGO1lBRUEsTUFBTUQsU0FBU2tEO1FBQ2pCLEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMEJBQTBCQTtZQUN4Q1QsTUFBTTtRQUNSLFNBQVU7WUFDUmhDLGFBQWE7UUFDZjtJQUNGO0lBRUEsc0JBQXNCO0lBQ3RCLE1BQU0yQyxvQkFBb0IsQ0FBQ3ZCO1lBb0NoQkE7UUFuQ1QsTUFBTVYsUUFBUWYsaUJBQWlCLENBQUN5QixNQUFNQyxJQUFJLENBQUMsSUFBSTtRQUMvQyxNQUFNb0IsUUFBUTVDLFdBQVcsQ0FBQ3VCLE1BQU1DLElBQUksQ0FBQztRQUVyQyxxQkFDRSw4REFBQ3VCO1lBQW1CdEQsV0FBVTs7OEJBQzVCLDhEQUFDbUM7b0JBQU1uQyxXQUFVOzt3QkFDZDhCLE1BQU1LLEtBQUs7d0JBQ1hMLE1BQU1FLFFBQVEsa0JBQUksOERBQUN1Qjs0QkFBS3ZELFdBQVU7c0NBQW9COzs7Ozs7Ozs7Ozs7Z0JBR3hEOEIsTUFBTU0sSUFBSSxLQUFLLG1DQUNkLDhEQUFDb0I7b0JBQ0NwQixNQUFLO29CQUNMaEIsT0FBT0E7b0JBQ1BxQyxVQUFVLENBQUNDLElBQU14QyxrQkFBa0JZLE1BQU1DLElBQUksRUFBRTJCLEVBQUVDLE1BQU0sQ0FBQ3ZDLEtBQUs7b0JBQzdEd0MsYUFBYTlCLE1BQU04QixXQUFXO29CQUM5QjdELFVBQVVBO29CQUNWQyxXQUFXLHdJQUVWLE9BRENtRCxRQUFRLG1CQUFtQjs7Ozs7O2dCQUtoQ3JCLE1BQU1NLElBQUksS0FBSyw0QkFDZCw4REFBQ3lCO29CQUNDekMsT0FBT0E7b0JBQ1BxQyxVQUFVLENBQUNDLElBQU14QyxrQkFBa0JZLE1BQU1DLElBQUksRUFBRTJCLEVBQUVDLE1BQU0sQ0FBQ3ZDLEtBQUs7b0JBQzdEckIsVUFBVUE7b0JBQ1ZDLFdBQVcsa0hBRVYsT0FEQ21ELFFBQVEsbUJBQW1COztzQ0FHN0IsOERBQUNXOzRCQUFPMUMsT0FBTTtzQ0FDWFUsTUFBTThCLFdBQVcsSUFBSSxRQUFvQixPQUFaOUIsTUFBTUssS0FBSzs7Ozs7O3lCQUUxQ0wsaUJBQUFBLE1BQU1pQyxPQUFPLGNBQWJqQyxxQ0FBQUEsZUFBZWtDLEdBQUcsQ0FBQyxDQUFDRix1QkFDbkIsOERBQUNBO2dDQUF1QjFDLE9BQU8wQyxPQUFPMUMsS0FBSzswQ0FDeEMwQyxPQUFPM0IsS0FBSzsrQkFERjJCLE9BQU9kLEVBQUU7Ozs7Ozs7Ozs7O2dCQVEzQmxCLE1BQU1NLElBQUksS0FBSyx3QkFDZCw4REFBQ29CO29CQUNDcEIsTUFBSztvQkFDTGhCLE9BQU9BO29CQUNQcUMsVUFBVSxDQUFDQyxJQUFNeEMsa0JBQWtCWSxNQUFNQyxJQUFJLEVBQUUyQixFQUFFQyxNQUFNLENBQUN2QyxLQUFLO29CQUM3RHdDLGFBQWE5QixNQUFNOEIsV0FBVztvQkFDOUI3RCxVQUFVQTtvQkFDVkMsV0FBVyx3SUFFVixPQURDbUQsUUFBUSxtQkFBbUI7Ozs7OztnQkFLaENyQixNQUFNTSxJQUFJLEtBQUsseUJBQ2QsOERBQUNvQjtvQkFDQ3BCLE1BQUs7b0JBQ0xoQixPQUFPQTtvQkFDUHFDLFVBQVUsQ0FBQ0MsSUFBTXhDLGtCQUFrQlksTUFBTUMsSUFBSSxFQUFFMkIsRUFBRUMsTUFBTSxDQUFDdkMsS0FBSztvQkFDN0R3QyxhQUFhOUIsTUFBTThCLFdBQVc7b0JBQzlCN0QsVUFBVUE7b0JBQ1ZDLFdBQVcsd0lBRVYsT0FEQ21ELFFBQVEsbUJBQW1COzs7Ozs7Z0JBS2hDckIsTUFBTU0sSUFBSSxLQUFLLDBCQUNkLDhEQUFDb0I7b0JBQ0NwQixNQUFLO29CQUNMaEIsT0FBT0E7b0JBQ1BxQyxVQUFVLENBQUNDLElBQU14QyxrQkFBa0JZLE1BQU1DLElBQUksRUFBRTJCLEVBQUVDLE1BQU0sQ0FBQ3ZDLEtBQUs7b0JBQzdEd0MsYUFBYTlCLE1BQU04QixXQUFXO29CQUM5QjdELFVBQVVBO29CQUNWQyxXQUFXLHdJQUVWLE9BRENtRCxRQUFRLG1CQUFtQjs7Ozs7O2dCQUtoQ0EsdUJBQ0MsOERBQUNjO29CQUFFakUsV0FBVTs4QkFBd0JtRDs7Ozs7OztXQWhGL0JyQixNQUFNa0IsRUFBRTs7Ozs7SUFvRnRCO0lBRUEsaUNBQWlDO0lBQ2pDLE1BQU1FLGFBQWF0QztJQUNuQixNQUFNc0QsZ0JBQWdCdkUsU0FBUzhCLE1BQU0sQ0FBQ0MsTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxRQUFRO0lBRTVELHFCQUNFLDhEQUFDMEI7UUFBSXRELFdBQVcsYUFBdUIsT0FBVkE7OzBCQUUzQiw4REFBQ2hCLHFEQUFJQTtnQkFBQ2dCLFdBQVU7O2tDQUNkLDhEQUFDZCwyREFBVUE7a0NBQ1QsNEVBQUNDLDBEQUFTQTs0QkFBQ2EsV0FBVTs7OENBQ25CLDhEQUFDViwrRUFBR0E7b0NBQUNVLFdBQVU7Ozs7OztnQ0FBWTs7Ozs7Ozs7Ozs7O2tDQUkvQiw4REFBQ2YsNERBQVdBO2tDQUNWLDRFQUFDcUU7NEJBQUl0RCxXQUFVO3NDQUNaTCxTQUFTd0UsUUFBUSxDQUFDSCxHQUFHLENBQUMsQ0FBQy9DO29DQUVDQTtnQ0FEdkIsTUFBTW1ELGNBQWM3RSwwRUFBOEJBLENBQUMwQjtnQ0FDbkQsTUFBTW9ELGlCQUFpQnBELEVBQUFBLG9CQUFBQSxJQUFJcUQsWUFBWSxjQUFoQnJELHdDQUFBQSxrQkFBa0JTLE1BQU0sQ0FBQzZDLENBQUFBLE9BQVEsQ0FBQ0EsS0FBS0MsSUFBSSxFQUFFM0IsTUFBTSxLQUFJO2dDQUU5RSwwREFBMEQ7Z0NBQzFELE1BQU00QixjQUFjLENBQUM7b0NBQ25CLElBQUksQ0FBQ3hELElBQUlXLFFBQVEsRUFBRSxPQUFPO29DQUUxQiw4QkFBOEI7b0NBQzlCLElBQUlYLElBQUlxRCxZQUFZLElBQUlyRCxJQUFJcUQsWUFBWSxDQUFDekIsTUFBTSxHQUFHLEdBQUc7d0NBQ25ELE9BQU93QixpQkFBaUI7b0NBQzFCO29DQUVBLHVDQUF1QztvQ0FDdkMsSUFBSXBELElBQUl5RCxhQUFhLEtBQUtDLGFBQWExRCxJQUFJeUQsYUFBYSxLQUFLLE1BQU07d0NBQ2pFLE9BQU96RCxJQUFJeUQsYUFBYSxHQUFHO29DQUM3QjtvQ0FFQSw0REFBNEQ7b0NBQzVELE9BQU87Z0NBQ1Q7Z0NBRUEscUJBQ0UsOERBQUNwQjtvQ0FFQ3NCLFNBQVMsSUFBTSxDQUFDN0UsWUFBWTBFLGVBQWV6RCxvQkFBb0JDO29DQUMvRGpCLFdBQVcsa0VBTVBELE9BTEZFLENBQUFBLDRCQUFBQSxzQ0FBQUEsZ0JBQWlCK0MsRUFBRSxNQUFLL0IsSUFBSStCLEVBQUUsR0FDMUIsbUNBQ0F5QixjQUNFLDREQUNBLG1DQUNQLEtBQW1FLE9BQWhFMUUsWUFBWSxDQUFDMEUsY0FBYyxrQ0FBa0M7OENBRWpFLDRFQUFDbkI7d0NBQUl0RCxXQUFVOzswREFDYiw4REFBQ3NEO2dEQUFJdEQsV0FBVTs7a0VBQ2IsOERBQUM2RTt3REFBRzdFLFdBQVU7a0VBQTRCaUIsSUFBSWMsSUFBSTs7Ozs7O29EQUNqRGQsSUFBSTZELE1BQU0sa0JBQ1QsOERBQUNiO3dEQUFFakUsV0FBVTtrRUFBMEJpQixJQUFJNkQsTUFBTTs7Ozs7O29EQUVsRDdELElBQUk4RCxXQUFXLGtCQUNkLDhEQUFDZDt3REFBRWpFLFdBQVU7a0VBQStCaUIsSUFBSThELFdBQVc7Ozs7OztvREFJM0Q7d0RBQ0EsOEJBQThCO3dEQUM5QixJQUFJOUQsSUFBSXFELFlBQVksSUFBSXJELElBQUlxRCxZQUFZLENBQUN6QixNQUFNLEdBQUcsR0FBRzs0REFDbkQscUJBQ0UsOERBQUNvQjtnRUFBRWpFLFdBQVcsZ0JBR2IsT0FGQ3FFLG1CQUFtQixJQUFJLGlCQUN2QkEsaUJBQWlCLElBQUksb0JBQW9COzBFQUV4Q0EsbUJBQW1CLElBQUksZ0JBQ3ZCQSxpQkFBaUIsSUFBSSxTQUF3QixPQUFmQSxrQkFBbUI7Ozs7Ozt3REFHeEQ7d0RBRUEsdUNBQXVDO3dEQUN2QyxJQUFJcEQsSUFBSXlELGFBQWEsS0FBS0MsYUFBYTFELElBQUl5RCxhQUFhLEtBQUssTUFBTTs0REFDakUscUJBQ0UsOERBQUNUO2dFQUFFakUsV0FBVyxnQkFHYixPQUZDaUIsSUFBSXlELGFBQWEsS0FBSyxJQUFJLGlCQUMxQnpELElBQUl5RCxhQUFhLEdBQUcsSUFBSSxvQkFBb0I7MEVBRTNDekQsSUFBSXlELGFBQWEsS0FBSyxJQUFJLGdCQUMxQnpELElBQUl5RCxhQUFhLEdBQUcsSUFBSSxTQUEyQixPQUFsQnpELElBQUl5RCxhQUFhLElBQUs7Ozs7Ozt3REFHOUQ7d0RBRUEsMERBQTBEO3dEQUMxRCxxQkFDRSw4REFBQ1Q7NERBQUVqRSxXQUFVO3NFQUE4Qjs7Ozs7O29EQUkvQzs7Ozs7OzswREFHRiw4REFBQ3NEO2dEQUFJdEQsV0FBVTs7b0RBQ1pvRSxZQUFZWSxXQUFXLElBQUkvRCxJQUFJZ0UsYUFBYSxrQkFDM0MsOERBQUNoQjt3REFBRWpFLFdBQVU7a0VBQ1ZjLFlBQVlHLElBQUlnRSxhQUFhOzs7Ozs7a0VBR2xDLDhEQUFDaEI7d0RBQUVqRSxXQUFVO2tFQUNWYyxZQUFZRyxJQUFJSixLQUFLOzs7Ozs7b0RBRXZCdUQsWUFBWVksV0FBVyxJQUFJWixZQUFZYyxrQkFBa0Isa0JBQ3hELDhEQUFDN0YsdURBQUtBO3dEQUFDOEYsU0FBUTt3REFBY25GLFdBQVU7OzREQUFPOzREQUN2Q29FLFlBQVljLGtCQUFrQjs0REFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzttQ0FwRXZDakUsSUFBSStCLEVBQUU7Ozs7OzRCQTJFakI7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBTUxrQixjQUFjckIsTUFBTSxHQUFHLG1CQUN0Qiw4REFBQzdELHFEQUFJQTtnQkFBQ2dCLFdBQVU7O2tDQUNkLDhEQUFDZCwyREFBVUE7a0NBQ1QsNEVBQUNDLDBEQUFTQTs0QkFBQ2EsV0FBVTtzQ0FBYTs7Ozs7Ozs7Ozs7a0NBRXBDLDhEQUFDZiw0REFBV0E7a0NBQ1YsNEVBQUNxRTs0QkFBSXRELFdBQVU7c0NBQ1prRSxjQUFjRixHQUFHLENBQUNYOzs7Ozs7Ozs7Ozs7Ozs7OztZQU8xQnBELGlDQUNDLDhEQUFDakIscURBQUlBO2dCQUFDZ0IsV0FBVTswQkFDZCw0RUFBQ2YsNERBQVdBO29CQUFDZSxXQUFVOzhCQUNyQiw0RUFBQ1oseURBQU1BO3dCQUNMd0YsU0FBU25DO3dCQUNUMUMsVUFBVUEsWUFBWVU7d0JBQ3RCVCxXQUFVO3dCQUNWb0YsTUFBSztrQ0FFSjNFLFlBQ0MsbUNBRUE7O2dDQUNHZCxTQUFTMEYsY0FBYyxLQUFLLFlBQVksZUFBZTtnQ0FDdkR2RixlQUFlLE1BQThCLE9BQXhCZ0IsWUFBWW9DOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFTcEQ7R0E3V2dCeEQ7O1FBY1dELDJFQUFvQkE7OztLQWQvQkMiLCJzb3VyY2VzIjpbIkQ6XFxWUy1wcm9qZWN0c1xcdHJ5XFxhbHJheWEtc3RvcmVcXGNvbXBvbmVudHNcXHByb2R1Y3RzXFxTaW1wbGVQcm9kdWN0Rm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYmFkZ2VcIlxuaW1wb3J0IHsgU2hvcHBpbmdDYXJ0LCBaYXAsIENhbGN1bGF0b3IsIFN0YXIgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcbmltcG9ydCB7IFByb2R1Y3RUZW1wbGF0ZSwgUHJvZHVjdEZvcm1EYXRhLCBQcm9kdWN0UGFja2FnZSwgQ3VycmVuY3ksIER5bmFtaWNGaWVsZCwgZW5oYW5jZVBhY2thZ2VXaXRoRGlzY291bnRJbmZvIH0gZnJvbSBcIkAvbGliL3R5cGVzXCJcbmltcG9ydCB7IGZvcm1hdEN1cnJlbmN5IH0gZnJvbSBcIkAvbGliL2RhdGEvY3VycmVuY2llc1wiXG5pbXBvcnQgeyB1c2VDdXJyZW5jeUNvbnZlcnRlciB9IGZyb20gXCJAL2NvbnRleHRzL0N1cnJlbmN5Q29udGV4dFwiXG5cbmludGVyZmFjZSBTaW1wbGVQcm9kdWN0Rm9ybVByb3BzIHtcbiAgdGVtcGxhdGU6IFByb2R1Y3RUZW1wbGF0ZVxuICBvblN1Ym1pdDogKGZvcm1EYXRhOiBQcm9kdWN0Rm9ybURhdGEpID0+IHZvaWRcbiAgY3VycmVuY3k6IEN1cnJlbmN5XG4gIHNob3dQcmljaW5nPzogYm9vbGVhblxuICBkaXNhYmxlZD86IGJvb2xlYW5cbiAgY2xhc3NOYW1lPzogc3RyaW5nXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBTaW1wbGVQcm9kdWN0Rm9ybSh7XG4gIHRlbXBsYXRlLFxuICBvblN1Ym1pdCxcbiAgY3VycmVuY3ksXG4gIHNob3dQcmljaW5nID0gdHJ1ZSxcbiAgZGlzYWJsZWQgPSBmYWxzZSxcbiAgY2xhc3NOYW1lID0gXCJcIlxufTogU2ltcGxlUHJvZHVjdEZvcm1Qcm9wcykge1xuICBjb25zdCBbc2VsZWN0ZWRQYWNrYWdlLCBzZXRTZWxlY3RlZFBhY2thZ2VdID0gdXNlU3RhdGU8UHJvZHVjdFBhY2thZ2UgfCBudWxsPihudWxsKVxuICBjb25zdCBbcXVhbnRpdHksIHNldFF1YW50aXR5XSA9IHVzZVN0YXRlKDEpXG4gIGNvbnN0IFtjdXN0b21GaWVsZFZhbHVlcywgc2V0Q3VzdG9tRmllbGRWYWx1ZXNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgYW55Pj4oe30pXG4gIGNvbnN0IFtmaWVsZEVycm9ycywgc2V0RmllbGRFcnJvcnNdID0gdXNlU3RhdGU8UmVjb3JkPHN0cmluZywgc3RyaW5nPj4oe30pXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgXG4gIGNvbnN0IHsgY29udmVydFByaWNlIH0gPSB1c2VDdXJyZW5jeUNvbnZlcnRlcigpXG5cbiAgLy8gQ2FsY3VsYXRlIHRvdGFsIHByaWNlXG4gIGNvbnN0IGNhbGN1bGF0ZVRvdGFsUHJpY2UgPSAoKSA9PiB7XG4gICAgaWYgKCFzZWxlY3RlZFBhY2thZ2UpIHJldHVybiAwXG4gICAgcmV0dXJuIHNlbGVjdGVkUGFja2FnZS5wcmljZSAqIHF1YW50aXR5XG4gIH1cblxuICAvLyBGb3JtYXQgcHJpY2Ugd2l0aCBjdXJyZW5jeVxuICBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgY29udmVydGVkUHJpY2UgPSBjb252ZXJ0UHJpY2UocHJpY2UsIFwiVVNEXCIsIGN1cnJlbmN5KVxuICAgIHJldHVybiBmb3JtYXRDdXJyZW5jeShjb252ZXJ0ZWRQcmljZSwgY3VycmVuY3kpXG4gIH1cblxuICAvLyBIYW5kbGUgcGFja2FnZSBzZWxlY3Rpb25cbiAgY29uc3QgaGFuZGxlUGFja2FnZVNlbGVjdCA9IChwa2c6IFByb2R1Y3RQYWNrYWdlKSA9PiB7XG4gICAgc2V0U2VsZWN0ZWRQYWNrYWdlKHBrZylcbiAgICBzZXRGaWVsZEVycm9ycyh7fSkgLy8gQ2xlYXIgZXJyb3JzIHdoZW4gcGFja2FnZSBjaGFuZ2VzXG4gIH1cblxuICAvLyBIYW5kbGUgY3VzdG9tIGZpZWxkIGNoYW5nZXNcbiAgY29uc3QgaGFuZGxlRmllbGRDaGFuZ2UgPSAoZmllbGROYW1lOiBzdHJpbmcsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRDdXN0b21GaWVsZFZhbHVlcyhwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgW2ZpZWxkTmFtZV06IHZhbHVlXG4gICAgfSkpXG4gICAgXG4gICAgLy8gQ2xlYXIgZXJyb3IgZm9yIHRoaXMgZmllbGQgd2hlbiB1c2VyIHN0YXJ0cyB0eXBpbmdcbiAgICBpZiAoZmllbGRFcnJvcnNbZmllbGROYW1lXSkge1xuICAgICAgc2V0RmllbGRFcnJvcnMocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBbZmllbGROYW1lXTogXCJcIlxuICAgICAgfSkpXG4gICAgfVxuICB9XG5cbiAgLy8gVmFsaWRhdGUgZm9ybSBmaWVsZHNcbiAgY29uc3QgdmFsaWRhdGVGaWVsZHMgPSAoKSA9PiB7XG4gICAgY29uc3QgZXJyb3JzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge31cbiAgICBjb25zdCBhY3RpdmVGaWVsZHMgPSB0ZW1wbGF0ZS5maWVsZHMuZmlsdGVyKGYgPT4gZi5pc0FjdGl2ZSlcbiAgICBcbiAgICBhY3RpdmVGaWVsZHMuZm9yRWFjaChmaWVsZCA9PiB7XG4gICAgICBjb25zdCB2YWx1ZSA9IGN1c3RvbUZpZWxkVmFsdWVzW2ZpZWxkLm5hbWVdXG4gICAgICBcbiAgICAgIGlmIChmaWVsZC5yZXF1aXJlZCAmJiAoIXZhbHVlIHx8IHZhbHVlLnRvU3RyaW5nKCkudHJpbSgpID09PSBcIlwiKSkge1xuICAgICAgICBlcnJvcnNbZmllbGQubmFtZV0gPSBgJHtmaWVsZC5sYWJlbH0g2YXYt9mE2YjYqGBcbiAgICAgIH1cbiAgICAgIFxuICAgICAgaWYgKHZhbHVlICYmIGZpZWxkLnR5cGUgPT09IFwiZW1haWxcIikge1xuICAgICAgICBjb25zdCBlbWFpbFJlZ2V4ID0gL15bXlxcc0BdK0BbXlxcc0BdK1xcLlteXFxzQF0rJC9cbiAgICAgICAgaWYgKCFlbWFpbFJlZ2V4LnRlc3QodmFsdWUpKSB7XG4gICAgICAgICAgZXJyb3JzW2ZpZWxkLm5hbWVdID0gXCLZitix2KzZiSDYpdiv2K7Yp9mEINio2LHZitivINil2YTZg9iq2LHZiNmG2Yog2LXYrdmK2K1cIlxuICAgICAgICB9XG4gICAgICB9XG4gICAgICBcbiAgICAgIGlmICh2YWx1ZSAmJiBmaWVsZC50eXBlID09PSBcIm51bWJlclwiKSB7XG4gICAgICAgIGlmIChpc05hTihOdW1iZXIodmFsdWUpKSkge1xuICAgICAgICAgIGVycm9yc1tmaWVsZC5uYW1lXSA9IFwi2YrYsdis2Ykg2KXYr9iu2KfZhCDYsdmC2YUg2LXYrdmK2K1cIlxuICAgICAgICB9XG4gICAgICB9XG4gICAgfSlcbiAgICBcbiAgICByZXR1cm4gZXJyb3JzXG4gIH1cblxuICAvLyBIYW5kbGUgZm9ybSBzdWJtaXNzaW9uXG4gIGNvbnN0IGhhbmRsZVN1Ym1pdCA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXNlbGVjdGVkUGFja2FnZSkge1xuICAgICAgYWxlcnQoXCLZitix2KzZiSDYp9iu2KrZitin2LEg2K3YstmF2KlcIilcbiAgICAgIHJldHVyblxuICAgIH1cbiAgICBcbiAgICAvLyBWYWxpZGF0ZSBhbGwgY3VzdG9tIGZpZWxkc1xuICAgIGNvbnN0IGVycm9ycyA9IHZhbGlkYXRlRmllbGRzKClcbiAgICBcbiAgICBpZiAoT2JqZWN0LmtleXMoZXJyb3JzKS5sZW5ndGggPiAwKSB7XG4gICAgICBzZXRGaWVsZEVycm9ycyhlcnJvcnMpXG4gICAgICByZXR1cm5cbiAgICB9XG4gICAgXG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgICAgXG4gICAgICBjb25zdCBmb3JtRGF0YTogUHJvZHVjdEZvcm1EYXRhID0ge1xuICAgICAgICB0ZW1wbGF0ZUlkOiB0ZW1wbGF0ZS5pZCxcbiAgICAgICAgc2VsZWN0ZWRQYWNrYWdlLFxuICAgICAgICBxdWFudGl0eSxcbiAgICAgICAgY3VzdG9tRmllbGRzOiBjdXN0b21GaWVsZFZhbHVlcyxcbiAgICAgICAgdG90YWxQcmljZTogY2FsY3VsYXRlVG90YWxQcmljZSgpLFxuICAgICAgICBjdXJyZW5jeVxuICAgICAgfVxuICAgICAgXG4gICAgICBhd2FpdCBvblN1Ym1pdChmb3JtRGF0YSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHN1Ym1pdHRpbmcgZm9ybTpcIiwgZXJyb3IpXG4gICAgICBhbGVydChcItit2K/YqyDYrti32KMg2KPYq9mG2KfYoSDYpdix2LPYp9mEINin2YTYt9mE2KhcIilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFJlbmRlciBjdXN0b20gZmllbGRcbiAgY29uc3QgcmVuZGVyQ3VzdG9tRmllbGQgPSAoZmllbGQ6IER5bmFtaWNGaWVsZCkgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gY3VzdG9tRmllbGRWYWx1ZXNbZmllbGQubmFtZV0gfHwgXCJcIlxuICAgIGNvbnN0IGVycm9yID0gZmllbGRFcnJvcnNbZmllbGQubmFtZV1cbiAgICBcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBrZXk9e2ZpZWxkLmlkfSBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1zbGF0ZS0zMDBcIj5cbiAgICAgICAgICB7ZmllbGQubGFiZWx9XG4gICAgICAgICAge2ZpZWxkLnJlcXVpcmVkICYmIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCBtbC0xXCI+Kjwvc3Bhbj59XG4gICAgICAgIDwvbGFiZWw+XG4gICAgICAgIFxuICAgICAgICB7ZmllbGQudHlwZSA9PT0gXCJ1bml2ZXJzYWxfaW5wdXRcIiAmJiAoXG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICB2YWx1ZT17dmFsdWV9XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpZWxkQ2hhbmdlKGZpZWxkLm5hbWUsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtmaWVsZC5wbGFjZWhvbGRlcn1cbiAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cbiAgICAgICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zbGF0ZS03MDAgYm9yZGVyIHJvdW5kZWQtbGcgcHgtMyBweS0yIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItc2xhdGUtNDAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCAke1xuICAgICAgICAgICAgICBlcnJvciA/IFwiYm9yZGVyLXJlZC01MDBcIiA6IFwiYm9yZGVyLXNsYXRlLTYwMFwiXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuICAgICAgICBcbiAgICAgICAge2ZpZWxkLnR5cGUgPT09IFwiZHJvcGRvd25cIiAmJiAoXG4gICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgdmFsdWU9e3ZhbHVlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWVsZENoYW5nZShmaWVsZC5uYW1lLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgYmctc2xhdGUtNzAwIGJvcmRlciByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCAke1xuICAgICAgICAgICAgICBlcnJvciA/IFwiYm9yZGVyLXJlZC01MDBcIiA6IFwiYm9yZGVyLXNsYXRlLTYwMFwiXG4gICAgICAgICAgICB9YH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+XG4gICAgICAgICAgICAgIHtmaWVsZC5wbGFjZWhvbGRlciB8fCBg2KfYrtiq2LEgJHtmaWVsZC5sYWJlbH1gfVxuICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICB7ZmllbGQub3B0aW9ucz8ubWFwKChvcHRpb24pID0+IChcbiAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e29wdGlvbi5pZH0gdmFsdWU9e29wdGlvbi52YWx1ZX0+XG4gICAgICAgICAgICAgICAge29wdGlvbi5sYWJlbH1cbiAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHsvKiBMZWdhY3kgc3VwcG9ydCBmb3Igb2xkIGZpZWxkIHR5cGVzICovfVxuICAgICAgICB7ZmllbGQudHlwZSA9PT0gXCJ0ZXh0XCIgJiYgKFxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgdmFsdWU9e3ZhbHVlfVxuICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWVsZENoYW5nZShmaWVsZC5uYW1lLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICBwbGFjZWhvbGRlcj17ZmllbGQucGxhY2Vob2xkZXJ9XG4gICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWR9XG4gICAgICAgICAgICBjbGFzc05hbWU9e2B3LWZ1bGwgYmctc2xhdGUtNzAwIGJvcmRlciByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLXNsYXRlLTQwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgJHtcbiAgICAgICAgICAgICAgZXJyb3IgPyBcImJvcmRlci1yZWQtNTAwXCIgOiBcImJvcmRlci1zbGF0ZS02MDBcIlxuICAgICAgICAgICAgfWB9XG4gICAgICAgICAgLz5cbiAgICAgICAgKX1cbiAgICAgICAgXG4gICAgICAgIHtmaWVsZC50eXBlID09PSBcImVtYWlsXCIgJiYgKFxuICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgdHlwZT1cImVtYWlsXCJcbiAgICAgICAgICAgIHZhbHVlPXt2YWx1ZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmllbGRDaGFuZ2UoZmllbGQubmFtZSwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2ZpZWxkLnBsYWNlaG9sZGVyfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGJnLXNsYXRlLTcwMCBib3JkZXIgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1zbGF0ZS00MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwICR7XG4gICAgICAgICAgICAgIGVycm9yID8gXCJib3JkZXItcmVkLTUwMFwiIDogXCJib3JkZXItc2xhdGUtNjAwXCJcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICB7ZmllbGQudHlwZSA9PT0gXCJudW1iZXJcIiAmJiAoXG4gICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgIHZhbHVlPXt2YWx1ZX1cbiAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlRmllbGRDaGFuZ2UoZmllbGQubmFtZSwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgcGxhY2Vob2xkZXI9e2ZpZWxkLnBsYWNlaG9sZGVyfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2Rpc2FibGVkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPXtgdy1mdWxsIGJnLXNsYXRlLTcwMCBib3JkZXIgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1zbGF0ZS00MDAgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwICR7XG4gICAgICAgICAgICAgIGVycm9yID8gXCJib3JkZXItcmVkLTUwMFwiIDogXCJib3JkZXItc2xhdGUtNjAwXCJcbiAgICAgICAgICAgIH1gfVxuICAgICAgICAgIC8+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtcmVkLTQwMCB0ZXh0LXNtXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgKX1cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIC8vIENhbGN1bGF0ZSB2YWx1ZXMgZm9yIHJlbmRlcmluZ1xuICBjb25zdCB0b3RhbFByaWNlID0gY2FsY3VsYXRlVG90YWxQcmljZSgpXG4gIGNvbnN0IHZpc2libGVGaWVsZHMgPSB0ZW1wbGF0ZS5maWVsZHMuZmlsdGVyKGYgPT4gZi5pc0FjdGl2ZSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtgc3BhY2UteS02ICR7Y2xhc3NOYW1lfWB9PlxuICAgICAgey8qIFBhY2thZ2UgU2VsZWN0aW9uICovfVxuICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwLzUwIGJvcmRlci1zbGF0ZS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtd2hpdGUgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgIDxaYXAgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICDYp9iu2KrYsSDYp9mE2K3YstmF2KlcbiAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdhcC00XCI+XG4gICAgICAgICAgICB7dGVtcGxhdGUucGFja2FnZXMubWFwKChwa2cpID0+IHtcbiAgICAgICAgICAgICAgY29uc3QgZW5oYW5jZWRQa2cgPSBlbmhhbmNlUGFja2FnZVdpdGhEaXNjb3VudEluZm8ocGtnKVxuICAgICAgICAgICAgICBjb25zdCBhdmFpbGFibGVDb2RlcyA9IHBrZy5kaWdpdGFsQ29kZXM/LmZpbHRlcihjb2RlID0+ICFjb2RlLnVzZWQpLmxlbmd0aCB8fCAwXG5cbiAgICAgICAgICAgICAgLy8gRW5oYW5jZWQgYXZhaWxhYmlsaXR5IGxvZ2ljIGZvciBkaWZmZXJlbnQgcHJvZHVjdCB0eXBlc1xuICAgICAgICAgICAgICBjb25zdCBpc0F2YWlsYWJsZSA9ICgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaWYgKCFwa2cuaXNBY3RpdmUpIHJldHVybiBmYWxzZVxuXG4gICAgICAgICAgICAgICAgLy8gRGlnaXRhbCBwcm9kdWN0cyB3aXRoIGNvZGVzXG4gICAgICAgICAgICAgICAgaWYgKHBrZy5kaWdpdGFsQ29kZXMgJiYgcGtnLmRpZ2l0YWxDb2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gYXZhaWxhYmxlQ29kZXMgPiAwXG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gUHJvZHVjdHMgd2l0aCBtYW51YWwgcXVhbnRpdHkgbGltaXRzXG4gICAgICAgICAgICAgICAgaWYgKHBrZy5xdWFudGl0eUxpbWl0ICE9PSB1bmRlZmluZWQgJiYgcGtnLnF1YW50aXR5TGltaXQgIT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgIHJldHVybiBwa2cucXVhbnRpdHlMaW1pdCA+IDBcbiAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAvLyBVbmxpbWl0ZWQgZGlnaXRhbCBwcm9kdWN0cy9zZXJ2aWNlcyAobm8gY29kZXMsIG5vIGxpbWl0cylcbiAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZVxuICAgICAgICAgICAgICB9KSgpXG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgIGtleT17cGtnLmlkfVxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gIWRpc2FibGVkICYmIGlzQXZhaWxhYmxlICYmIGhhbmRsZVBhY2thZ2VTZWxlY3QocGtnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHJlbGF0aXZlIHAtNCByb3VuZGVkLWxnIGJvcmRlci0yIGN1cnNvci1wb2ludGVyIHRyYW5zaXRpb24tYWxsICR7XG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUGFja2FnZT8uaWQgPT09IHBrZy5pZFxuICAgICAgICAgICAgICAgICAgICAgID8gXCJib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MDAvMTBcIlxuICAgICAgICAgICAgICAgICAgICAgIDogaXNBdmFpbGFibGUgXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiYm9yZGVyLXNsYXRlLTYwMCBiZy1zbGF0ZS03MDAvMzAgaG92ZXI6Ym9yZGVyLXNsYXRlLTUwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICA6IFwiYm9yZGVyLXJlZC02MDAvNTAgYmctcmVkLTkwMC8yMFwiXG4gICAgICAgICAgICAgICAgICB9ICR7ZGlzYWJsZWQgfHwgIWlzQXZhaWxhYmxlID8gXCJvcGFjaXR5LTUwIGN1cnNvci1ub3QtYWxsb3dlZFwiIDogXCJcIn1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtc3RhcnRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlXCI+e3BrZy5uYW1lfTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAge3BrZy5hbW91bnQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS0zMDAgdGV4dC1zbVwiPntwa2cuYW1vdW50fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIHtwa2cuZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbGF0ZS00MDAgdGV4dC1zbSBtdC0xXCI+e3BrZy5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgICB7LyogRW5oYW5jZWQgU3RvY2sgU3RhdHVzICovfVxuICAgICAgICAgICAgICAgICAgICAgIHsoKCkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gRGlnaXRhbCBwcm9kdWN0cyB3aXRoIGNvZGVzXG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAocGtnLmRpZ2l0YWxDb2RlcyAmJiBwa2cuZGlnaXRhbENvZGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGF2YWlsYWJsZUNvZGVzID09PSAwID8gXCJ0ZXh0LXJlZC00MDBcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhdmFpbGFibGVDb2RlcyA8IDUgPyBcInRleHQteWVsbG93LTQwMFwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2F2YWlsYWJsZUNvZGVzID09PSAwID8gXCLZhtmB2K/YqiDYp9mE2YPZhdmK2KlcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgYXZhaWxhYmxlQ29kZXMgPCA1ID8gYNmF2KrYqNmC2YogJHthdmFpbGFibGVDb2Rlc31gIDogXCLZhdiq2YjZgdixXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFByb2R1Y3RzIHdpdGggbWFudWFsIHF1YW50aXR5IGxpbWl0c1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHBrZy5xdWFudGl0eUxpbWl0ICE9PSB1bmRlZmluZWQgJiYgcGtnLnF1YW50aXR5TGltaXQgIT09IG51bGwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9e2B0ZXh0LXhzIG10LTEgJHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBrZy5xdWFudGl0eUxpbWl0ID09PSAwID8gXCJ0ZXh0LXJlZC00MDBcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBwa2cucXVhbnRpdHlMaW1pdCA8IDUgPyBcInRleHQteWVsbG93LTQwMFwiIDogXCJ0ZXh0LWdyZWVuLTQwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3BrZy5xdWFudGl0eUxpbWl0ID09PSAwID8gXCLZhtmB2K/YqiDYp9mE2YPZhdmK2KlcIiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcGtnLnF1YW50aXR5TGltaXQgPCA1ID8gYNmF2KrYqNmC2YogJHtwa2cucXVhbnRpdHlMaW1pdH1gIDogXCLZhdiq2YjZgdixXCJ9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICApXG4gICAgICAgICAgICAgICAgICAgICAgICB9XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFVubGltaXRlZCBkaWdpdGFsIHByb2R1Y3RzL3NlcnZpY2VzIC0gc2hvdyBhcyBhdmFpbGFibGVcbiAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgbXQtMSB0ZXh0LWdyZWVuLTQwMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgINmF2KrZiNmB2LFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgKVxuICAgICAgICAgICAgICAgICAgICAgIH0pKCl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+XG4gICAgICAgICAgICAgICAgICAgICAge2VuaGFuY2VkUGtnLmhhc0Rpc2NvdW50ICYmIHBrZy5vcmlnaW5hbFByaWNlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc2xhdGUtNDAwIHRleHQtc20gbGluZS10aHJvdWdoXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtmb3JtYXRQcmljZShwa2cub3JpZ2luYWxQcmljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlIGZvbnQtYm9sZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdFByaWNlKHBrZy5wcmljZSl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgIHtlbmhhbmNlZFBrZy5oYXNEaXNjb3VudCAmJiBlbmhhbmNlZFBrZy5kaXNjb3VudFBlcmNlbnRhZ2UgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiIGNsYXNzTmFtZT1cIm10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAg2K7YtdmFIHtlbmhhbmNlZFBrZy5kaXNjb3VudFBlcmNlbnRhZ2V9JVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApXG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cblxuICAgICAgey8qIEN1c3RvbSBGaWVsZHMgKi99XG4gICAgICB7dmlzaWJsZUZpZWxkcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwLzUwIGJvcmRlci1zbGF0ZS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXdoaXRlXCI+2YXYudmE2YjZhdin2Kog2KXYttin2YHZitipPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIHt2aXNpYmxlRmllbGRzLm1hcChyZW5kZXJDdXN0b21GaWVsZCl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICApfVxuXG4gICAgICB7LyogU3VibWl0IEJ1dHRvbiAqL31cbiAgICAgIHtzZWxlY3RlZFBhY2thZ2UgJiYgKFxuICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJiZy1zbGF0ZS04MDAvNTAgYm9yZGVyLXNsYXRlLTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtXCI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInB0LTZcIj5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU3VibWl0fVxuICAgICAgICAgICAgICBkaXNhYmxlZD17ZGlzYWJsZWQgfHwgaXNMb2FkaW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy01MDAgdG8teWVsbG93LTYwMCBob3Zlcjpmcm9tLXllbGxvdy02MDAgaG92ZXI6dG8teWVsbG93LTcwMCB0ZXh0LWJsYWNrIGZvbnQtYm9sZCBweS00IHRleHQtbGdcIlxuICAgICAgICAgICAgICBzaXplPVwibGdcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIFwi2KzYp9ix2Yog2KfZhNmF2LnYp9mE2KzYqS4uLlwiXG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5wcm9jZXNzaW5nVHlwZSA9PT0gXCJpbnN0YW50XCIgPyBcItin2LTYqtix2Yog2KfZhNii2YZcIiA6IFwi2KPYttmBINmE2YTYs9mE2KlcIn1cbiAgICAgICAgICAgICAgICAgIHtzaG93UHJpY2luZyAmJiBgIC0gJHtmb3JtYXRQcmljZSh0b3RhbFByaWNlKX1gfVxuICAgICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiQmFkZ2UiLCJaYXAiLCJlbmhhbmNlUGFja2FnZVdpdGhEaXNjb3VudEluZm8iLCJmb3JtYXRDdXJyZW5jeSIsInVzZUN1cnJlbmN5Q29udmVydGVyIiwiU2ltcGxlUHJvZHVjdEZvcm0iLCJ0ZW1wbGF0ZSIsIm9uU3VibWl0IiwiY3VycmVuY3kiLCJzaG93UHJpY2luZyIsImRpc2FibGVkIiwiY2xhc3NOYW1lIiwic2VsZWN0ZWRQYWNrYWdlIiwic2V0U2VsZWN0ZWRQYWNrYWdlIiwicXVhbnRpdHkiLCJzZXRRdWFudGl0eSIsImN1c3RvbUZpZWxkVmFsdWVzIiwic2V0Q3VzdG9tRmllbGRWYWx1ZXMiLCJmaWVsZEVycm9ycyIsInNldEZpZWxkRXJyb3JzIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiY29udmVydFByaWNlIiwiY2FsY3VsYXRlVG90YWxQcmljZSIsInByaWNlIiwiZm9ybWF0UHJpY2UiLCJjb252ZXJ0ZWRQcmljZSIsImhhbmRsZVBhY2thZ2VTZWxlY3QiLCJwa2ciLCJoYW5kbGVGaWVsZENoYW5nZSIsImZpZWxkTmFtZSIsInZhbHVlIiwicHJldiIsInZhbGlkYXRlRmllbGRzIiwiZXJyb3JzIiwiYWN0aXZlRmllbGRzIiwiZmllbGRzIiwiZmlsdGVyIiwiZiIsImlzQWN0aXZlIiwiZm9yRWFjaCIsImZpZWxkIiwibmFtZSIsInJlcXVpcmVkIiwidG9TdHJpbmciLCJ0cmltIiwibGFiZWwiLCJ0eXBlIiwiZW1haWxSZWdleCIsInRlc3QiLCJpc05hTiIsIk51bWJlciIsImhhbmRsZVN1Ym1pdCIsImFsZXJ0IiwiT2JqZWN0Iiwia2V5cyIsImxlbmd0aCIsImZvcm1EYXRhIiwidGVtcGxhdGVJZCIsImlkIiwiY3VzdG9tRmllbGRzIiwidG90YWxQcmljZSIsImVycm9yIiwiY29uc29sZSIsInJlbmRlckN1c3RvbUZpZWxkIiwiZGl2Iiwic3BhbiIsImlucHV0Iiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0IiwicGxhY2Vob2xkZXIiLCJzZWxlY3QiLCJvcHRpb24iLCJvcHRpb25zIiwibWFwIiwicCIsInZpc2libGVGaWVsZHMiLCJwYWNrYWdlcyIsImVuaGFuY2VkUGtnIiwiYXZhaWxhYmxlQ29kZXMiLCJkaWdpdGFsQ29kZXMiLCJjb2RlIiwidXNlZCIsImlzQXZhaWxhYmxlIiwicXVhbnRpdHlMaW1pdCIsInVuZGVmaW5lZCIsIm9uQ2xpY2siLCJoMyIsImFtb3VudCIsImRlc2NyaXB0aW9uIiwiaGFzRGlzY291bnQiLCJvcmlnaW5hbFByaWNlIiwiZGlzY291bnRQZXJjZW50YWdlIiwidmFyaWFudCIsInNpemUiLCJwcm9jZXNzaW5nVHlwZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/SimpleProductForm.tsx\n"));

/***/ })

});