"use client"

import { useState, useRef, useEffect, useCallback } from "react"
import { Plus, Edit, Trash2, X, Upload, Key, AlertCircle, Package, Type, Loader2, RefreshCw, Crop, Save, AlertTriangle } from "lucide-react"
import {
  ProductTemplate,
  ProductPackage,
  DynamicField,
  DigitalCode,
  DropdownOption,
  calculateDiscountPercentage,
  validateDiscountPricing,
  enhancePackageWithDiscountInfo
} from "@/lib/types"
import { createProduct, updateProduct } from "@/lib/services/productService"
import { getCategories } from "@/lib/services/categoryService"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import ImageUploader from "./ImageUploader"
import {
  validateProductName,
  validateProductCategory,
  validatePackageName,
  validatePackagePrice,
  validateDigitalCodes,
  validateFieldLabel,
  validateDropdownOptions,
  validateProductData,
  validatePriceDistribution,
  sanitizeText,
  sanitizeDigitalCode,
  sanitizeNumber
} from "@/lib/utils/validation"

interface SimpleProductFormProps {
  product?: ProductTemplate
  onSave: (product: ProductTemplate) => void
  onCancel: () => void
  isEditing?: boolean
}

export default function SimpleProductForm({ product, onSave, onCancel, isEditing = false }: SimpleProductFormProps) {
  const { toast } = useToast()

  // Core form state
  const [isLoading, setIsLoading] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [validationWarnings, setValidationWarnings] = useState<string[]>([])

  // Dialog states
  const [isPackageDialogOpen, setIsPackageDialogOpen] = useState(false)
  const [isFieldDialogOpen, setIsFieldDialogOpen] = useState(false)
  const [editingPackageIndex, setEditingPackageIndex] = useState<number | null>(null)
  const [editingFieldIndex, setEditingFieldIndex] = useState<number | null>(null)

  const [formData, setFormData] = useState<Partial<ProductTemplate>>({
    name: product?.name || "",
    description: product?.description || "",
    category: product?.category || "",
    tags: product?.tags || [],
    image: product?.image || "",
    packages: product?.packages || [],
    fields: product?.fields || [],
    features: product?.features || [],
    isActive: product?.isActive ?? true,
    isFeatured: product?.isFeatured || false,
    deliveryType: product?.deliveryType || "code_based",
    productType: product?.productType || "digital",
    processingType: product?.processingType || "instant",
  })



  // Package dialog form state (updated for new discount system)
  const [packageForm, setPackageForm] = useState({
    name: "",
    amount: "",
    price: 0,
    originalPrice: 0, // Optional - for discount calculation
    description: "",
    popular: false,
    digitalCodes: ""
  })

  // Field dialog form state (updated to support dropdown fields)
  const [fieldForm, setFieldForm] = useState({
    label: "",
    type: "universal_input" as "universal_input" | "dropdown",
    placeholder: "",
    required: false,
    options: [] as DropdownOption[]
  })

  // Dropdown options management
  const [newOptionText, setNewOptionText] = useState("")

  // Image upload state (temporary fix for the error)
  const [tempUrl, setTempUrl] = useState("")
  const [isValidImage, setIsValidImage] = useState(true)
  const [isTestingUrl, setIsTestingUrl] = useState(false)
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 })
  const [isImageCropDialogOpen, setIsImageCropDialogOpen] = useState(false)
  const [imageSrc, setImageSrc] = useState<string | null>(null)
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [cropArea, setCropArea] = useState({ x: 0, y: 0, width: 0, height: 0 })
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const inputId = useRef(`image-upload-${Math.random().toString(36).substr(2, 9)}`)
  const imageRef = useRef<HTMLImageElement>(null)

  // Track unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault()
        e.returnValue = ''
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [hasUnsavedChanges])

  // Mark form as changed when data updates
  const updateFormData = useCallback((updater: (prev: Partial<ProductTemplate>) => Partial<ProductTemplate>) => {
    setFormData(updater)
    setHasUnsavedChanges(true)
  }, [])

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "",
      tags: [],
      image: "",
      packages: [],
      fields: [],
      features: [],
      isActive: true,
      isFeatured: false,
      deliveryType: "code_based",
      productType: "digital",
      processingType: "instant",
    })
    setTempUrl("")
    setHasUnsavedChanges(false)
    setValidationErrors([])
    setValidationWarnings([])
  }

  // Temporary handlers for the old image upload (will be replaced)
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempUrl(e.target.value)
    setIsValidImage(true)
  }

  const handleApplyUrl = async () => {
    // Temporary implementation
    setFormData(prev => ({ ...prev, image: tempUrl }))
  }

  const handleImageError = () => {
    setIsValidImage(false)
  }

  const handleUploadButtonClick = () => {
    // Temporary implementation
  }

  const onSelectFile = () => {
    // Temporary implementation
  }

  // Image cropping handlers
  const handleStart = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault()
    setIsDragging(true)
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY
    setDragStart({ x: clientX, y: clientY })
  }

  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging) return
    e.preventDefault()
    const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX
    const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY

    const deltaX = clientX - dragStart.x
    const deltaY = clientY - dragStart.y

    setCropArea(prev => ({
      ...prev,
      x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),
      y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))
    }))

    setDragStart({ x: clientX, y: clientY })
  }

  const handleEnd = () => {
    setIsDragging(false)
  }

  // Handle image crop completion
  const handleImageCrop = (croppedImageUrl: string) => {
    setFormData(prev => ({ ...prev, image: croppedImageUrl }))
    setIsImageCropDialogOpen(false)
    setImagePreview(null)
  }

  const handleSave = async () => {
    setIsLoading(true)
    setValidationErrors([])
    setValidationWarnings([])

    try {
      // Comprehensive validation
      const validation = validateProductData(formData)

      if (!validation.isValid) {
        setValidationErrors(validation.errors)
        setValidationWarnings(validation.warnings)

        toast({
          title: "خطأ في البيانات",
          description: `يرجى تصحيح ${validation.errors.length} خطأ قبل الحفظ`,
          variant: "destructive",
        })

        setIsLoading(false)
        return
      }

      // Show warnings if any
      if (validation.warnings.length > 0) {
        setValidationWarnings(validation.warnings)
        toast({
          title: "تحذيرات",
          description: `${validation.warnings.length} تحذير - يمكنك المتابعة أو تحسين البيانات`,
          variant: "default",
        })
      }

      // Enhance packages with discount calculations
      const enhancedPackages = formData.packages?.map((pkg, index) => ({
        ...pkg,
        sortOrder: index,
        ...enhancePackageWithDiscountInfo(pkg)
      })) || []

      // Prepare product data
      const productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'> = {
        name: sanitizeText(formData.name!),
        description: sanitizeText(formData.description || ""),
        category: sanitizeText(formData.category!),
        image: formData.image,
        deliveryType: formData.deliveryType!,
        productType: formData.productType!,
        processingType: formData.processingType!,
        fields: formData.fields?.map((field, index) => ({
          ...field,
          sortOrder: index,
          label: sanitizeText(field.label),
          name: field.name || `field_${Date.now()}_${index}`
        })) || [],
        packages: enhancedPackages,
        features: formData.features?.map(sanitizeText) || [],
        tags: formData.tags?.map(sanitizeText) || [],
        isActive: formData.isActive!,
        isFeatured: formData.isFeatured!,
        createdBy: undefined // TODO: Get from auth
      }

      let savedProduct: ProductTemplate

      if (isEditing && product) {
        savedProduct = await updateProduct(product.id, productData)
        toast({
          title: "تم التحديث بنجاح",
          description: "تم تحديث المنتج بنجاح",
        })
      } else {
        savedProduct = await createProduct(productData)
        toast({
          title: "تم الإنشاء بنجاح",
          description: "تم إنشاء المنتج بنجاح",
        })
      }

      setHasUnsavedChanges(false)
      onSave(savedProduct)

    } catch (error) {
      console.error("Error saving product:", error)
      toast({
        title: "خطأ في الحفظ",
        description: "حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Reset package form
  const resetPackageForm = () => {
    setPackageForm({
      name: "",
      amount: "",
      price: 0,
      originalPrice: 0,
      description: "",
      popular: false,
      digitalCodes: ""
    })
  }

  // Reset field form
  const resetFieldForm = () => {
    setFieldForm({
      label: "",
      type: "universal_input",
      placeholder: "",
      required: false,
      options: []
    })
    setNewOptionText("")
  }

  // Open package dialog for creating new package
  const openPackageDialog = () => {
    resetPackageForm()
    setEditingPackageIndex(null)
    setIsPackageDialogOpen(true)
  }

  // Open package dialog for editing existing package
  const editPackage = (index: number) => {
    const pkg = formData.packages![index]
    setPackageForm({
      name: pkg.name,
      amount: pkg.amount,
      price: pkg.price,
      originalPrice: pkg.originalPrice || 0,
      description: pkg.description || "",
      popular: pkg.popular || false,
      digitalCodes: pkg.digitalCodes?.map(code => code.key).join('\n') || ""
    })
    setEditingPackageIndex(index)
    setIsPackageDialogOpen(true)
  }

  // Save package from dialog
  const savePackage = () => {
    // Validate package name
    const nameValidation = validatePackageName(packageForm.name)
    if (!nameValidation.isValid) {
      toast({
        title: "خطأ في اسم الحزمة",
        description: nameValidation.error,
        variant: "destructive",
      })
      return
    }

    // Validate package price
    const priceValidation = validatePackagePrice(packageForm.price)
    if (!priceValidation.isValid) {
      toast({
        title: "خطأ في السعر",
        description: priceValidation.error,
        variant: "destructive",
      })
      return
    }

    // Validate discount pricing if original price is provided
    if (packageForm.originalPrice > 0) {
      const discountValidation = validateDiscountPricing(packageForm.originalPrice, packageForm.price)
      if (!discountValidation.isValid) {
        toast({
          title: "خطأ في الخصم",
          description: discountValidation.error,
          variant: "destructive",
        })
        return
      }
    }

    // Process and validate digital codes
    const codeLines = packageForm.digitalCodes
      .split('\n')
      .map(line => line.trim())
      .filter(Boolean)

    const codesValidation = validateDigitalCodes(codeLines)
    if (!codesValidation.isValid) {
      toast({
        title: "خطأ في الأكواد الرقمية",
        description: codesValidation.error,
        variant: "destructive",
      })
      return
    }

    // Create digital codes with proper structure
    const digitalCodes: DigitalCode[] = (codesValidation.sanitizedCodes || []).map((key, i) => ({
      id: `${Date.now()}-${i}`,
      key: sanitizeDigitalCode(key),
      used: false,
      assignedToOrderId: null,
      createdAt: new Date()
    }))

    const newPackage: ProductPackage = {
      id: editingPackageIndex !== null ? formData.packages![editingPackageIndex].id : Date.now().toString(),
      name: sanitizeText(packageForm.name),
      amount: sanitizeText(packageForm.amount),
      price: sanitizeNumber(packageForm.price),
      originalPrice: packageForm.originalPrice > 0 ? sanitizeNumber(packageForm.originalPrice) : undefined,
      description: packageForm.description ? sanitizeText(packageForm.description) : undefined,
      popular: packageForm.popular,
      isActive: true,
      sortOrder: editingPackageIndex !== null ? editingPackageIndex : (formData.packages?.length || 0),
      digitalCodes
    }

    updateFormData((prev) => {
      const packages = [...(prev.packages || [])]
      if (editingPackageIndex !== null) {
        packages[editingPackageIndex] = newPackage
      } else {
        packages.push(newPackage)
      }
      return { ...prev, packages }
    })

    toast({
      title: "تم الحفظ",
      description: editingPackageIndex !== null ? "تم تحديث الحزمة بنجاح" : "تم إضافة الحزمة بنجاح",
    })

    setIsPackageDialogOpen(false)
    resetPackageForm()
  }

  // Remove package
  const removePackage = (index: number) => {
    const packageName = formData.packages?.[index]?.name || `الحزمة ${index + 1}`

    if (confirm(`هل أنت متأكد من حذف "${packageName}"؟\n\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.`)) {
      updateFormData((prev) => ({
        ...prev,
        packages: prev.packages?.filter((_, i) => i !== index) || [],
      }))

      toast({
        title: "تم الحذف",
        description: `تم حذف "${packageName}" بنجاح`,
      })
    }
  }

  // Open field dialog for creating new field
  const openFieldDialog = () => {
    resetFieldForm()
    setEditingFieldIndex(null)
    setIsFieldDialogOpen(true)
  }

  // Open field dialog for editing existing field
  const editField = (index: number) => {
    const field = formData.fields![index]
    setFieldForm({
      label: field.label,
      type: field.type,
      placeholder: field.placeholder || "",
      required: field.required,
      options: field.options || []
    })
    setEditingFieldIndex(index)
    setIsFieldDialogOpen(true)
  }

  // Dropdown option management functions
  const addDropdownOption = () => {
    const sanitizedText = sanitizeText(newOptionText)
    if (!sanitizedText) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال نص الخيار",
        variant: "destructive",
      })
      return
    }

    // Check for duplicates
    const exists = fieldForm.options.some(opt => opt.label === sanitizedText)
    if (exists) {
      toast({
        title: "خطأ",
        description: "هذا الخيار موجود بالفعل",
        variant: "destructive",
      })
      return
    }

    const newOption: DropdownOption = {
      id: `option_${Date.now()}`,
      value: sanitizedText.toLowerCase().replace(/\s+/g, '_'),
      label: sanitizedText,
      sortOrder: fieldForm.options.length,
      isActive: true
    }

    setFieldForm(prev => ({
      ...prev,
      options: [...prev.options, newOption]
    }))
    setNewOptionText("")
  }

  const removeDropdownOption = (optionId: string) => {
    setFieldForm(prev => ({
      ...prev,
      options: prev.options.filter(opt => opt.id !== optionId)
    }))
  }

  const moveDropdownOption = (optionId: string, direction: 'up' | 'down') => {
    setFieldForm(prev => {
      const options = [...prev.options]
      const index = options.findIndex(opt => opt.id === optionId)

      if (index === -1) return prev

      const newIndex = direction === 'up' ? index - 1 : index + 1
      if (newIndex < 0 || newIndex >= options.length) return prev

      // Swap options
      [options[index], options[newIndex]] = [options[newIndex], options[index]]

      // Update sort orders
      options.forEach((opt, i) => {
        opt.sortOrder = i
      })

      return { ...prev, options }
    })
  }

  // Save field from dialog
  const saveField = () => {
    // Validate field label
    const labelValidation = validateFieldLabel(fieldForm.label)
    if (!labelValidation.isValid) {
      toast({
        title: "خطأ في تسمية الحقل",
        description: labelValidation.error,
        variant: "destructive",
      })
      return
    }

    // Validate dropdown options if it's a dropdown field
    if (fieldForm.type === 'dropdown') {
      const optionsValidation = validateDropdownOptions(fieldForm.options)
      if (!optionsValidation.isValid) {
        toast({
          title: "خطأ في خيارات القائمة",
          description: optionsValidation.error,
          variant: "destructive",
        })
        return
      }
    }

    const newField: DynamicField = {
      id: editingFieldIndex !== null ? formData.fields![editingFieldIndex].id : Date.now().toString(),
      type: fieldForm.type,
      name: editingFieldIndex !== null ? formData.fields![editingFieldIndex].name : `field_${Date.now()}`,
      label: sanitizeText(fieldForm.label),
      placeholder: fieldForm.placeholder ? sanitizeText(fieldForm.placeholder) : undefined,
      required: fieldForm.required,
      isActive: true,
      sortOrder: editingFieldIndex !== null ? editingFieldIndex : (formData.fields?.length || 0),
      validation: {},
      options: fieldForm.type === 'dropdown' ? fieldForm.options : undefined
    }

    updateFormData((prev) => {
      const fields = [...(prev.fields || [])]
      if (editingFieldIndex !== null) {
        fields[editingFieldIndex] = newField
      } else {
        fields.push(newField)
      }
      return { ...prev, fields }
    })

    toast({
      title: "تم الحفظ",
      description: editingFieldIndex !== null ? "تم تحديث الحقل بنجاح" : "تم إضافة الحقل بنجاح",
    })

    setIsFieldDialogOpen(false)
    resetFieldForm()
  }

  // Remove field
  const removeField = (index: number) => {
    const fieldLabel = formData.fields?.[index]?.label || `الحقل ${index + 1}`

    if (confirm(`هل أنت متأكد من حذف "${fieldLabel}"؟`)) {
      updateFormData((prev) => ({
        ...prev,
        fields: prev.fields?.filter((_, i) => i !== index) || [],
      }))

      toast({
        title: "تم الحذف",
        description: `تم حذف "${fieldLabel}" بنجاح`,
      })
    }
  }

  // Handle cancel with unsaved changes warning
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      if (confirm("لديك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟")) {
        onCancel()
      }
    } else {
      onCancel()
    }
  }

  return (
    <div className="bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl">
      <div className="p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl">
              <Package className="w-6 h-6 text-white" />
            </div>
            <div>
              <h3 className="text-2xl md:text-3xl font-bold text-white">
                {isEditing ? "تعديل المنتج" : "إنشاء منتج جديد"}
              </h3>
              <p className="text-gray-400 text-sm">
                {isEditing ? "قم بتحديث معلومات المنتج" : "أضف منتج جديد إلى المتجر"}
              </p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={handleCancel}
            className="border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white"
          >
            <X className="w-4 h-4 mr-2" />
            إلغاء
          </Button>
        </div>
      </div>

      <div className="p-6 md:p-8 space-y-8">
        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>يرجى تصحيح الأخطاء التالية:</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1 mt-2">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Validation Warnings */}
        {validationWarnings.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>تحذيرات:</AlertTitle>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1 mt-2">
                {validationWarnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Unsaved Changes Indicator */}
        {hasUnsavedChanges && (
          <Alert>
            <Save className="h-4 w-4" />
            <AlertTitle>تغييرات غير محفوظة</AlertTitle>
            <AlertDescription>
              لديك تغييرات غير محفوظة. تأكد من حفظ عملك قبل المغادرة.
            </AlertDescription>
          </Alert>
        )}

        {/* Basic Info */}
        <div className="bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20">
          <h4 className="text-xl font-semibold text-white mb-6 flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg">
              <Package className="w-5 h-5 text-white" />
            </div>
            المعلومات الأساسية
          </h4>

          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium mb-3 text-white">اسم المنتج *</label>
                <input
                  type="text"
                  value={formData.name || ""}
                  onChange={(e) => updateFormData((prev) => ({ ...prev, name: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                  placeholder="أدخل اسم المنتج"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-3 text-white">الفئة *</label>
                <input
                  type="text"
                  value={formData.category || ""}
                  onChange={(e) => updateFormData((prev) => ({ ...prev, category: e.target.value }))}
                  className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                  placeholder="مثل: MOBA, RPG, باتل رويال"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">الوصف</label>
              <textarea
                value={formData.description || ""}
                onChange={(e) => updateFormData((prev) => ({ ...prev, description: e.target.value }))}
                rows={4}
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none"
                placeholder="وصف المنتج"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">العلامات</label>
              <input
                type="text"
                value={formData.tags?.join(", ") || ""}
                onChange={(e) =>
                  updateFormData((prev) => ({
                    ...prev,
                    tags: e.target.value
                      .split(",")
                      .map((tag) => tag.trim())
                      .filter(Boolean),
                  }))
                }
                className="w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
                placeholder="شائع, مميز, جديد (مفصولة بفاصلة)"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-3 text-white">صورة الغلاف</label>

              <ImageUploader
                currentImage={formData.image || ""}
                onImageChanged={(url) => updateFormData(prev => ({ ...prev, image: url }))}
                label="صورة المنتج"
                placeholderText="أدخل رابط صورة المنتج أو قم برفع صورة"
                aspectRatio={1}
                maxFileSize={10}
                showUrlInput={true}
                className="space-y-3"
              />
            </div>

            {/* Status Toggles */}
            <div className="flex flex-wrap gap-6 pt-4 border-t border-gray-600/30">
              <label className="flex items-center gap-3 text-white cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isFeatured || false}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isFeatured: e.target.checked }))}
                  className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                />
                <span>منتج مميز</span>
              </label>
              <label className="flex items-center gap-3 text-white cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isActive ?? true}
                  onChange={(e) => setFormData((prev) => ({ ...prev, isActive: e.target.checked }))}
                  className="w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500"
                />
                <span>منتج نشط</span>
              </label>
            </div>
          </div>
        </div>

        {/* Packages */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Package className="w-6 h-6 text-purple-400" />
              <h4 className="text-xl font-semibold text-white">الحزم</h4>
              <span className="text-sm text-gray-400">({formData.packages?.length || 0})</span>
            </div>
            <Button
              onClick={openPackageDialog}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة حزمة
            </Button>
          </div>

          {formData.packages && formData.packages.length > 0 ? (
            <div className="grid gap-4">
              {formData.packages.map((pkg, index) => (
                <div
                  key={pkg.id}
                  className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300"
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <h5 className="text-lg font-semibold text-white mb-1">{pkg.name}</h5>
                      <p className="text-purple-300 font-bold text-xl">${pkg.price}</p>
                      {pkg.amount && (
                        <p className="text-gray-300 text-sm">{pkg.amount}</p>
                      )}
                      {pkg.description && (
                        <p className="text-gray-400 text-sm mt-2">{pkg.description}</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {pkg.popular && (
                        <span className="bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs">
                          شائع
                        </span>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editPackage(index)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removePackage(index)}
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>

                  {pkg.digitalCodes && pkg.digitalCodes.length > 0 && (
                    <div className="flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20">
                      <Key className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-300 text-sm">
                        {pkg.digitalCodes.length} كود رقمي متاح
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600">
              <Package className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">لم يتم إضافة أي حزم بعد</p>
              <Button
                onClick={openPackageDialog}
                variant="outline"
                className="border-purple-600 text-purple-400 hover:bg-purple-600/10"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة أول حزمة
              </Button>
            </div>
          )}
        </div>

        {/* Custom Fields */}
        <div>
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <Type className="w-6 h-6 text-blue-400" />
              <h4 className="text-xl font-semibold text-white">الحقول المخصصة</h4>
              <span className="text-sm text-gray-400">({formData.fields?.length || 0})</span>
            </div>
            <Button
              onClick={openFieldDialog}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg"
            >
              <Plus className="w-4 h-4 mr-2" />
              إضافة حقل
            </Button>
          </div>

          {formData.fields && formData.fields.length > 0 ? (
            <div className="grid gap-4">
              {formData.fields.map((field, index) => (
                <div
                  key={field.id}
                  className="bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h5 className="text-lg font-semibold text-white mb-1">{field.label}</h5>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span className="bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full">
                          {field.type === "text" ? "نص" : field.type === "email" ? "بريد إلكتروني" : "رقم"}
                        </span>
                        {field.required && (
                          <span className="bg-red-500/20 text-red-300 px-2 py-1 rounded-full">
                            مطلوب
                          </span>
                        )}
                      </div>
                      {field.placeholder && (
                        <p className="text-gray-400 text-sm mt-2">"{field.placeholder}"</p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => editField(index)}
                        className="border-gray-600 text-gray-300 hover:bg-gray-700"
                      >
                        <Edit className="w-3 h-3 mr-1" />
                        تعديل
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeField(index)}
                        className="border-red-600 text-red-400 hover:bg-red-600/10"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600">
              <Type className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-400 mb-4">لم يتم إضافة أي حقول مخصصة بعد</p>
              <Button
                onClick={openFieldDialog}
                variant="outline"
                className="border-blue-600 text-blue-400 hover:bg-blue-600/10"
              >
                <Plus className="w-4 h-4 mr-2" />
                إضافة أول حقل
              </Button>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30">
          <Button
            onClick={handleSave}
            disabled={isLoading}
            className="flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg"
            size="lg"
          >
            {isLoading ? (
              <div className="flex items-center gap-3">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>جاري الحفظ...</span>
              </div>
            ) : (
              <div className="flex items-center gap-3">
                <Package className="w-5 h-5" />
                <span>{isEditing ? "تحديث المنتج" : "إنشاء المنتج"}</span>
              </div>
            )}
          </Button>
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg"
            size="lg"
          >
            إلغاء
          </Button>
        </div>
      </div>

      {/* Package Dialog */}
      <Dialog open={isPackageDialogOpen} onOpenChange={setIsPackageDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Package className="w-5 h-5 text-purple-400" />
              {editingPackageIndex !== null ? "تعديل الحزمة" : "إضافة حزمة جديدة"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Basic Package Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">اسم الحزمة *</label>
                <input
                  type="text"
                  value={packageForm.name}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="مثل: 60 يوسي"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">الكمية</label>
                <input
                  type="text"
                  value={packageForm.amount}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, amount: e.target.value }))}
                  placeholder="مثل: 60 يوسي"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                />
              </div>
            </div>

            {/* Enhanced Pricing System */}
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">السعر الحالي *</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0.01"
                    value={packageForm.price}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, price: Number(e.target.value) }))}
                    placeholder="0.00"
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                  <p className="text-xs text-gray-400 mt-1">السعر الذي سيدفعه العميل</p>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">السعر الأصلي (اختياري)</label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={packageForm.originalPrice}
                    onChange={(e) => setPackageForm(prev => ({ ...prev, originalPrice: Number(e.target.value) }))}
                    placeholder="0.00"
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500"
                  />
                  <p className="text-xs text-gray-400 mt-1">للعرض كخصم (اتركه فارغاً إذا لم يكن هناك خصم)</p>
                </div>
              </div>

              {/* Discount Calculation Display */}
              {packageForm.originalPrice > 0 && packageForm.price > 0 && (
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
                  {packageForm.originalPrice > packageForm.price ? (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-green-300 text-sm">
                        خصم {calculateDiscountPercentage(packageForm.originalPrice, packageForm.price)}%
                        (توفير {(packageForm.originalPrice - packageForm.price).toFixed(2)} دولار)
                      </span>
                    </div>
                  ) : packageForm.originalPrice === packageForm.price ? (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      <span className="text-yellow-300 text-sm">
                        لا يوجد خصم (السعران متساويان)
                      </span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                      <span className="text-red-300 text-sm">
                        خطأ: السعر الأصلي يجب أن يكون أكبر من السعر الحالي
                      </span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium mb-2">الوصف</label>
              <textarea
                value={packageForm.description}
                onChange={(e) => setPackageForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="وصف الحزمة (اختياري)"
                rows={3}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none"
              />
            </div>

            {/* Digital Codes */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Key className="w-5 h-5 text-blue-400" />
                <label className="block text-sm font-medium">الأكواد الرقمية</label>
                <span className="text-xs text-gray-400">(اختياري)</span>
              </div>

              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3">
                <p className="text-sm text-blue-300 mb-2">💡 إرشادات:</p>
                <ul className="text-xs text-blue-200 space-y-1">
                  <li>• أدخل كود واحد في كل سطر</li>
                  <li>• سيتم تخصيص كود واحد فقط لكل طلب</li>
                  <li>• الأكواد المستخدمة لن تظهر للمشترين الآخرين</li>
                </ul>
              </div>

              <textarea
                value={packageForm.digitalCodes}
                onChange={(e) => setPackageForm(prev => ({ ...prev, digitalCodes: e.target.value }))}
                placeholder="أدخل الأكواد الرقمية (كود واحد في كل سطر)&#10;مثال:&#10;AB12-XY34-ZZ78&#10;CD56-PL90-QW12"
                rows={6}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none"
              />
            </div>

            {/* Options */}
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-white">
                <input
                  type="checkbox"
                  checked={packageForm.popular}
                  onChange={(e) => setPackageForm(prev => ({ ...prev, popular: e.target.checked }))}
                  className="rounded"
                />
                <span>حزمة شائعة</span>
              </label>
            </div>
          </div>

          {/* Dialog Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={() => setIsPackageDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              إلغاء
            </Button>
            <Button
              onClick={savePackage}
              className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800"
            >
              {editingPackageIndex !== null ? "تحديث الحزمة" : "إضافة الحزمة"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Field Dialog */}
      <Dialog open={isFieldDialogOpen} onOpenChange={setIsFieldDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Type className="w-5 h-5 text-blue-400" />
              {editingFieldIndex !== null ? "تعديل الحقل" : "إضافة حقل جديد"}
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Field Label */}
            <div>
              <label className="block text-sm font-medium mb-2">تسمية الحقل *</label>
              <input
                type="text"
                value={fieldForm.label}
                onChange={(e) => setFieldForm(prev => ({ ...prev, label: e.target.value }))}
                placeholder="مثل: اسم المستخدم"
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
              />
            </div>

            {/* Field Type */}
            <div>
              <label className="block text-sm font-medium mb-2">نوع الحقل</label>
              <select
                value={fieldForm.type}
                onChange={(e) => setFieldForm(prev => ({
                  ...prev,
                  type: e.target.value as "universal_input" | "dropdown",
                  options: e.target.value === "dropdown" ? prev.options : []
                }))}
                className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
              >
                <option value="universal_input">حقل إدخال (نص، بريد إلكتروني، رقم)</option>
                <option value="dropdown">قائمة منسدلة</option>
              </select>
              <p className="text-xs text-gray-400 mt-1">
                {fieldForm.type === "universal_input"
                  ? "حقل إدخال عام يمكن استخدامه للنصوص والأرقام والبريد الإلكتروني"
                  : "قائمة خيارات يختار منها المستخدم"
                }
              </p>
            </div>

            {/* Placeholder (only for universal_input) */}
            {fieldForm.type === "universal_input" && (
              <div>
                <label className="block text-sm font-medium mb-2">النص التوضيحي</label>
                <input
                  type="text"
                  value={fieldForm.placeholder}
                  onChange={(e) => setFieldForm(prev => ({ ...prev, placeholder: e.target.value }))}
                  placeholder="مثل: أدخل اسم المستخدم"
                  className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                />
              </div>
            )}

            {/* Dropdown Options Management */}
            {fieldForm.type === "dropdown" && (
              <div>
                <label className="block text-sm font-medium mb-3">خيارات القائمة المنسدلة</label>

                {/* Add New Option */}
                <div className="flex gap-2 mb-4">
                  <input
                    type="text"
                    value={newOptionText}
                    onChange={(e) => setNewOptionText(e.target.value)}
                    placeholder="أدخل خيار جديد..."
                    className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addDropdownOption()
                      }
                    }}
                  />
                  <Button
                    type="button"
                    onClick={addDropdownOption}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>

                {/* Options List */}
                {fieldForm.options.length > 0 ? (
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {fieldForm.options.map((option, index) => (
                      <div key={option.id} className="flex items-center gap-2 bg-gray-700/50 rounded-lg p-2">
                        <span className="flex-1 text-sm">{option.label}</span>
                        <div className="flex gap-1">
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => moveDropdownOption(option.id, 'up')}
                            disabled={index === 0}
                            className="h-6 w-6 p-0"
                          >
                            ↑
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => moveDropdownOption(option.id, 'down')}
                            disabled={index === fieldForm.options.length - 1}
                            className="h-6 w-6 p-0"
                          >
                            ↓
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            variant="ghost"
                            onClick={() => removeDropdownOption(option.id)}
                            className="h-6 w-6 p-0 text-red-400 hover:text-red-300"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-400 text-sm border border-gray-600 rounded-lg border-dashed">
                    لا توجد خيارات. أضف خيار واحد على الأقل.
                  </div>
                )}
              </div>
            )}

            {/* Required */}
            <div className="flex items-center gap-2">
              <input
                type="checkbox"
                id="field-required"
                checked={fieldForm.required}
                onChange={(e) => setFieldForm(prev => ({ ...prev, required: e.target.checked }))}
                className="rounded"
              />
              <label htmlFor="field-required" className="text-white">
                حقل مطلوب
              </label>
            </div>
          </div>

          {/* Dialog Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-700">
            <Button
              variant="outline"
              onClick={() => setIsFieldDialogOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              إلغاء
            </Button>
            <Button
              onClick={saveField}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
            >
              {editingFieldIndex !== null ? "تحديث الحقل" : "إضافة الحقل"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Image Crop Dialog */}
      <Dialog open={isImageCropDialogOpen} onOpenChange={setIsImageCropDialogOpen}>
        <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center gap-2">
              <Crop className="w-5 h-5 text-green-400" />
              قص وتعديل الصورة
            </DialogTitle>
          </DialogHeader>

          {imagePreview && (
            <ImageCropper
              imageSrc={imagePreview}
              onCrop={handleImageCrop}
              onCancel={() => setIsImageCropDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Simple Image Cropper Component
interface ImageCropperProps {
  imageSrc: string
  onCrop: (croppedImage: string) => void
  onCancel: () => void
}

function ImageCropper({ imageSrc, onCrop, onCancel }: ImageCropperProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const [cropArea, setCropArea] = useState({ x: 50, y: 50, width: 200, height: 200 })
  const [isDragging, setIsDragging] = useState(false)
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageSize, setImageSize] = useState({ width: 0, height: 0 })

  // Handle both mouse and touch events
  const getEventPosition = (e: React.MouseEvent | React.TouchEvent) => {
    if ('touches' in e) {
      return { x: e.touches[0].clientX, y: e.touches[0].clientY }
    }
    return { x: e.clientX, y: e.clientY }
  }

  const handleStart = (e: React.MouseEvent | React.TouchEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleMove = (e: React.MouseEvent | React.TouchEvent) => {
    if (!isDragging || !imageRef.current) return
    e.preventDefault()

    const rect = imageRef.current.getBoundingClientRect()
    const pos = getEventPosition(e)

    const relativeX = pos.x - rect.left
    const relativeY = pos.y - rect.top

    // Keep crop area within image bounds
    const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width))
    const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height))

    setCropArea(prev => ({ ...prev, x: newX, y: newY }))
  }

  const handleEnd = () => {
    setIsDragging(false)
  }

  const handleCrop = () => {
    const canvas = canvasRef.current
    const image = imageRef.current
    if (!canvas || !image) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Calculate scale factors
    const scaleX = image.naturalWidth / image.offsetWidth
    const scaleY = image.naturalHeight / image.offsetHeight

    // Set canvas size to desired output size
    const outputSize = 400
    canvas.width = outputSize
    canvas.height = outputSize

    // Draw cropped and resized image
    ctx.drawImage(
      image,
      cropArea.x * scaleX,
      cropArea.y * scaleY,
      cropArea.width * scaleX,
      cropArea.height * scaleY,
      0,
      0,
      outputSize,
      outputSize
    )

    // Convert to base64
    const croppedImageData = canvas.toDataURL('image/jpeg', 0.9)
    onCrop(croppedImageData)
  }

  const setCropSize = (size: number) => {
    const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8
    const newSize = Math.min(size, maxSize)
    setCropArea(prev => ({
      ...prev,
      width: newSize,
      height: newSize,
      x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),
      y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))
    }))
  }

  return (
    <div className="space-y-6 py-4">
      {/* Crop Size Controls */}
      <div className="flex justify-center gap-2 mb-4">
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() => setCropSize(150)}
          className="border-blue-600 text-blue-400 hover:bg-blue-600/10"
        >
          صغير
        </Button>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() => setCropSize(200)}
          className="border-blue-600 text-blue-400 hover:bg-blue-600/10"
        >
          متوسط
        </Button>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() => setCropSize(300)}
          className="border-blue-600 text-blue-400 hover:bg-blue-600/10"
        >
          كبير
        </Button>
      </div>

      <div className="text-center">
        <p className="text-gray-300 mb-4">اضغط واسحب لتحريك منطقة القص</p>

        <div className="relative inline-block bg-gray-900 rounded-lg overflow-hidden">
          <img
            ref={imageRef}
            src={imageSrc}
            alt="صورة للقص"
            className="max-w-full max-h-96 object-contain block select-none"
            onLoad={() => {
              if (imageRef.current) {
                const { offsetWidth, offsetHeight } = imageRef.current
                setImageSize({ width: offsetWidth, height: offsetHeight })
                const size = Math.min(offsetWidth, offsetHeight) * 0.6
                setCropArea({
                  x: (offsetWidth - size) / 2,
                  y: (offsetHeight - size) / 2,
                  width: size,
                  height: size
                })
                setImageLoaded(true)
              }
            }}
            onMouseMove={handleMove}
            onMouseUp={handleEnd}
            onMouseLeave={handleEnd}
            onTouchMove={handleMove}
            onTouchEnd={handleEnd}
          />

          {/* Simple crop overlay */}
          {imageLoaded && (
            <div
              className="absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none"
              style={{
                left: cropArea.x,
                top: cropArea.y,
                width: cropArea.width,
                height: cropArea.height,
                userSelect: 'none',
                WebkitUserSelect: 'none',
                touchAction: 'none'
              }}
              onMouseDown={handleStart}
              onTouchStart={handleStart}
            >
              {/* Simple center indicator */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div className="w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center">
                  <Crop className="w-4 h-4 text-white" />
                </div>
              </div>

              {/* Corner indicators */}
              <div className="absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
              <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
              <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full"></div>
            </div>
          )}
        </div>
      </div>

      <canvas ref={canvasRef} className="hidden" />

      <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4">
        <p className="text-green-300 text-sm mb-2">💡 كيفية الاستخدام:</p>
        <ul className="text-green-200 text-xs space-y-1">
          <li>• اختر حجم منطقة القص من الأزرار أعلاه</li>
          <li>• اضغط واسحب المربع الأخضر لتحريكه</li>
          <li>• يعمل باللمس على الهاتف والماوس على الكمبيوتر</li>
          <li>• الصورة ستُحفظ بجودة عالية مربعة الشكل</li>
        </ul>
      </div>

      <div className="flex justify-end gap-3 pt-6 border-t border-gray-700">
        <Button
          variant="outline"
          onClick={onCancel}
          className="border-gray-600 text-gray-300 hover:bg-gray-700"
        >
          إلغاء
        </Button>
        <Button
          onClick={handleCrop}
          disabled={!imageLoaded}
          className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50"
        >
          <Crop className="w-4 h-4 mr-2" />
          قص واستخدام
        </Button>
      </div>
    </div>
  )
}
