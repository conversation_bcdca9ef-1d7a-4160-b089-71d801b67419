"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./lib/types/index.ts":
/*!****************************!*\
  !*** ./lib/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscountPercentage: () => (/* binding */ calculateDiscountPercentage),\n/* harmony export */   enhancePackageWithDiscountInfo: () => (/* binding */ enhancePackageWithDiscountInfo),\n/* harmony export */   validateDiscountPricing: () => (/* binding */ validateDiscountPricing)\n/* harmony export */ });\n// Common types used across the application\n// =====================================================\n// UTILITY FUNCTIONS FOR PACKAGE CALCULATIONS\n// =====================================================\n/**\r\n * Calculate discount percentage from original and current price\r\n */ const calculateDiscountPercentage = (originalPrice, currentPrice)=>{\n    if (!originalPrice || originalPrice <= 0 || !currentPrice || currentPrice <= 0) {\n        return 0;\n    }\n    if (originalPrice <= currentPrice) {\n        return 0;\n    }\n    return Math.round((originalPrice - currentPrice) / originalPrice * 100);\n};\n/**\r\n * Validate discount pricing logic\r\n */ const validateDiscountPricing = (originalPrice, currentPrice)=>{\n    // If no original price, no discount validation needed\n    if (!originalPrice) {\n        return {\n            isValid: true\n        };\n    }\n    // If original price is provided, current price must be provided and valid\n    if (!currentPrice || currentPrice <= 0) {\n        return {\n            isValid: false,\n            error: \"يرجى إدخال السعر الحالي\"\n        };\n    }\n    // Original price must be greater than current price for discount to make sense\n    if (originalPrice <= currentPrice) {\n        return {\n            isValid: false,\n            error: \"السعر الأصلي يجب أن يكون أكبر من السعر الحالي\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\r\n * Enhanced ProductPackage with computed properties\r\n */ const enhancePackageWithDiscountInfo = (pkg)=>{\n    const discountPercentage = pkg.originalPrice ? calculateDiscountPercentage(pkg.originalPrice, pkg.price) : 0;\n    return {\n        ...pkg,\n        discountPercentage,\n        hasDiscount: discountPercentage > 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/types/index.ts\n"));

/***/ })

});