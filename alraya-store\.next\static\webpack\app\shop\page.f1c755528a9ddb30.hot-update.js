"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./components/layout/DesktopFooter.tsx":
/*!*********************************************!*\
  !*** ./components/layout/DesktopFooter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DesktopFooter: () => (/* binding */ DesktopFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ DesktopFooter auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DesktopFooter(param) {\n    let { activeTab, onTabChange } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat)();\n    // Standardized navigation items matching mobile\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, this),\n            label: \"الدعم الفني\",\n            action: ()=>openChat() // Opens chat for support, consistent with mobile\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-8 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-8\",\n                children: navItems.map((param)=>{\n                    let { id, icon, label } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onTabChange(id),\n                        className: \"flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 hover:scale-105 \".concat(activeTab === id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg\" : \"text-slate-400 hover:text-white hover:bg-white/10\"),\n                        children: [\n                            icon,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopFooter, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat\n    ];\n});\n_c = DesktopFooter;\nvar _c;\n$RefreshReg$(_c, \"DesktopFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbGF5b3V0L0Rlc2t0b3BGb290ZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFRcUI7QUFDK0M7QUFPN0QsU0FBU00sY0FBYyxLQUE4QztRQUE5QyxFQUFFQyxTQUFTLEVBQUVDLFdBQVcsRUFBc0IsR0FBOUM7O0lBQzVCLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdKLGtGQUFhQTtJQUVsQyxnREFBZ0Q7SUFDaEQsTUFBTUssV0FBVztRQUNmO1lBQ0VDLElBQUk7WUFDSkMsb0JBQU0sOERBQUNWLDJHQUFJQTtnQkFBQ1csV0FBVTs7Ozs7O1lBQ3RCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTVAsWUFBWTtRQUM1QjtRQUNBO1lBQ0VHLElBQUk7WUFDSkMsb0JBQU0sOERBQUNSLDJHQUFLQTtnQkFBQ1MsV0FBVTs7Ozs7O1lBQ3ZCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTVAsWUFBWTtRQUM1QjtRQUNBO1lBQ0VHLElBQUk7WUFDSkMsb0JBQU0sOERBQUNaLDJHQUFJQTtnQkFBQ2EsV0FBVTs7Ozs7O1lBQ3RCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTVAsWUFBWTtRQUM1QjtRQUNBO1lBQ0VHLElBQUk7WUFDSkMsb0JBQU0sOERBQUNULDJHQUFNQTtnQkFBQ1UsV0FBVTs7Ozs7O1lBQ3hCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTVAsWUFBWTtRQUM1QjtRQUNBO1lBQ0VHLElBQUk7WUFDSkMsb0JBQU0sOERBQUNYLDJHQUFRQTtnQkFBQ1ksV0FBVTs7Ozs7O1lBQzFCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTU4sV0FBVyxpREFBaUQ7UUFDNUU7S0FDRDtJQUVELHFCQUNFLDhEQUFDTztRQUFPSCxXQUFVO2tCQUNoQiw0RUFBQ0k7WUFBSUosV0FBVTtzQkFDYiw0RUFBQ0k7Z0JBQUlKLFdBQVU7MEJBQ1pILFNBQVNRLEdBQUcsQ0FBQzt3QkFBQyxFQUFFUCxFQUFFLEVBQUVDLElBQUksRUFBRUUsS0FBSyxFQUFFO3lDQUNoQyw4REFBQ0s7d0JBRUNDLFNBQVMsSUFBTVosWUFBWUc7d0JBQzNCRSxXQUFXLGdHQUlWLE9BSENOLGNBQWNJLEtBQ1YsNEVBQ0E7OzRCQUdMQzswQ0FDRCw4REFBQ1M7Z0NBQUtSLFdBQVU7MENBQXVCQzs7Ozs7Ozt1QkFUbENIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFnQm5CO0dBM0RnQkw7O1FBQ09ELDhFQUFhQTs7O0tBRHBCQyIsInNvdXJjZXMiOlsiRDpcXFZTLXByb2plY3RzXFx0cnlcXGFscmF5YS1zdG9yZVxcY29tcG9uZW50c1xcbGF5b3V0XFxEZXNrdG9wRm9vdGVyLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQge1xuICBIb21lLFxuICBTZXR0aW5ncyxcbiAgVXNlcixcbiAgV2FsbGV0LFxuICBTdG9yZSxcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQgeyB1c2VHbG9iYWxDaGF0IH0gZnJvbSAnQC9jb21wb25lbnRzL2NoYXQvR2xvYmFsQ2hhdFByb3ZpZGVyJ1xuXG5pbnRlcmZhY2UgRGVza3RvcEZvb3RlclByb3BzIHtcbiAgYWN0aXZlVGFiOiBzdHJpbmdcbiAgb25UYWJDaGFuZ2U6ICh0YWI6IHN0cmluZykgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gRGVza3RvcEZvb3Rlcih7IGFjdGl2ZVRhYiwgb25UYWJDaGFuZ2UgfTogRGVza3RvcEZvb3RlclByb3BzKSB7XG4gIGNvbnN0IHsgb3BlbkNoYXQgfSA9IHVzZUdsb2JhbENoYXQoKVxuXG4gIC8vIFN0YW5kYXJkaXplZCBuYXZpZ2F0aW9uIGl0ZW1zIG1hdGNoaW5nIG1vYmlsZVxuICBjb25zdCBuYXZJdGVtcyA9IFtcbiAgICB7XG4gICAgICBpZDogXCJwcm9maWxlXCIsXG4gICAgICBpY29uOiA8VXNlciBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgICBsYWJlbDogXCLYrdiz2KfYqNmKXCIsXG4gICAgICBhY3Rpb246ICgpID0+IG9uVGFiQ2hhbmdlKFwicHJvZmlsZVwiKVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwic2hvcFwiLFxuICAgICAgaWNvbjogPFN0b3JlIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICAgIGxhYmVsOiBcItin2YTZhdiq2KzYsVwiLFxuICAgICAgYWN0aW9uOiAoKSA9PiBvblRhYkNoYW5nZShcInNob3BcIilcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcImhvbWVcIixcbiAgICAgIGljb246IDxIb21lIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICAgIGxhYmVsOiBcItin2YTYsdim2YrYs9mK2KlcIixcbiAgICAgIGFjdGlvbjogKCkgPT4gb25UYWJDaGFuZ2UoXCJob21lXCIpXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJ3YWxsZXRcIixcbiAgICAgIGljb246IDxXYWxsZXQgY2xhc3NOYW1lPVwiaC02IHctNlwiIC8+LFxuICAgICAgbGFiZWw6IFwi2KfZhNmF2K3Zgdi42KlcIixcbiAgICAgIGFjdGlvbjogKCkgPT4gb25UYWJDaGFuZ2UoXCJ3YWxsZXRcIilcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcInN1cHBvcnRcIixcbiAgICAgIGljb246IDxTZXR0aW5ncyBjbGFzc05hbWU9XCJoLTYgdy02XCIgLz4sXG4gICAgICBsYWJlbDogXCLYp9mE2K/YudmFINin2YTZgdmG2YpcIixcbiAgICAgIGFjdGlvbjogKCkgPT4gb3BlbkNoYXQoKSAvLyBPcGVucyBjaGF0IGZvciBzdXBwb3J0LCBjb25zaXN0ZW50IHdpdGggbW9iaWxlXG4gICAgfSxcbiAgXVxuXG4gIHJldHVybiAoXG4gICAgPGZvb3RlciBjbGFzc05hbWU9XCJoaWRkZW4gbGc6YmxvY2sgcmVsYXRpdmUgei0xMCBiZy1zbGF0ZS04MDAvNTAgYmFja2Ryb3AtYmx1ci14bCBib3JkZXItdCBib3JkZXItc2xhdGUtNzAwLzUwIG10LTEyXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTggcHktOFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGdhcC04XCI+XG4gICAgICAgICAge25hdkl0ZW1zLm1hcCgoeyBpZCwgaWNvbiwgbGFiZWwgfSkgPT4gKFxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBrZXk9e2lkfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvblRhYkNoYW5nZShpZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIGdhcC0yIHAtNCByb3VuZGVkLTJ4bCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgaG92ZXI6c2NhbGUtMTA1ICR7XG4gICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBpZFxuICAgICAgICAgICAgICAgICAgPyBcImJnLWdyYWRpZW50LXRvLXIgZnJvbS15ZWxsb3ctNDAwIHRvLW9yYW5nZS01MDAgdGV4dC1zbGF0ZS05MDAgc2hhZG93LWxnXCJcbiAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzEwXCJcbiAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtpY29ufVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+e2xhYmVsfTwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZm9vdGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiSG9tZSIsIlNldHRpbmdzIiwiVXNlciIsIldhbGxldCIsIlN0b3JlIiwidXNlR2xvYmFsQ2hhdCIsIkRlc2t0b3BGb290ZXIiLCJhY3RpdmVUYWIiLCJvblRhYkNoYW5nZSIsIm9wZW5DaGF0IiwibmF2SXRlbXMiLCJpZCIsImljb24iLCJjbGFzc05hbWUiLCJsYWJlbCIsImFjdGlvbiIsImZvb3RlciIsImRpdiIsIm1hcCIsImJ1dHRvbiIsIm9uQ2xpY2siLCJzcGFuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/DesktopFooter.tsx\n"));

/***/ })

});