import { WalletData, Transaction } from "@/lib/types"

// ## Mock data for wallet - Replace with Supabase queries
export const mockWalletData: WalletData = {
  balances: [
    {
      currency: "USD",
      amount: 100.00,
      lastUpdated: new Date()
    },
    {
      currency: "SDG",
      amount: 15000,
      lastUpdated: new Date()
    },
    {
      currency: "EGP",
      amount: 250,
      lastUpdated: new Date()
    }
  ],
  selectedCurrency: "USD",
  totalPurchases: 8500,
  transactions: [
    {
      id: "txn_digital_001",
      userId: "user_001",
      walletId: "wallet_001",
      type: "purchase",
      amount: 75,
      currency: "USD",
      description: "🎮 حزمة PUBG Mobile UC الرقمية - 325 UC",
      date: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      status: "completed",
      referenceNumber: "PRD-20241225-160030-DIGITAL",
      hasDigitalContent: true,
      createdAt: new Date(Date.now() - 30 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000),
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_001",
            type: "game_code",
            title: "PUBG Mobile 325 UC - كود رقمي فوري",
            content: "PUBG-UC-2024-EFGH-5678",
            instructions: "🎮 طريقة استخدام كود PUBG Mobile UC:\n\n1. افتح لعبة PUBG Mobile على هاتفك\n2. اذهب إلى المتجر داخل اللعبة\n3. اختر 'استرداد كود' أو 'Redeem Code'\n4. أدخل الكود: PUBG-UC-2024-EFGH-5678\n5. اضغط على 'تأكيد' أو 'Confirm'\n6. سيتم إضافة 325 UC إلى حسابك فوراً!\n\n✅ الكود صالح لجميع السيرفرات\n⏰ صالح لمدة سنة واحدة من تاريخ الشراء\n🔒 لا تشارك الكود مع أي شخص آخر",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 30 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
      }
    },
    {
      id: "txn_digital_002",
      userId: "user_001",
      walletId: "wallet_001",
      type: "purchase",
      amount: 150,
      currency: "USD",
      description: "🎮 حزمة PUBG Mobile UC الرقمية - 660 UC",
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      status: "completed",
      referenceNumber: "PRD-20241222-140015-DIGITAL",
      hasDigitalContent: true,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_002",
            type: "game_code",
            title: "PUBG Mobile 660 UC - كود رقمي فوري",
            content: "PUBG-UC-2024-IJKL-9012",
            instructions: "🎮 طريقة استخدام كود PUBG Mobile UC:\n\n1. افتح لعبة PUBG Mobile على هاتفك\n2. اذهب إلى المتجر داخل اللعبة\n3. اختر 'استرداد كود' أو 'Redeem Code'\n4. أدخل الكود: PUBG-UC-2024-IJKL-9012\n5. اضغط على 'تأكيد' أو 'Confirm'\n6. سيتم إضافة 660 UC إلى حسابك فوراً!\n\n✅ الكود صالح لجميع السيرفرات\n⏰ صالح لمدة سنة واحدة من تاريخ الشراء\n🔒 لا تشارك الكود مع أي شخص آخر",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_digital_003",
      type: "purchase",
      amount: 400,
      currency: "USD",
      description: "🎮 حزمة PUBG Mobile UC الرقمية - 1800 UC",
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
      status: "completed",
      reference: "PRD-20241218-090030-DIGITAL",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_003",
            type: "game_code",
            title: "PUBG Mobile 1800 UC - كود رقمي فوري",
            content: "PUBG-UC-2024-MNOP-3456",
            instructions: "🎮 طريقة استخدام كود PUBG Mobile UC:\n\n1. افتح لعبة PUBG Mobile على هاتفك\n2. اذهب إلى المتجر داخل اللعبة\n3. اختر 'استرداد كود' أو 'Redeem Code'\n4. أدخل الكود: PUBG-UC-2024-MNOP-3456\n5. اضغط على 'تأكيد' أو 'Confirm'\n6. سيتم إضافة 1800 UC إلى حسابك فوراً!\n\n✅ الكود صالح لجميع السيرفرات\n⏰ صالح لمدة سنة واحدة من تاريخ الشراء\n🔒 لا تشارك الكود مع أي شخص آخر\n🏆 حزمة مثالية للاعبين المحترفين",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_digital_004",
      type: "purchase",
      amount: 35,
      currency: "USD",
      description: "💎 Free Fire Diamonds الرقمية - 310 جوهرة",
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      status: "completed",
      reference: "PRD-20241220-110045-DIGITAL",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_004",
            type: "game_code",
            title: "Free Fire 310 Diamonds - كود رقمي فوري",
            content: "FF-DIAMONDS-2024-WXYZ-7890",
            instructions: "💎 طريقة استخدام كود Free Fire Diamonds:\n\n1. افتح لعبة Free Fire على هاتفك\n2. اذهب إلى المتجر داخل اللعبة\n3. اختر 'استرداد كود' أو 'Redeem Code'\n4. أدخل الكود: FF-DIAMONDS-2024-WXYZ-7890\n5. اضغط على 'تأكيد' أو 'Confirm'\n6. سيتم إضافة 310 جوهرة إلى حسابك فوراً!\n\n✅ الكود صالح لجميع السيرفرات\n⏰ صالح لمدة سنة واحدة من تاريخ الشراء\n🔒 لا تشارك الكود مع أي شخص آخر\n💎 استخدم الجواهر لشراء الشخصيات والأسلحة",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_digital_005",
      type: "purchase",
      amount: 20,
      currency: "USD",
      description: "💳 Steam Wallet الرقمية - $20",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      status: "completed",
      reference: "PRD-20241223-150030-DIGITAL",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_005",
            type: "license",
            title: "Steam Wallet $20 - كود رقمي فوري",
            content: "STEAM-WALLET-2024-IJKL-9012",
            instructions: "💻 طريقة استخدام كود Steam Wallet:\n\n1. افتح Steam على جهازك\n2. اذهب إلى 'Games' → 'Activate a Product on Steam'\n3. أدخل الكود: STEAM-WALLET-2024-IJKL-9012\n4. اضغط على 'Next' ثم 'Finish'\n5. سيتم إضافة $20 إلى محفظة Steam فوراً!\n\n✅ الكود صالح عالمياً\n⏰ لا ينتهي صلاحية الكود\n🔒 لا تشارك الكود مع أي شخص آخر\n🛒 استخدم الرصيد لشراء الألعاب والمحتوى",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_digital_006",
      type: "purchase",
      amount: 30,
      currency: "USD",
      description: "💎 Free Fire Diamonds الرقمية - 310 جوهرة",
      date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
      status: "completed",
      reference: "PRD-20241219-120045-DIGITAL",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_digital_006",
            type: "game_code",
            title: "Free Fire 310 Diamonds - كود رقمي فوري",
            content: "FF-DIAMONDS-2024-QRST-3456",
            instructions: "💎 طريقة استخدام كود Free Fire Diamonds:\n\n1. افتح لعبة Free Fire على هاتفك\n2. اذهب إلى المتجر داخل اللعبة\n3. اختر 'استرداد كود' أو 'Redeem Code'\n4. أدخل الكود: FF-DIAMONDS-2024-QRST-3456\n5. اضغط على 'تأكيد' أو 'Confirm'\n6. سيتم إضافة 310 جوهرة إلى حسابك فوراً!\n\n✅ الكود صالح لجميع السيرفرات\n⏰ صالح لمدة سنة واحدة من تاريخ الشراء\n🔒 لا تشارك الكود مع أي شخص آخر\n💎 استخدم الجواهر لشراء الشخصيات والأسلحة",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_001",
      type: "deposit",
      amount: 50.00,
      currency: "USD",
      description: "Initial deposit",
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "USD001"
    },
    {
      id: "txn_002",
      type: "purchase",
      amount: 25.00,
      currency: "USD",
      description: "PUBG UC Purchase",
      date: new Date(Date.now() - 6 * 60 * 60 * 1000),
      status: "completed",
      reference: "USD002",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_001",
            type: "game_code",
            title: "PUBG Mobile UC Code",
            content: "PUBG-UC-2024-ABCD-EFGH-1234",
            instructions: "ادخل إلى لعبة PUBG Mobile، اذهب إلى المتجر، اختر 'استرداد كود'، أدخل الكود أعلاه",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 5 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_003",
      type: "deposit",
      amount: 5000,
      currency: "SDG",
      description: "إيداع عبر فودافون كاش",
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "VF123456789"
    },
    {
      id: "txn_005",
      type: "purchase",
      amount: 1200,
      currency: "SDG",
      description: "شحن ببجي موبايل - 1800 UC",
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "PUB001",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_002",
            type: "game_code",
            title: "PUBG Mobile 1800 UC",
            content: "PUBGM-1800-UC-XYZW-9876-5432",
            instructions: "1. افتح لعبة PUBG Mobile\n2. اذهب إلى المتجر\n3. اختر 'استرداد كود'\n4. أدخل الكود\n5. استمتع بـ 1800 UC!",
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 20 * 60 * 60 * 1000),
          }
        ],
        deliveryMethod: "manual",
        lastUpdated: new Date(Date.now() - 20 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_006",
      type: "purchase",
      amount: 15.00,
      currency: "USD",
      description: "Steam Gift Card $15",
      date: new Date(Date.now() - 2 * 60 * 60 * 1000),
      status: "completed",
      reference: "STM001",
      hasDigitalContent: true,
      digitalContent: {
        status: "ready",
        contents: [
          {
            id: "dc_003",
            type: "coupon",
            title: "Steam Gift Card $15",
            content: "STEAM-GC-15USD-QWER-TYUI-ASDF",
            instructions: "1. اذهب إلى Steam\n2. اختر 'استرداد كود Steam'\n3. أدخل الكود أعلاه\n4. سيتم إضافة $15 إلى محفظة Steam",
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
            isRevealed: false,
            deliveredAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          }
        ],
        deliveryMethod: "instant",
        lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_007",
      type: "purchase",
      amount: 800,
      currency: "SDG",
      description: "كوبون خصم 20% - متجر الألعاب",
      date: new Date(Date.now() - 30 * 60 * 1000),
      status: "completed",
      reference: "COUP001",
      hasDigitalContent: true,
      digitalContent: {
        status: "processing",
        contents: [],
        deliveryMethod: "manual",
        estimatedDeliveryTime: "خلال 30 دقيقة",
        lastUpdated: new Date(Date.now() - 25 * 60 * 1000)
      }
    },
    {
      id: "txn_008",
      type: "deposit",
      amount: 100,
      currency: "EGP",
      description: "إيداع عبر فوري",
      date: new Date(Date.now() - 3 * 60 * 60 * 1000),
      status: "completed",
      reference: "FO987654321"
    },
    {
      id: "txn_004",
      type: "withdrawal",
      amount: 2000,
      currency: "SDG", 
      description: "سحب إلى محفظة خارجية",
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "WD445566"
    },
    {
      id: "txn_009",
      type: "purchase",
      amount: 800,
      currency: "SDG",
      description: "شحن فري فاير - 2200 ماسة",
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: "completed",
      reference: "FF002",
      hasDigitalContent: true,
      digitalContent: {
        status: "pending",
        contents: [],
        deliveryMethod: "database_fetch",
        lastUpdated: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      }
    },
    {
      id: "txn_010",
      type: "purchase",
      amount: 20.00,
      currency: "USD",
      description: "Steam Gift Card $20",
      date: new Date(Date.now() - 4 * 60 * 60 * 1000),
      status: "completed",
      reference: "STM002",
      hasDigitalContent: true,
      digitalContent: {
        status: "pending",
        contents: [],
        deliveryMethod: "database_fetch",
        lastUpdated: new Date(Date.now() - 4 * 60 * 60 * 1000)
      }
    }
  ]
}

// ## Helper function to filter transactions by type - Will be replaced with Supabase filtering
export function filterTransactionsByType(transactions: Transaction[], type?: "deposit" | "withdrawal" | "purchase"): Transaction[] {
  if (!type) return transactions
  return transactions.filter(transaction => transaction.type === type)
}

// ## Helper function to get balance for specific currency - Will be replaced with Supabase query
export function getBalanceForCurrency(walletData: WalletData, currency: string) {
  // Safety check for undefined walletData or balances
  if (!walletData || !walletData.balances || !Array.isArray(walletData.balances)) {
    return 0
  }

  const balance = walletData.balances.find(b => b.currency === currency)
  return balance?.amount || 0
}
