"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        var _formData_packages;\n        // Validate package name\n        const nameValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validatePackageName)(packageForm.name);\n        if (!nameValidation.isValid) {\n            toast({\n                title: \"خطأ في اسم الحزمة\",\n                description: nameValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate package price\n        const priceValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validatePackagePrice)(packageForm.price);\n        if (!priceValidation.isValid) {\n            toast({\n                title: \"خطأ في السعر\",\n                description: priceValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate discount pricing if original price is provided\n        if (packageForm.originalPrice > 0) {\n            const discountValidation = (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.validateDiscountPricing)(packageForm.originalPrice, packageForm.price);\n            if (!discountValidation.isValid) {\n                toast({\n                    title: \"خطأ في الخصم\",\n                    description: discountValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        // Process and validate digital codes\n        const codeLines = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean);\n        const codesValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateDigitalCodes)(codeLines);\n        if (!codesValidation.isValid) {\n            toast({\n                title: \"خطأ في الأكواد الرقمية\",\n                description: codesValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create digital codes with proper structure\n        const digitalCodes = (codesValidation.sanitizedCodes || []).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeDigitalCode)(key),\n                used: false,\n                assignedToOrderId: null,\n                createdAt: new Date()\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.name),\n            amount: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.amount),\n            price: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeNumber)(packageForm.price),\n            originalPrice: packageForm.originalPrice > 0 ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeNumber)(packageForm.originalPrice) : undefined,\n            description: packageForm.description ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.description) : undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            sortOrder: editingPackageIndex !== null ? editingPackageIndex : ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n            digitalCodes\n        };\n        updateFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingPackageIndex !== null ? \"تم تحديث الحزمة بنجاح\" : \"تم إضافة الحزمة بنجاح\"\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        var _formData_packages_index, _formData_packages;\n        const packageName = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : (_formData_packages_index = _formData_packages[index]) === null || _formData_packages_index === void 0 ? void 0 : _formData_packages_index.name) || \"الحزمة \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(packageName, '\"؟\\n\\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.'))) {\n            updateFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(packageName, '\" بنجاح')\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required,\n            options: field.options || []\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Dropdown option management functions\n    const addDropdownOption = ()=>{\n        const sanitizedText = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(newOptionText);\n        if (!sanitizedText) {\n            toast({\n                title: \"خطأ\",\n                description: \"يرجى إدخال نص الخيار\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Check for duplicates\n        const exists = fieldForm.options.some((opt)=>opt.label === sanitizedText);\n        if (exists) {\n            toast({\n                title: \"خطأ\",\n                description: \"هذا الخيار موجود بالفعل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const newOption = {\n            id: \"option_\".concat(Date.now()),\n            value: sanitizedText.toLowerCase().replace(/\\s+/g, '_'),\n            label: sanitizedText,\n            sortOrder: fieldForm.options.length,\n            isActive: true\n        };\n        setFieldForm((prev)=>({\n                ...prev,\n                options: [\n                    ...prev.options,\n                    newOption\n                ]\n            }));\n        setNewOptionText(\"\");\n    };\n    const removeDropdownOption = (optionId)=>{\n        setFieldForm((prev)=>({\n                ...prev,\n                options: prev.options.filter((opt)=>opt.id !== optionId)\n            }));\n    };\n    const moveDropdownOption = (optionId, direction)=>{\n        setFieldForm((prev)=>{\n            const options = [\n                ...prev.options\n            ];\n            const index = options.findIndex((opt)=>opt.id === optionId);\n            if (index === -1) return prev;\n            const newIndex = direction === 'up' ? index - 1 : index + 1;\n            if (newIndex < 0 || newIndex >= options.length) return prev[options[index], options[newIndex]] = [\n                options[newIndex],\n                options[index]\n            ];\n            // Update sort orders\n            options.forEach((opt, i)=>{\n                opt.sortOrder = i;\n            });\n            return {\n                ...prev,\n                options\n            };\n        });\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        var _formData_fields;\n        // Validate field label\n        const labelValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateFieldLabel)(fieldForm.label);\n        if (!labelValidation.isValid) {\n            toast({\n                title: \"خطأ في تسمية الحقل\",\n                description: labelValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate dropdown options if it's a dropdown field\n        if (fieldForm.type === 'dropdown') {\n            const optionsValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateDropdownOptions)(fieldForm.options);\n            if (!optionsValidation.isValid) {\n                toast({\n                    title: \"خطأ في خيارات القائمة\",\n                    description: optionsValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(fieldForm.label),\n            placeholder: fieldForm.placeholder ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(fieldForm.placeholder) : undefined,\n            required: fieldForm.required,\n            isActive: true,\n            sortOrder: editingFieldIndex !== null ? editingFieldIndex : ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n            validation: {},\n            options: fieldForm.type === 'dropdown' ? fieldForm.options : undefined\n        };\n        updateFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingFieldIndex !== null ? \"تم تحديث الحقل بنجاح\" : \"تم إضافة الحقل بنجاح\"\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        var _formData_fields_index, _formData_fields;\n        const fieldLabel = ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : (_formData_fields_index = _formData_fields[index]) === null || _formData_fields_index === void 0 ? void 0 : _formData_fields_index.label) || \"الحقل \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(fieldLabel, '\"؟'))) {\n            updateFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(fieldLabel, '\" بنجاح')\n            });\n        }\n    };\n    // Handle cancel with unsaved changes warning\n    const handleCancel = ()=>{\n        if (hasUnsavedChanges) {\n            if (confirm(\"لديك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟\")) {\n                onCancel();\n            }\n        } else {\n            onCancel();\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 659,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 691,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 710,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 697,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 733,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 752,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 754,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 751,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 769,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 775,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 768,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 784,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 767,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 696,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 794,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 793,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 798,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 816,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 819,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 822,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 815,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 827,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 837,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 831,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 846,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 853,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 854,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 852,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 808,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 864,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 865,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 866,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 863,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 791,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 883,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 884,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 881,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 890,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 886,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 906,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 910,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 905,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 916,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 903,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 926,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 920,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 935,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 929,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 902,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 898,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 896,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 951,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 943,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 879,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 974,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 960,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 978,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 959,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 994,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 993,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1015,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1014,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1030,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1028,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1041,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1042,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1040,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1053,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1054,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1052,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1027,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1066,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1065,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1079,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1081,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1085,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1087,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1089,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1086,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1084,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1093,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1077,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1104,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1103,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1000,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1118,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 992,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 991,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1140,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1139,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1138,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1148,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1147,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1160,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1168,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1161,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1159,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1174,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1175,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1173,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1186,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1145,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1200,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1223,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1221,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1229,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1220,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1219,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 658,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"8Y1hvcddtm4Q8zWI4nTeUx6y9nU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1341,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1359,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1340,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1371,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1374,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1419,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1418,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1417,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1424,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1425,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1426,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1427,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1402,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1373,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1370,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1433,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1438,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1439,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1440,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1441,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1437,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1435,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1446,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1458,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1445,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1338,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});