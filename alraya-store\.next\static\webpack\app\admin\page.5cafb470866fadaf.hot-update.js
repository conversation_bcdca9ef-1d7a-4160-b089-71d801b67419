"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 396,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 489,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    formData.image && isValidImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-lg border border-gray-600/50 bg-gray-700/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: formData.image,\n                                                                alt: \"صورة المنتج\",\n                                                                className: \"w-full h-auto object-cover rounded-lg\",\n                                                                style: {\n                                                                    aspectRatio: \"1\"\n                                                                },\n                                                                onError: handleImageError,\n                                                                onLoad: (e)=>{\n                                                                    const img = e.target;\n                                                                    setImageDimensions({\n                                                                        width: img.naturalWidth,\n                                                                        height: img.naturalHeight\n                                                                    });\n                                                                    setIsValidImage(true);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 495,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-between p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white text-xs\",\n                                                                        children: isValidImage && imageDimensions.width > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                imageDimensions.width,\n                                                                                \" \\xd7 \",\n                                                                                imageDimensions.height,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 511,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-8 w-8 rounded-full bg-red-500/80 hover:bg-red-500 shadow-md\",\n                                                                        onClick: ()=>{\n                                                                            setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    image: \"\"\n                                                                                }));\n                                                                            setTempUrl(\"\");\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 526,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"حذف\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 527,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 516,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    value: tempUrl,\n                                                                    onChange: handleInputChange,\n                                                                    placeholder: \"أدخل رابط الصورة أو قم برفع صورة\",\n                                                                    className: \"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700 px-4\",\n                                                                    onClick: handleApplyUrl,\n                                                                    disabled: isTestingUrl,\n                                                                    children: [\n                                                                        isTestingUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"ml-2 h-4 w-4 animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 550,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"ml-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 552,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"تطبيق\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 535,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        id: inputId.current,\n                                                        className: \"hidden\",\n                                                        accept: \"image/*\",\n                                                        onChange: onSelectFile\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 560,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        className: \"w-full border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                                        onClick: handleUploadButtonClick,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"رفع صورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 488,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 584,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 608,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 607,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 631,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 637,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 630,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 642,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 652,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 640,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 629,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 669,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 625,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 686,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 681,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 705,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 719,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 721,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 725,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 720,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 718,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 741,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 750,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 744,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 734,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 713,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 711,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 759,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 758,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 694,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 783,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 782,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 788,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 789,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 787,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 775,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 774,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 819,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 820,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 818,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 830,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 831,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 829,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 844,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 845,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 856,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 857,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 855,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 869,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 867,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 881,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 882,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 880,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 895,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 896,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 893,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 900,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 902,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 903,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 904,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 901,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 899,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 908,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 892,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 920,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 919,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 918,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 815,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 940,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 807,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 806,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 955,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 962,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 975,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 981,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 982,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 990,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 988,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1008,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1000,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 960,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1015,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 952,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 951,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1038,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1037,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1036,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1044,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1035,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1034,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 395,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"oSWCy1Wg9EfPKrmYKV0bkkiPz3U=\");\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1174,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1186,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1189,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1233,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1240,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1241,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1242,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1217,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1188,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1185,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1251,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1255,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1252,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1250,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1261,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1273,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1268,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1260,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1153,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});