"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        var _formData_packages;\n        // Validate package name\n        const nameValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackageName)(packageForm.name);\n        if (!nameValidation.isValid) {\n            toast({\n                title: \"خطأ في اسم الحزمة\",\n                description: nameValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate package price\n        const priceValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackagePrice)(packageForm.price);\n        if (!priceValidation.isValid) {\n            toast({\n                title: \"خطأ في السعر\",\n                description: priceValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate discount pricing if original price is provided\n        if (packageForm.originalPrice > 0) {\n            const discountValidation = (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.validateDiscountPricing)(packageForm.originalPrice, packageForm.price);\n            if (!discountValidation.isValid) {\n                toast({\n                    title: \"خطأ في الخصم\",\n                    description: discountValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        // Process and validate digital codes\n        const codeLines = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean);\n        const codesValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDigitalCodes)(codeLines);\n        if (!codesValidation.isValid) {\n            toast({\n                title: \"خطأ في الأكواد الرقمية\",\n                description: codesValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create digital codes with proper structure\n        const digitalCodes = (codesValidation.sanitizedCodes || []).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeDigitalCode)(key),\n                used: false,\n                assignedToOrderId: null,\n                createdAt: new Date()\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.name),\n            amount: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.amount),\n            price: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.price),\n            originalPrice: packageForm.originalPrice > 0 ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.originalPrice) : undefined,\n            description: packageForm.description ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.description) : undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            sortOrder: editingPackageIndex !== null ? editingPackageIndex : ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n            digitalCodes\n        };\n        updateFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingPackageIndex !== null ? \"تم تحديث الحزمة بنجاح\" : \"تم إضافة الحزمة بنجاح\"\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        var _formData_packages_index, _formData_packages;\n        const packageName = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : (_formData_packages_index = _formData_packages[index]) === null || _formData_packages_index === void 0 ? void 0 : _formData_packages_index.name) || \"الحزمة \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(packageName, '\"؟\\n\\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.'))) {\n            updateFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(packageName, '\" بنجاح')\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required,\n            options: field.options || []\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Dropdown option management functions\n    const addDropdownOption = ()=>{\n        const sanitizedText = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(newOptionText);\n        if (!sanitizedText) {\n            toast({\n                title: \"خطأ\",\n                description: \"يرجى إدخال نص الخيار\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Check for duplicates\n        const exists = fieldForm.options.some((opt)=>opt.label === sanitizedText);\n        if (exists) {\n            toast({\n                title: \"خطأ\",\n                description: \"هذا الخيار موجود بالفعل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const newOption = {\n            id: \"option_\".concat(Date.now()),\n            value: sanitizedText.toLowerCase().replace(/\\s+/g, '_'),\n            label: sanitizedText,\n            sortOrder: fieldForm.options.length,\n            isActive: true\n        };\n        setFieldForm((prev)=>({\n                ...prev,\n                options: [\n                    ...prev.options,\n                    newOption\n                ]\n            }));\n        setNewOptionText(\"\");\n    };\n    const removeDropdownOption = (optionId)=>{\n        setFieldForm((prev)=>({\n                ...prev,\n                options: prev.options.filter((opt)=>opt.id !== optionId)\n            }));\n    };\n    const moveDropdownOption = (optionId, direction)=>{\n        setFieldForm((prev)=>{\n            const options = [\n                ...prev.options\n            ];\n            const index = options.findIndex((opt)=>opt.id === optionId);\n            if (index === -1) return prev;\n            const newIndex = direction === 'up' ? index - 1 : index + 1;\n            if (newIndex < 0 || newIndex >= options.length) return prev[options[index], options[newIndex]] = [\n                options[newIndex],\n                options[index]\n            ];\n            // Update sort orders\n            options.forEach((opt, i)=>{\n                opt.sortOrder = i;\n            });\n            return {\n                ...prev,\n                options\n            };\n        });\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        var _formData_fields;\n        // Validate field label\n        const labelValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateFieldLabel)(fieldForm.label);\n        if (!labelValidation.isValid) {\n            toast({\n                title: \"خطأ في تسمية الحقل\",\n                description: labelValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate dropdown options if it's a dropdown field\n        if (fieldForm.type === 'dropdown') {\n            const optionsValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDropdownOptions)(fieldForm.options);\n            if (!optionsValidation.isValid) {\n                toast({\n                    title: \"خطأ في خيارات القائمة\",\n                    description: optionsValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.label),\n            placeholder: fieldForm.placeholder ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.placeholder) : undefined,\n            required: fieldForm.required,\n            isActive: true,\n            sortOrder: editingFieldIndex !== null ? editingFieldIndex : ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n            validation: {},\n            options: fieldForm.type === 'dropdown' ? fieldForm.options : undefined\n        };\n        updateFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingFieldIndex !== null ? \"تم تحديث الحقل بنجاح\" : \"تم إضافة الحقل بنجاح\"\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        var _formData_fields_index, _formData_fields;\n        const fieldLabel = ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : (_formData_fields_index = _formData_fields[index]) === null || _formData_fields_index === void 0 ? void 0 : _formData_fields_index.label) || \"الحقل \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(fieldLabel, '\"؟'))) {\n            updateFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(fieldLabel, '\" بنجاح')\n            });\n        }\n    };\n    // Handle cancel with unsaved changes warning\n    const handleCancel = ()=>{\n        if (hasUnsavedChanges) {\n            if (confirm(\"لديك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟\")) {\n                onCancel();\n            }\n        } else {\n            onCancel();\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 661,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 680,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 660,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 659,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    validationErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"يرجى تصحيح الأخطاء التالية:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 691,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationErrors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: error\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 695,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 693,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 692,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 11\n                    }, this),\n                    validationWarnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 705,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تحذيرات:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 706,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationWarnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: warning\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 708,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 707,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 704,\n                        columnNumber: 11\n                    }, this),\n                    hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 720,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تغييرات غير محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: \"لديك تغييرات غير محفوظة. تأكد من حفظ عملك قبل المغادرة.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 722,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 719,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 732,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 740,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 739,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 751,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 752,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 750,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 764,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 762,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 774,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 775,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 773,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 793,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 795,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 810,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 809,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 818,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 808,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 729,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 835,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 836,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 843,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 839,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 833,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 858,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 868,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 878,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 872,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 887,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 866,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 894,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 895,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 893,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 907,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 832,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 924,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 925,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 922,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 927,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 945,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 947,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 951,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 946,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 957,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 944,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 961,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 976,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 970,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 960,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 943,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 939,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 985,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 992,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 984,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 920,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1009,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1010,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1008,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1014,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1015,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1013,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1001,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1000,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1036,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1035,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1046,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1044,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1055,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الحالي *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1071,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0.01\",\n                                                            value: packageForm.price,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        price: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1072,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"السعر الذي سيدفعه العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1081,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1070,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الأصلي (اختياري)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1085,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0\",\n                                                            value: packageForm.originalPrice,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        originalPrice: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1086,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"للعرض كخصم (اتركه فارغاً إذا لم يكن هناك خصم)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1084,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1069,\n                                            columnNumber: 15\n                                        }, this),\n                                        packageForm.originalPrice > 0 && packageForm.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3\",\n                                            children: packageForm.originalPrice > packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1104,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-300 text-sm\",\n                                                        children: [\n                                                            \"خصم \",\n                                                            (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.calculateDiscountPercentage)(packageForm.originalPrice, packageForm.price),\n                                                            \"% (توفير \",\n                                                            (packageForm.originalPrice - packageForm.price).toFixed(2),\n                                                            \" دولار)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1105,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1103,\n                                                columnNumber: 21\n                                            }, this) : packageForm.originalPrice === packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1112,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-300 text-sm\",\n                                                        children: \"لا يوجد خصم (السعران متساويان)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1113,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1111,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1119,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-300 text-sm\",\n                                                        children: \"خطأ: السعر الأصلي يجب أن يكون أكبر من السعر الحالي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1120,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1118,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1101,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1068,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1131,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1132,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1130,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1146,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1143,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1150,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1152,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1153,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1154,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1151,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1158,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1142,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1170,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1176,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1169,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1041,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1182,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1033,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1032,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1205,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1204,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1203,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1213,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1225,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1231,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1232,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1233,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1224,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1239,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1240,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1251,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1258,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1250,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1210,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1273,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1265,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1201,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1288,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1287,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1286,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1294,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1285,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1284,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 658,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"8Y1hvcddtm4Q8zWI4nTeUx6y9nU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1406,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1415,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1424,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1405,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1439,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1484,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1483,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1482,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1489,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1491,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1492,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1467,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1438,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1435,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1498,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1501,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1504,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1505,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1506,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1502,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1500,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1511,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1523,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1518,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1510,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1403,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvYWRtaW4vU2ltcGxlUHJvZHVjdEZvcm0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0U7QUFDNEU7QUFVeEg7QUFDd0Q7QUFVN0M7QUFLRDtBQUNpQjtBQUNLO0FBQ1Q7QUFjWjtBQVNoQixTQUFTdUMsa0JBQWtCLEtBQXdFO1FBQXhFLEVBQUVDLE9BQU8sRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUVDLFlBQVksS0FBSyxFQUEwQixHQUF4RTtRQWl0Qm5CQyxnQkE0RGlDQSxvQkF3RkFBOztJQXAyQnRELE1BQU0sRUFBRUMsS0FBSyxFQUFFLEdBQUdqQixrRUFBUUE7SUFFMUIsa0JBQWtCO0lBQ2xCLE1BQU0sQ0FBQ2tCLFdBQVdDLGFBQWEsR0FBRy9DLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ2dELG1CQUFtQkMscUJBQXFCLEdBQUdqRCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUNrRCxrQkFBa0JDLG9CQUFvQixHQUFHbkQsK0NBQVFBLENBQVcsRUFBRTtJQUNyRSxNQUFNLENBQUNvRCxvQkFBb0JDLHNCQUFzQixHQUFHckQsK0NBQVFBLENBQVcsRUFBRTtJQUV6RSxnQkFBZ0I7SUFDaEIsTUFBTSxDQUFDc0QscUJBQXFCQyx1QkFBdUIsR0FBR3ZELCtDQUFRQSxDQUFDO0lBQy9ELE1BQU0sQ0FBQ3dELG1CQUFtQkMscUJBQXFCLEdBQUd6RCwrQ0FBUUEsQ0FBQztJQUMzRCxNQUFNLENBQUMwRCxxQkFBcUJDLHVCQUF1QixHQUFHM0QsK0NBQVFBLENBQWdCO0lBQzlFLE1BQU0sQ0FBQzRELG1CQUFtQkMscUJBQXFCLEdBQUc3RCwrQ0FBUUEsQ0FBZ0I7UUFXOUR3QztJQVRaLE1BQU0sQ0FBQ0ksVUFBVWtCLFlBQVksR0FBRzlELCtDQUFRQSxDQUEyQjtRQUNqRStELE1BQU12QixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVN1QixJQUFJLEtBQUk7UUFDdkJDLGFBQWF4QixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVN3QixXQUFXLEtBQUk7UUFDckNDLFVBQVV6QixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVN5QixRQUFRLEtBQUk7UUFDL0JDLE1BQU0xQixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMwQixJQUFJLEtBQUksRUFBRTtRQUN6QkMsT0FBTzNCLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUzJCLEtBQUssS0FBSTtRQUN6QkMsVUFBVTVCLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBUzRCLFFBQVEsS0FBSSxFQUFFO1FBQ2pDQyxRQUFRN0IsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTNkIsTUFBTSxLQUFJLEVBQUU7UUFDN0JDLFVBQVU5QixDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVM4QixRQUFRLEtBQUksRUFBRTtRQUNqQ0MsVUFBVS9CLENBQUFBLG9CQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVMrQixRQUFRLGNBQWpCL0IsK0JBQUFBLG9CQUFxQjtRQUMvQmdDLFlBQVloQyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNnQyxVQUFVLEtBQUk7UUFDbkNDLGNBQWNqQyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNpQyxZQUFZLEtBQUk7UUFDdkNDLGFBQWFsQyxDQUFBQSxvQkFBQUEsOEJBQUFBLFFBQVNrQyxXQUFXLEtBQUk7UUFDckNDLGdCQUFnQm5DLENBQUFBLG9CQUFBQSw4QkFBQUEsUUFBU21DLGNBQWMsS0FBSTtJQUM3QztJQUlBLDhEQUE4RDtJQUM5RCxNQUFNLENBQUNDLGFBQWFDLGVBQWUsR0FBRzdFLCtDQUFRQSxDQUFDO1FBQzdDK0QsTUFBTTtRQUNOZSxRQUFRO1FBQ1JDLE9BQU87UUFDUEMsZUFBZTtRQUNmaEIsYUFBYTtRQUNiaUIsU0FBUztRQUNUQyxjQUFjO0lBQ2hCO0lBRUEsK0RBQStEO0lBQy9ELE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHcEYsK0NBQVFBLENBQUM7UUFDekNxRixPQUFPO1FBQ1BDLE1BQU07UUFDTkMsYUFBYTtRQUNiQyxVQUFVO1FBQ1ZDLFNBQVMsRUFBRTtJQUNiO0lBRUEsOEJBQThCO0lBQzlCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUczRiwrQ0FBUUEsQ0FBQztJQUVuRCxtREFBbUQ7SUFDbkQsTUFBTSxDQUFDNEYsU0FBU0MsV0FBVyxHQUFHN0YsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDOEYsY0FBY0MsZ0JBQWdCLEdBQUcvRiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNLENBQUNnRyxjQUFjQyxnQkFBZ0IsR0FBR2pHLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ2tHLGlCQUFpQkMsbUJBQW1CLEdBQUduRywrQ0FBUUEsQ0FBQztRQUFFb0csT0FBTztRQUFHQyxRQUFRO0lBQUU7SUFDN0UsTUFBTSxDQUFDQyx1QkFBdUJDLHlCQUF5QixHQUFHdkcsK0NBQVFBLENBQUM7SUFDbkUsTUFBTSxDQUFDd0csVUFBVUMsWUFBWSxHQUFHekcsK0NBQVFBLENBQWdCO0lBQ3hELE1BQU0sQ0FBQzBHLGNBQWNDLGdCQUFnQixHQUFHM0csK0NBQVFBLENBQWdCO0lBQ2hFLE1BQU0sQ0FBQzRHLGFBQWFDLGVBQWUsR0FBRzdHLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQzhHLFVBQVVDLFlBQVksR0FBRy9HLCtDQUFRQSxDQUFDO1FBQUVnSCxHQUFHO1FBQUdDLEdBQUc7UUFBR2IsT0FBTztRQUFHQyxRQUFRO0lBQUU7SUFDM0UsTUFBTSxDQUFDYSxXQUFXQyxhQUFhLEdBQUduSCwrQ0FBUUEsQ0FBQztRQUFFb0csT0FBTztRQUFHQyxRQUFRO0lBQUU7SUFDakUsTUFBTSxDQUFDZSxZQUFZQyxjQUFjLEdBQUdySCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNzSCxXQUFXQyxhQUFhLEdBQUd2SCwrQ0FBUUEsQ0FBQztRQUFFZ0gsR0FBRztRQUFHQyxHQUFHO0lBQUU7SUFDeEQsTUFBTU8sVUFBVXZILDZDQUFNQSxDQUFDLGdCQUF3RCxPQUF4Q3dILEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHO0lBQzVFLE1BQU1DLFdBQVc1SCw2Q0FBTUEsQ0FBbUI7SUFFMUMsd0JBQXdCO0lBQ3hCQyxnREFBU0E7dUNBQUM7WUFDUixNQUFNNEg7a0VBQXFCLENBQUNDO29CQUMxQixJQUFJL0UsbUJBQW1CO3dCQUNyQitFLEVBQUVDLGNBQWM7d0JBQ2hCRCxFQUFFRSxXQUFXLEdBQUc7b0JBQ2xCO2dCQUNGOztZQUVBQyxPQUFPQyxnQkFBZ0IsQ0FBQyxnQkFBZ0JMO1lBQ3hDOytDQUFPLElBQU1JLE9BQU9FLG1CQUFtQixDQUFDLGdCQUFnQk47O1FBQzFEO3NDQUFHO1FBQUM5RTtLQUFrQjtJQUV0Qix5Q0FBeUM7SUFDekMsTUFBTXFGLGlCQUFpQmxJLGtEQUFXQTt5REFBQyxDQUFDbUk7WUFDbEN4RSxZQUFZd0U7WUFDWnJGLHFCQUFxQjtRQUN2Qjt3REFBRyxFQUFFO0lBRUwsTUFBTXNGLFlBQVk7UUFDaEJ6RSxZQUFZO1lBQ1ZDLE1BQU07WUFDTkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLE1BQU0sRUFBRTtZQUNSQyxPQUFPO1lBQ1BDLFVBQVUsRUFBRTtZQUNaQyxRQUFRLEVBQUU7WUFDVkMsVUFBVSxFQUFFO1lBQ1pDLFVBQVU7WUFDVkMsWUFBWTtZQUNaQyxjQUFjO1lBQ2RDLGFBQWE7WUFDYkMsZ0JBQWdCO1FBQ2xCO1FBQ0FrQixXQUFXO1FBQ1g1QyxxQkFBcUI7UUFDckJFLG9CQUFvQixFQUFFO1FBQ3RCRSxzQkFBc0IsRUFBRTtJQUMxQjtJQUVBLGlFQUFpRTtJQUNqRSxNQUFNbUYsb0JBQW9CLENBQUNUO1FBQ3pCbEMsV0FBV2tDLEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSztRQUN6QjNDLGdCQUFnQjtJQUNsQjtJQUVBLE1BQU00QyxpQkFBaUI7UUFDckIsMkJBQTJCO1FBQzNCN0UsWUFBWThFLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRXpFLE9BQU95QjtZQUFRO0lBQ2pEO0lBRUEsTUFBTWlELG1CQUFtQjtRQUN2QjlDLGdCQUFnQjtJQUNsQjtJQUVBLE1BQU0rQywwQkFBMEI7SUFDOUIsMkJBQTJCO0lBQzdCO0lBRUEsTUFBTUMsZUFBZTtJQUNuQiwyQkFBMkI7SUFDN0I7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUMsY0FBYyxDQUFDakI7UUFDbkJBLEVBQUVDLGNBQWM7UUFDaEJYLGNBQWM7UUFDZCxNQUFNNEIsVUFBVSxhQUFhbEIsSUFBSUEsRUFBRW1CLE9BQU8sQ0FBQyxFQUFFLENBQUNELE9BQU8sR0FBR2xCLEVBQUVrQixPQUFPO1FBQ2pFLE1BQU1FLFVBQVUsYUFBYXBCLElBQUlBLEVBQUVtQixPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLEdBQUdwQixFQUFFb0IsT0FBTztRQUNqRTVCLGFBQWE7WUFBRVAsR0FBR2lDO1lBQVNoQyxHQUFHa0M7UUFBUTtJQUN4QztJQUVBLE1BQU1DLGFBQWEsQ0FBQ3JCO1FBQ2xCLElBQUksQ0FBQ1gsWUFBWTtRQUNqQlcsRUFBRUMsY0FBYztRQUNoQixNQUFNaUIsVUFBVSxhQUFhbEIsSUFBSUEsRUFBRW1CLE9BQU8sQ0FBQyxFQUFFLENBQUNELE9BQU8sR0FBR2xCLEVBQUVrQixPQUFPO1FBQ2pFLE1BQU1FLFVBQVUsYUFBYXBCLElBQUlBLEVBQUVtQixPQUFPLENBQUMsRUFBRSxDQUFDQyxPQUFPLEdBQUdwQixFQUFFb0IsT0FBTztRQUVqRSxNQUFNRSxTQUFTSixVQUFVM0IsVUFBVU4sQ0FBQztRQUNwQyxNQUFNc0MsU0FBU0gsVUFBVTdCLFVBQVVMLENBQUM7UUFFcENGLFlBQVk2QixDQUFBQSxPQUFTO2dCQUNuQixHQUFHQSxJQUFJO2dCQUNQNUIsR0FBR1MsS0FBSzhCLEdBQUcsQ0FBQyxHQUFHOUIsS0FBSytCLEdBQUcsQ0FBQ3RDLFVBQVVkLEtBQUssR0FBR3dDLEtBQUt4QyxLQUFLLEVBQUV3QyxLQUFLNUIsQ0FBQyxHQUFHcUM7Z0JBQy9EcEMsR0FBR1EsS0FBSzhCLEdBQUcsQ0FBQyxHQUFHOUIsS0FBSytCLEdBQUcsQ0FBQ3RDLFVBQVViLE1BQU0sR0FBR3VDLEtBQUt2QyxNQUFNLEVBQUV1QyxLQUFLM0IsQ0FBQyxHQUFHcUM7WUFDbkU7UUFFQS9CLGFBQWE7WUFBRVAsR0FBR2lDO1lBQVNoQyxHQUFHa0M7UUFBUTtJQUN4QztJQUVBLE1BQU1NLFlBQVk7UUFDaEJwQyxjQUFjO0lBQ2hCO0lBRUEsK0JBQStCO0lBQy9CLE1BQU1xQyxrQkFBa0IsQ0FBQ0M7UUFDdkI3RixZQUFZOEUsQ0FBQUEsT0FBUztnQkFBRSxHQUFHQSxJQUFJO2dCQUFFekUsT0FBT3dGO1lBQWdCO1FBQ3ZEcEQseUJBQXlCO1FBQ3pCSSxnQkFBZ0I7SUFDbEI7SUFFQSxNQUFNaUQsYUFBYTtRQUNqQjdHLGFBQWE7UUFDYkksb0JBQW9CLEVBQUU7UUFDdEJFLHNCQUFzQixFQUFFO1FBRXhCLElBQUk7Z0JBNkJ1QlQsb0JBZWZBLGtCQU9FQSxvQkFDSkE7WUFuRFIsMkJBQTJCO1lBQzNCLE1BQU1pSCxhQUFhMUgsMEVBQW1CQSxDQUFDUztZQUV2QyxJQUFJLENBQUNpSCxXQUFXQyxPQUFPLEVBQUU7Z0JBQ3ZCM0csb0JBQW9CMEcsV0FBV0UsTUFBTTtnQkFDckMxRyxzQkFBc0J3RyxXQUFXRyxRQUFRO2dCQUV6Q25ILE1BQU07b0JBQ0pvSCxPQUFPO29CQUNQakcsYUFBYSxjQUF1QyxPQUF6QjZGLFdBQVdFLE1BQU0sQ0FBQ0csTUFBTSxFQUFDO29CQUNwREMsU0FBUztnQkFDWDtnQkFFQXBILGFBQWE7Z0JBQ2I7WUFDRjtZQUVBLHVCQUF1QjtZQUN2QixJQUFJOEcsV0FBV0csUUFBUSxDQUFDRSxNQUFNLEdBQUcsR0FBRztnQkFDbEM3RyxzQkFBc0J3RyxXQUFXRyxRQUFRO2dCQUN6Q25ILE1BQU07b0JBQ0pvSCxPQUFPO29CQUNQakcsYUFBYSxHQUE4QixPQUEzQjZGLFdBQVdHLFFBQVEsQ0FBQ0UsTUFBTSxFQUFDO29CQUMzQ0MsU0FBUztnQkFDWDtZQUNGO1lBRUEsOENBQThDO1lBQzlDLE1BQU1DLG1CQUFtQnhILEVBQUFBLHFCQUFBQSxTQUFTd0IsUUFBUSxjQUFqQnhCLHlDQUFBQSxtQkFBbUJ5SCxHQUFHLENBQUMsQ0FBQ0MsS0FBS0MsUUFBVztvQkFDL0QsR0FBR0QsR0FBRztvQkFDTkUsV0FBV0Q7b0JBQ1gsR0FBR3RKLDBFQUE4QkEsQ0FBQ3FKLElBQUk7Z0JBQ3hDLFFBQU8sRUFBRTtZQUVULHVCQUF1QjtZQUN2QixNQUFNRyxjQUF1RTtnQkFDM0UxRyxNQUFNM0IsbUVBQVlBLENBQUNRLFNBQVNtQixJQUFJO2dCQUNoQ0MsYUFBYTVCLG1FQUFZQSxDQUFDUSxTQUFTb0IsV0FBVyxJQUFJO2dCQUNsREMsVUFBVTdCLG1FQUFZQSxDQUFDUSxTQUFTcUIsUUFBUTtnQkFDeENFLE9BQU92QixTQUFTdUIsS0FBSztnQkFDckJNLGNBQWM3QixTQUFTNkIsWUFBWTtnQkFDbkNDLGFBQWE5QixTQUFTOEIsV0FBVztnQkFDakNDLGdCQUFnQi9CLFNBQVMrQixjQUFjO2dCQUN2Q04sUUFBUXpCLEVBQUFBLG1CQUFBQSxTQUFTeUIsTUFBTSxjQUFmekIsdUNBQUFBLGlCQUFpQnlILEdBQUcsQ0FBQyxDQUFDSyxPQUFPSCxRQUFXO3dCQUM5QyxHQUFHRyxLQUFLO3dCQUNSRixXQUFXRDt3QkFDWGxGLE9BQU9qRCxtRUFBWUEsQ0FBQ3NJLE1BQU1yRixLQUFLO3dCQUMvQnRCLE1BQU0yRyxNQUFNM0csSUFBSSxJQUFJLFNBQXVCd0csT0FBZEksS0FBS0MsR0FBRyxJQUFHLEtBQVMsT0FBTkw7b0JBQzdDLFFBQU8sRUFBRTtnQkFDVG5HLFVBQVVnRztnQkFDVjlGLFVBQVUxQixFQUFBQSxxQkFBQUEsU0FBUzBCLFFBQVEsY0FBakIxQix5Q0FBQUEsbUJBQW1CeUgsR0FBRyxDQUFDakksK0RBQVlBLE1BQUssRUFBRTtnQkFDcEQ4QixNQUFNdEIsRUFBQUEsaUJBQUFBLFNBQVNzQixJQUFJLGNBQWJ0QixxQ0FBQUEsZUFBZXlILEdBQUcsQ0FBQ2pJLCtEQUFZQSxNQUFLLEVBQUU7Z0JBQzVDbUMsVUFBVTNCLFNBQVMyQixRQUFRO2dCQUMzQkMsWUFBWTVCLFNBQVM0QixVQUFVO2dCQUMvQnFHLFdBQVdDLFVBQVUsc0JBQXNCO1lBQzdDO1lBRUEsSUFBSUM7WUFFSixJQUFJcEksYUFBYUgsU0FBUztnQkFDeEJ1SSxlQUFlLE1BQU01SiwyRUFBYUEsQ0FBQ3FCLFFBQVF3SSxFQUFFLEVBQUVQO2dCQUMvQzVILE1BQU07b0JBQ0pvSCxPQUFPO29CQUNQakcsYUFBYTtnQkFDZjtZQUNGLE9BQU87Z0JBQ0wrRyxlQUFlLE1BQU03SiwyRUFBYUEsQ0FBQ3VKO2dCQUNuQzVILE1BQU07b0JBQ0pvSCxPQUFPO29CQUNQakcsYUFBYTtnQkFDZjtZQUNGO1lBRUFmLHFCQUFxQjtZQUNyQlIsT0FBT3NJO1FBRVQsRUFBRSxPQUFPRSxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDcEksTUFBTTtnQkFDSm9ILE9BQU87Z0JBQ1BqRyxhQUFhO2dCQUNibUcsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNScEgsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTW9JLG1CQUFtQjtRQUN2QnRHLGVBQWU7WUFDYmQsTUFBTTtZQUNOZSxRQUFRO1lBQ1JDLE9BQU87WUFDUEMsZUFBZTtZQUNmaEIsYUFBYTtZQUNiaUIsU0FBUztZQUNUQyxjQUFjO1FBQ2hCO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTWtHLGlCQUFpQjtRQUNyQmhHLGFBQWE7WUFDWEMsT0FBTztZQUNQQyxNQUFNO1lBQ05DLGFBQWE7WUFDYkMsVUFBVTtZQUNWQyxTQUFTLEVBQUU7UUFDYjtRQUNBRSxpQkFBaUI7SUFDbkI7SUFFQSwrQ0FBK0M7SUFDL0MsTUFBTTBGLG9CQUFvQjtRQUN4QkY7UUFDQXhILHVCQUF1QjtRQUN2QkosdUJBQXVCO0lBQ3pCO0lBRUEsbURBQW1EO0lBQ25ELE1BQU0rSCxjQUFjLENBQUNmO1lBU0hEO1FBUmhCLE1BQU1BLE1BQU0xSCxTQUFTd0IsUUFBUSxDQUFFbUcsTUFBTTtRQUNyQzFGLGVBQWU7WUFDYmQsTUFBTXVHLElBQUl2RyxJQUFJO1lBQ2RlLFFBQVF3RixJQUFJeEYsTUFBTTtZQUNsQkMsT0FBT3VGLElBQUl2RixLQUFLO1lBQ2hCQyxlQUFlc0YsSUFBSXRGLGFBQWEsSUFBSTtZQUNwQ2hCLGFBQWFzRyxJQUFJdEcsV0FBVyxJQUFJO1lBQ2hDaUIsU0FBU3FGLElBQUlyRixPQUFPLElBQUk7WUFDeEJDLGNBQWNvRixFQUFBQSxvQkFBQUEsSUFBSXBGLFlBQVksY0FBaEJvRix3Q0FBQUEsa0JBQWtCRCxHQUFHLENBQUNrQixDQUFBQSxPQUFRQSxLQUFLQyxHQUFHLEVBQUVDLElBQUksQ0FBQyxVQUFTO1FBQ3RFO1FBQ0E5SCx1QkFBdUI0RztRQUN2QmhILHVCQUF1QjtJQUN6QjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNbUksY0FBYztZQXNFaUQ5STtRQXJFbkUsd0JBQXdCO1FBQ3hCLE1BQU0rSSxpQkFBaUI3SiwwRUFBbUJBLENBQUM4QyxZQUFZYixJQUFJO1FBQzNELElBQUksQ0FBQzRILGVBQWU3QixPQUFPLEVBQUU7WUFDM0JqSCxNQUFNO2dCQUNKb0gsT0FBTztnQkFDUGpHLGFBQWEySCxlQUFlVixLQUFLO2dCQUNqQ2QsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBLHlCQUF5QjtRQUN6QixNQUFNeUIsa0JBQWtCN0osMkVBQW9CQSxDQUFDNkMsWUFBWUcsS0FBSztRQUM5RCxJQUFJLENBQUM2RyxnQkFBZ0I5QixPQUFPLEVBQUU7WUFDNUJqSCxNQUFNO2dCQUNKb0gsT0FBTztnQkFDUGpHLGFBQWE0SCxnQkFBZ0JYLEtBQUs7Z0JBQ2xDZCxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsMERBQTBEO1FBQzFELElBQUl2RixZQUFZSSxhQUFhLEdBQUcsR0FBRztZQUNqQyxNQUFNNkcscUJBQXFCN0ssbUVBQXVCQSxDQUFDNEQsWUFBWUksYUFBYSxFQUFFSixZQUFZRyxLQUFLO1lBQy9GLElBQUksQ0FBQzhHLG1CQUFtQi9CLE9BQU8sRUFBRTtnQkFDL0JqSCxNQUFNO29CQUNKb0gsT0FBTztvQkFDUGpHLGFBQWE2SCxtQkFBbUJaLEtBQUs7b0JBQ3JDZCxTQUFTO2dCQUNYO2dCQUNBO1lBQ0Y7UUFDRjtRQUVBLHFDQUFxQztRQUNyQyxNQUFNMkIsWUFBWWxILFlBQVlNLFlBQVksQ0FDdkM2RyxLQUFLLENBQUMsTUFDTjFCLEdBQUcsQ0FBQzJCLENBQUFBLE9BQVFBLEtBQUtDLElBQUksSUFDckJDLE1BQU0sQ0FBQ0M7UUFFVixNQUFNQyxrQkFBa0JwSywyRUFBb0JBLENBQUM4SjtRQUM3QyxJQUFJLENBQUNNLGdCQUFnQnRDLE9BQU8sRUFBRTtZQUM1QmpILE1BQU07Z0JBQ0pvSCxPQUFPO2dCQUNQakcsYUFBYW9JLGdCQUFnQm5CLEtBQUs7Z0JBQ2xDZCxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsNkNBQTZDO1FBQzdDLE1BQU1qRixlQUE4QixDQUFDa0gsZ0JBQWdCQyxjQUFjLElBQUksRUFBRSxFQUFFaEMsR0FBRyxDQUFDLENBQUNtQixLQUFLYyxJQUFPO2dCQUMxRnRCLElBQUksR0FBaUJzQixPQUFkM0IsS0FBS0MsR0FBRyxJQUFHLEtBQUssT0FBRjBCO2dCQUNyQmQsS0FBS25KLDBFQUFtQkEsQ0FBQ21KO2dCQUN6QmUsTUFBTTtnQkFDTkMsbUJBQW1CO2dCQUNuQkMsV0FBVyxJQUFJOUI7WUFDakI7UUFFQSxNQUFNK0IsYUFBNkI7WUFDakMxQixJQUFJdEgsd0JBQXdCLE9BQU9kLFNBQVN3QixRQUFRLENBQUVWLG9CQUFvQixDQUFDc0gsRUFBRSxHQUFHTCxLQUFLQyxHQUFHLEdBQUdqRCxRQUFRO1lBQ25HNUQsTUFBTTNCLG1FQUFZQSxDQUFDd0MsWUFBWWIsSUFBSTtZQUNuQ2UsUUFBUTFDLG1FQUFZQSxDQUFDd0MsWUFBWUUsTUFBTTtZQUN2Q0MsT0FBT3pDLHFFQUFjQSxDQUFDc0MsWUFBWUcsS0FBSztZQUN2Q0MsZUFBZUosWUFBWUksYUFBYSxHQUFHLElBQUkxQyxxRUFBY0EsQ0FBQ3NDLFlBQVlJLGFBQWEsSUFBSThGO1lBQzNGOUcsYUFBYVksWUFBWVosV0FBVyxHQUFHNUIsbUVBQVlBLENBQUN3QyxZQUFZWixXQUFXLElBQUk4RztZQUMvRTdGLFNBQVNMLFlBQVlLLE9BQU87WUFDNUJWLFVBQVU7WUFDVmlHLFdBQVc5Ryx3QkFBd0IsT0FBT0Esc0JBQXVCZCxFQUFBQSxxQkFBQUEsU0FBU3dCLFFBQVEsY0FBakJ4Qix5Q0FBQUEsbUJBQW1Cc0gsTUFBTSxLQUFJO1lBQzlGaEY7UUFDRjtRQUVBbUQsZUFBZSxDQUFDTztZQUNkLE1BQU14RSxXQUFXO21CQUFLd0UsS0FBS3hFLFFBQVEsSUFBSSxFQUFFO2FBQUU7WUFDM0MsSUFBSVYsd0JBQXdCLE1BQU07Z0JBQ2hDVSxRQUFRLENBQUNWLG9CQUFvQixHQUFHZ0o7WUFDbEMsT0FBTztnQkFDTHRJLFNBQVN1SSxJQUFJLENBQUNEO1lBQ2hCO1lBQ0EsT0FBTztnQkFBRSxHQUFHOUQsSUFBSTtnQkFBRXhFO1lBQVM7UUFDN0I7UUFFQXZCLE1BQU07WUFDSm9ILE9BQU87WUFDUGpHLGFBQWFOLHdCQUF3QixPQUFPLDBCQUEwQjtRQUN4RTtRQUVBSCx1QkFBdUI7UUFDdkI0SDtJQUNGO0lBRUEsaUJBQWlCO0lBQ2pCLE1BQU15QixnQkFBZ0IsQ0FBQ3JDO1lBQ0QzSCwwQkFBQUE7UUFBcEIsTUFBTWlLLGNBQWNqSyxFQUFBQSxxQkFBQUEsU0FBU3dCLFFBQVEsY0FBakJ4QiwwQ0FBQUEsMkJBQUFBLGtCQUFtQixDQUFDMkgsTUFBTSxjQUExQjNILCtDQUFBQSx5QkFBNEJtQixJQUFJLEtBQUksVUFBb0IsT0FBVndHLFFBQVE7UUFFMUUsSUFBSXVDLFFBQVEsd0JBQW9DLE9BQVpELGFBQVksNkRBQTJEO1lBQ3pHeEUsZUFBZSxDQUFDTztvQkFFSkE7dUJBRmM7b0JBQ3hCLEdBQUdBLElBQUk7b0JBQ1B4RSxVQUFVd0UsRUFBQUEsaUJBQUFBLEtBQUt4RSxRQUFRLGNBQWJ3RSxxQ0FBQUEsZUFBZXNELE1BQU0sQ0FBQyxDQUFDYSxHQUFHVCxJQUFNQSxNQUFNL0IsV0FBVSxFQUFFO2dCQUM5RDs7WUFFQTFILE1BQU07Z0JBQ0pvSCxPQUFPO2dCQUNQakcsYUFBYSxXQUF1QixPQUFaNkksYUFBWTtZQUN0QztRQUNGO0lBQ0Y7SUFFQSwyQ0FBMkM7SUFDM0MsTUFBTUcsa0JBQWtCO1FBQ3RCNUI7UUFDQXZILHFCQUFxQjtRQUNyQkoscUJBQXFCO0lBQ3ZCO0lBRUEsK0NBQStDO0lBQy9DLE1BQU13SixZQUFZLENBQUMxQztRQUNqQixNQUFNRyxRQUFROUgsU0FBU3lCLE1BQU0sQ0FBRWtHLE1BQU07UUFDckNuRixhQUFhO1lBQ1hDLE9BQU9xRixNQUFNckYsS0FBSztZQUNsQkMsTUFBTW9GLE1BQU1wRixJQUFJO1lBQ2hCQyxhQUFhbUYsTUFBTW5GLFdBQVcsSUFBSTtZQUNsQ0MsVUFBVWtGLE1BQU1sRixRQUFRO1lBQ3hCQyxTQUFTaUYsTUFBTWpGLE9BQU8sSUFBSSxFQUFFO1FBQzlCO1FBQ0E1QixxQkFBcUIwRztRQUNyQjlHLHFCQUFxQjtJQUN2QjtJQUVBLHVDQUF1QztJQUN2QyxNQUFNeUosb0JBQW9CO1FBQ3hCLE1BQU1DLGdCQUFnQi9LLG1FQUFZQSxDQUFDc0Q7UUFDbkMsSUFBSSxDQUFDeUgsZUFBZTtZQUNsQnRLLE1BQU07Z0JBQ0pvSCxPQUFPO2dCQUNQakcsYUFBYTtnQkFDYm1HLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSx1QkFBdUI7UUFDdkIsTUFBTWlELFNBQVNqSSxVQUFVTSxPQUFPLENBQUM0SCxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlqSSxLQUFLLEtBQUs4SDtRQUMzRCxJQUFJQyxRQUFRO1lBQ1Z2SyxNQUFNO2dCQUNKb0gsT0FBTztnQkFDUGpHLGFBQWE7Z0JBQ2JtRyxTQUFTO1lBQ1g7WUFDQTtRQUNGO1FBRUEsTUFBTW9ELFlBQTRCO1lBQ2hDdkMsSUFBSSxVQUFxQixPQUFYTCxLQUFLQyxHQUFHO1lBQ3RCbEMsT0FBT3lFLGNBQWNLLFdBQVcsR0FBR0MsT0FBTyxDQUFDLFFBQVE7WUFDbkRwSSxPQUFPOEg7WUFDUDNDLFdBQVdyRixVQUFVTSxPQUFPLENBQUN5RSxNQUFNO1lBQ25DM0YsVUFBVTtRQUNaO1FBRUFhLGFBQWF3RCxDQUFBQSxPQUFTO2dCQUNwQixHQUFHQSxJQUFJO2dCQUNQbkQsU0FBUzt1QkFBSW1ELEtBQUtuRCxPQUFPO29CQUFFOEg7aUJBQVU7WUFDdkM7UUFDQTVILGlCQUFpQjtJQUNuQjtJQUVBLE1BQU0rSCx1QkFBdUIsQ0FBQ0M7UUFDNUJ2SSxhQUFhd0QsQ0FBQUEsT0FBUztnQkFDcEIsR0FBR0EsSUFBSTtnQkFDUG5ELFNBQVNtRCxLQUFLbkQsT0FBTyxDQUFDeUcsTUFBTSxDQUFDb0IsQ0FBQUEsTUFBT0EsSUFBSXRDLEVBQUUsS0FBSzJDO1lBQ2pEO0lBQ0Y7SUFFQSxNQUFNQyxxQkFBcUIsQ0FBQ0QsVUFBa0JFO1FBQzVDekksYUFBYXdELENBQUFBO1lBQ1gsTUFBTW5ELFVBQVU7bUJBQUltRCxLQUFLbkQsT0FBTzthQUFDO1lBQ2pDLE1BQU04RSxRQUFROUUsUUFBUXFJLFNBQVMsQ0FBQ1IsQ0FBQUEsTUFBT0EsSUFBSXRDLEVBQUUsS0FBSzJDO1lBRWxELElBQUlwRCxVQUFVLENBQUMsR0FBRyxPQUFPM0I7WUFFekIsTUFBTW1GLFdBQVdGLGNBQWMsT0FBT3RELFFBQVEsSUFBSUEsUUFBUTtZQUMxRCxJQUFJd0QsV0FBVyxLQUFLQSxZQUFZdEksUUFBUXlFLE1BQU0sRUFBRSxPQUFPdEIsSUFHdkQsQ0FBQ25ELE9BQU8sQ0FBQzhFLE1BQU0sRUFBRTlFLE9BQU8sQ0FBQ3NJLFNBQVMsQ0FBQyxHQUFHO2dCQUFDdEksT0FBTyxDQUFDc0ksU0FBUztnQkFBRXRJLE9BQU8sQ0FBQzhFLE1BQU07YUFBQztZQUV6RSxxQkFBcUI7WUFDckI5RSxRQUFRdUksT0FBTyxDQUFDLENBQUNWLEtBQUtoQjtnQkFDcEJnQixJQUFJOUMsU0FBUyxHQUFHOEI7WUFDbEI7WUFFQSxPQUFPO2dCQUFFLEdBQUcxRCxJQUFJO2dCQUFFbkQ7WUFBUTtRQUM1QjtJQUNGO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU13SSxZQUFZO1lBaUMrQ3JMO1FBaEMvRCx1QkFBdUI7UUFDdkIsTUFBTXNMLGtCQUFrQmpNLHlFQUFrQkEsQ0FBQ2tELFVBQVVFLEtBQUs7UUFDMUQsSUFBSSxDQUFDNkksZ0JBQWdCcEUsT0FBTyxFQUFFO1lBQzVCakgsTUFBTTtnQkFDSm9ILE9BQU87Z0JBQ1BqRyxhQUFha0ssZ0JBQWdCakQsS0FBSztnQkFDbENkLFNBQVM7WUFDWDtZQUNBO1FBQ0Y7UUFFQSxxREFBcUQ7UUFDckQsSUFBSWhGLFVBQVVHLElBQUksS0FBSyxZQUFZO1lBQ2pDLE1BQU02SSxvQkFBb0JqTSw4RUFBdUJBLENBQUNpRCxVQUFVTSxPQUFPO1lBQ25FLElBQUksQ0FBQzBJLGtCQUFrQnJFLE9BQU8sRUFBRTtnQkFDOUJqSCxNQUFNO29CQUNKb0gsT0FBTztvQkFDUGpHLGFBQWFtSyxrQkFBa0JsRCxLQUFLO29CQUNwQ2QsU0FBUztnQkFDWDtnQkFDQTtZQUNGO1FBQ0Y7UUFFQSxNQUFNaUUsV0FBeUI7WUFDN0JwRCxJQUFJcEgsc0JBQXNCLE9BQU9oQixTQUFTeUIsTUFBTSxDQUFFVCxrQkFBa0IsQ0FBQ29ILEVBQUUsR0FBR0wsS0FBS0MsR0FBRyxHQUFHakQsUUFBUTtZQUM3RnJDLE1BQU1ILFVBQVVHLElBQUk7WUFDcEJ2QixNQUFNSCxzQkFBc0IsT0FBT2hCLFNBQVN5QixNQUFNLENBQUVULGtCQUFrQixDQUFDRyxJQUFJLEdBQUcsU0FBb0IsT0FBWDRHLEtBQUtDLEdBQUc7WUFDL0Z2RixPQUFPakQsbUVBQVlBLENBQUMrQyxVQUFVRSxLQUFLO1lBQ25DRSxhQUFhSixVQUFVSSxXQUFXLEdBQUduRCxtRUFBWUEsQ0FBQytDLFVBQVVJLFdBQVcsSUFBSXVGO1lBQzNFdEYsVUFBVUwsVUFBVUssUUFBUTtZQUM1QmpCLFVBQVU7WUFDVmlHLFdBQVc1RyxzQkFBc0IsT0FBT0Esb0JBQXFCaEIsRUFBQUEsbUJBQUFBLFNBQVN5QixNQUFNLGNBQWZ6Qix1Q0FBQUEsaUJBQWlCc0gsTUFBTSxLQUFJO1lBQ3hGTCxZQUFZLENBQUM7WUFDYnBFLFNBQVNOLFVBQVVHLElBQUksS0FBSyxhQUFhSCxVQUFVTSxPQUFPLEdBQUdxRjtRQUMvRDtRQUVBekMsZUFBZSxDQUFDTztZQUNkLE1BQU12RSxTQUFTO21CQUFLdUUsS0FBS3ZFLE1BQU0sSUFBSSxFQUFFO2FBQUU7WUFDdkMsSUFBSVQsc0JBQXNCLE1BQU07Z0JBQzlCUyxNQUFNLENBQUNULGtCQUFrQixHQUFHd0s7WUFDOUIsT0FBTztnQkFDTC9KLE9BQU9zSSxJQUFJLENBQUN5QjtZQUNkO1lBQ0EsT0FBTztnQkFBRSxHQUFHeEYsSUFBSTtnQkFBRXZFO1lBQU87UUFDM0I7UUFFQXhCLE1BQU07WUFDSm9ILE9BQU87WUFDUGpHLGFBQWFKLHNCQUFzQixPQUFPLHlCQUF5QjtRQUNyRTtRQUVBSCxxQkFBcUI7UUFDckIySDtJQUNGO0lBRUEsZUFBZTtJQUNmLE1BQU1pRCxjQUFjLENBQUM5RDtZQUNBM0gsd0JBQUFBO1FBQW5CLE1BQU0wTCxhQUFhMUwsRUFBQUEsbUJBQUFBLFNBQVN5QixNQUFNLGNBQWZ6Qix3Q0FBQUEseUJBQUFBLGdCQUFpQixDQUFDMkgsTUFBTSxjQUF4QjNILDZDQUFBQSx1QkFBMEJ5QyxLQUFLLEtBQUksU0FBbUIsT0FBVmtGLFFBQVE7UUFFdkUsSUFBSXVDLFFBQVEsd0JBQW1DLE9BQVh3QixZQUFXLFFBQU07WUFDbkRqRyxlQUFlLENBQUNPO29CQUVOQTt1QkFGZ0I7b0JBQ3hCLEdBQUdBLElBQUk7b0JBQ1B2RSxRQUFRdUUsRUFBQUEsZUFBQUEsS0FBS3ZFLE1BQU0sY0FBWHVFLG1DQUFBQSxhQUFhc0QsTUFBTSxDQUFDLENBQUNhLEdBQUdULElBQU1BLE1BQU0vQixXQUFVLEVBQUU7Z0JBQzFEOztZQUVBMUgsTUFBTTtnQkFDSm9ILE9BQU87Z0JBQ1BqRyxhQUFhLFdBQXNCLE9BQVhzSyxZQUFXO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLDZDQUE2QztJQUM3QyxNQUFNQyxlQUFlO1FBQ25CLElBQUl2TCxtQkFBbUI7WUFDckIsSUFBSThKLFFBQVEsc0RBQXNEO2dCQUNoRXBLO1lBQ0Y7UUFDRixPQUFPO1lBQ0xBO1FBQ0Y7SUFDRjtRQXNLeUJFO0lBcEt6QixxQkFDRSw4REFBQzRMO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDRDtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDL04sb0pBQU9BO3dDQUFDK04sV0FBVTs7Ozs7Ozs7Ozs7OENBRXJCLDhEQUFDRDs7c0RBQ0MsOERBQUNFOzRDQUFHRCxXQUFVO3NEQUNYOUwsWUFBWSxpQkFBaUI7Ozs7OztzREFFaEMsOERBQUNnTTs0Q0FBRUYsV0FBVTtzREFDVjlMLFlBQVksNkJBQTZCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSWhELDhEQUFDaEIseURBQU1BOzRCQUNMd0ksU0FBUTs0QkFDUnlFLE1BQUs7NEJBQ0xDLFNBQVNOOzRCQUNURSxXQUFVOzs4Q0FFViw4REFBQ2xPLG9KQUFDQTtvQ0FBQ2tPLFdBQVU7Ozs7OztnQ0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNcEMsOERBQUNEO2dCQUFJQyxXQUFVOztvQkFFWnZMLGlCQUFpQmdILE1BQU0sR0FBRyxtQkFDekIsOERBQUMxSSx1REFBS0E7d0JBQUMySSxTQUFROzswQ0FDYiw4REFBQ3JKLG9KQUFhQTtnQ0FBQzJOLFdBQVU7Ozs7OzswQ0FDekIsOERBQUMvTSw0REFBVUE7MENBQUM7Ozs7OzswQ0FDWiw4REFBQ0Qsa0VBQWdCQTswQ0FDZiw0RUFBQ3FOO29DQUFHTCxXQUFVOzhDQUNYdkwsaUJBQWlCbUgsR0FBRyxDQUFDLENBQUNZLE9BQU9WLHNCQUM1Qiw4REFBQ3dFO3NEQUFnQjlEOzJDQUFSVjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFsQm5ILG1CQUFtQjhHLE1BQU0sR0FBRyxtQkFDM0IsOERBQUMxSSx1REFBS0E7OzBDQUNKLDhEQUFDZixvSkFBV0E7Z0NBQUNnTyxXQUFVOzs7Ozs7MENBQ3ZCLDhEQUFDL00sNERBQVVBOzBDQUFDOzs7Ozs7MENBQ1osOERBQUNELGtFQUFnQkE7MENBQ2YsNEVBQUNxTjtvQ0FBR0wsV0FBVTs4Q0FDWHJMLG1CQUFtQmlILEdBQUcsQ0FBQyxDQUFDMkUsU0FBU3pFLHNCQUNoQyw4REFBQ3dFO3NEQUFnQkM7MkNBQVJ6RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQVFsQnZILG1DQUNDLDhEQUFDeEIsdURBQUtBOzswQ0FDSiw4REFBQ1gsb0pBQUlBO2dDQUFDNE4sV0FBVTs7Ozs7OzBDQUNoQiw4REFBQy9NLDREQUFVQTswQ0FBQzs7Ozs7OzBDQUNaLDhEQUFDRCxrRUFBZ0JBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBT3RCLDhEQUFDK007d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTs7a0RBQ1osOERBQUNEO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDL04sb0pBQU9BOzRDQUFDK04sV0FBVTs7Ozs7Ozs7Ozs7b0NBQ2Y7Ozs7Ozs7MENBSVIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7OzBEQUNiLDhEQUFDRDs7a0VBQ0MsOERBQUNuSjt3REFBTW9KLFdBQVU7a0VBQTRDOzs7Ozs7a0VBQzdELDhEQUFDUzt3REFDQzVKLE1BQUs7d0RBQ0xvRCxPQUFPOUYsU0FBU21CLElBQUksSUFBSTt3REFDeEJvTCxVQUFVLENBQUNwSCxJQUFNTSxlQUFlLENBQUNPLE9BQVU7b0VBQUUsR0FBR0EsSUFBSTtvRUFBRTdFLE1BQU1nRSxFQUFFVSxNQUFNLENBQUNDLEtBQUs7Z0VBQUM7d0RBQzNFK0YsV0FBVTt3REFDVmxKLGFBQVk7Ozs7Ozs7Ozs7OzswREFJaEIsOERBQUNpSjs7a0VBQ0MsOERBQUNuSjt3REFBTW9KLFdBQVU7a0VBQTRDOzs7Ozs7a0VBQzdELDhEQUFDUzt3REFDQzVKLE1BQUs7d0RBQ0xvRCxPQUFPOUYsU0FBU3FCLFFBQVEsSUFBSTt3REFDNUJrTCxVQUFVLENBQUNwSCxJQUFNTSxlQUFlLENBQUNPLE9BQVU7b0VBQUUsR0FBR0EsSUFBSTtvRUFBRTNFLFVBQVU4RCxFQUFFVSxNQUFNLENBQUNDLEtBQUs7Z0VBQUM7d0RBQy9FK0YsV0FBVTt3REFDVmxKLGFBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFLbEIsOERBQUNpSjs7MERBQ0MsOERBQUNuSjtnREFBTW9KLFdBQVU7MERBQTRDOzs7Ozs7MERBQzdELDhEQUFDVztnREFDQzFHLE9BQU85RixTQUFTb0IsV0FBVyxJQUFJO2dEQUMvQm1MLFVBQVUsQ0FBQ3BILElBQU1NLGVBQWUsQ0FBQ08sT0FBVTs0REFBRSxHQUFHQSxJQUFJOzREQUFFNUUsYUFBYStELEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSzt3REFBQztnREFDbEYyRyxNQUFNO2dEQUNOWixXQUFVO2dEQUNWbEosYUFBWTs7Ozs7Ozs7Ozs7O2tEQUloQiw4REFBQ2lKOzswREFDQyw4REFBQ25KO2dEQUFNb0osV0FBVTswREFBNEM7Ozs7OzswREFDN0QsOERBQUNTO2dEQUNDNUosTUFBSztnREFDTG9ELE9BQU85RixFQUFBQSxpQkFBQUEsU0FBU3NCLElBQUksY0FBYnRCLHFDQUFBQSxlQUFlNkksSUFBSSxDQUFDLFVBQVM7Z0RBQ3BDMEQsVUFBVSxDQUFDcEgsSUFDVE0sZUFBZSxDQUFDTyxPQUFVOzREQUN4QixHQUFHQSxJQUFJOzREQUNQMUUsTUFBTTZELEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSyxDQUNqQnFELEtBQUssQ0FBQyxLQUNOMUIsR0FBRyxDQUFDLENBQUNpRixNQUFRQSxJQUFJckQsSUFBSSxJQUNyQkMsTUFBTSxDQUFDQzt3REFDWjtnREFFRnNDLFdBQVU7Z0RBQ1ZsSixhQUFZOzs7Ozs7Ozs7Ozs7a0RBSWhCLDhEQUFDaUo7OzBEQUNDLDhEQUFDbko7Z0RBQU1vSixXQUFVOzBEQUE0Qzs7Ozs7OzBEQUU3RCw4REFBQzVNLHNEQUFhQTtnREFDWjBOLGNBQWMzTSxTQUFTdUIsS0FBSyxJQUFJO2dEQUNoQ3FMLGdCQUFnQixDQUFDQyxNQUFRcEgsZUFBZU8sQ0FBQUEsT0FBUzs0REFBRSxHQUFHQSxJQUFJOzREQUFFekUsT0FBT3NMO3dEQUFJO2dEQUN2RXBLLE9BQU07Z0RBQ05xSyxpQkFBZ0I7Z0RBQ2hCQyxhQUFhO2dEQUNiQyxhQUFhO2dEQUNiQyxjQUFjO2dEQUNkcEIsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUtkLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUNwSjtnREFBTW9KLFdBQVU7O2tFQUNmLDhEQUFDUzt3REFDQzVKLE1BQUs7d0RBQ0x3SyxTQUFTbE4sU0FBUzRCLFVBQVUsSUFBSTt3REFDaEMySyxVQUFVLENBQUNwSCxJQUFNakUsWUFBWSxDQUFDOEUsT0FBVTtvRUFBRSxHQUFHQSxJQUFJO29FQUFFcEUsWUFBWXVELEVBQUVVLE1BQU0sQ0FBQ3FILE9BQU87Z0VBQUM7d0RBQ2hGckIsV0FBVTs7Ozs7O2tFQUVaLDhEQUFDc0I7a0VBQUs7Ozs7Ozs7Ozs7OzswREFFUiw4REFBQzFLO2dEQUFNb0osV0FBVTs7a0VBQ2YsOERBQUNTO3dEQUNDNUosTUFBSzt3REFDTHdLLFNBQVNsTixDQUFBQSxxQkFBQUEsU0FBUzJCLFFBQVEsY0FBakIzQixnQ0FBQUEscUJBQXFCO3dEQUM5QnVNLFVBQVUsQ0FBQ3BILElBQU1qRSxZQUFZLENBQUM4RSxPQUFVO29FQUFFLEdBQUdBLElBQUk7b0VBQUVyRSxVQUFVd0QsRUFBRVUsTUFBTSxDQUFDcUgsT0FBTztnRUFBQzt3REFDOUVyQixXQUFVOzs7Ozs7a0VBRVosOERBQUNzQjtrRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU9kLDhEQUFDdkI7OzBDQUNDLDhEQUFDQTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQy9OLG9KQUFPQTtnREFBQytOLFdBQVU7Ozs7OzswREFDbkIsOERBQUNRO2dEQUFHUixXQUFVOzBEQUFtQzs7Ozs7OzBEQUNqRCw4REFBQ3NCO2dEQUFLdEIsV0FBVTs7b0RBQXdCO29EQUFFN0wsRUFBQUEscUJBQUFBLFNBQVN3QixRQUFRLGNBQWpCeEIseUNBQUFBLG1CQUFtQnNILE1BQU0sS0FBSTtvREFBRTs7Ozs7Ozs7Ozs7OztrREFFM0UsOERBQUN2SSx5REFBTUE7d0NBQ0xrTixTQUFTeEQ7d0NBQ1RvRCxXQUFVOzswREFFViw4REFBQ3JPLG9KQUFJQTtnREFBQ3FPLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7Ozs7Ozs7NEJBS3BDN0wsU0FBU3dCLFFBQVEsSUFBSXhCLFNBQVN3QixRQUFRLENBQUM4RixNQUFNLEdBQUcsa0JBQy9DLDhEQUFDc0U7Z0NBQUlDLFdBQVU7MENBQ1o3TCxTQUFTd0IsUUFBUSxDQUFDaUcsR0FBRyxDQUFDLENBQUNDLEtBQUtDLHNCQUMzQiw4REFBQ2lFO3dDQUVDQyxXQUFVOzswREFFViw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7MEVBQ2IsOERBQUN1QjtnRUFBR3ZCLFdBQVU7MEVBQXlDbkUsSUFBSXZHLElBQUk7Ozs7OzswRUFDL0QsOERBQUM0SztnRUFBRUYsV0FBVTs7b0VBQW9DO29FQUFFbkUsSUFBSXZGLEtBQUs7Ozs7Ozs7NERBQzNEdUYsSUFBSXhGLE1BQU0sa0JBQ1QsOERBQUM2SjtnRUFBRUYsV0FBVTswRUFBeUJuRSxJQUFJeEYsTUFBTTs7Ozs7OzREQUVqRHdGLElBQUl0RyxXQUFXLGtCQUNkLDhEQUFDMks7Z0VBQUVGLFdBQVU7MEVBQThCbkUsSUFBSXRHLFdBQVc7Ozs7Ozs7Ozs7OztrRUFHOUQsOERBQUN3Szt3REFBSUMsV0FBVTs7NERBQ1puRSxJQUFJckYsT0FBTyxrQkFDViw4REFBQzhLO2dFQUFLdEIsV0FBVTswRUFBa0U7Ozs7OzswRUFJcEYsOERBQUM5TSx5REFBTUE7Z0VBQ0x3SSxTQUFRO2dFQUNSeUUsTUFBSztnRUFDTEMsU0FBUyxJQUFNdkQsWUFBWWY7Z0VBQzNCa0UsV0FBVTs7a0ZBRVYsOERBQUNwTyxvSkFBSUE7d0VBQUNvTyxXQUFVOzs7Ozs7b0VBQWlCOzs7Ozs7OzBFQUduQyw4REFBQzlNLHlEQUFNQTtnRUFDTHdJLFNBQVE7Z0VBQ1J5RSxNQUFLO2dFQUNMQyxTQUFTLElBQU1qQyxjQUFjckM7Z0VBQzdCa0UsV0FBVTswRUFFViw0RUFBQ25PLG9KQUFNQTtvRUFBQ21PLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzRDQUt2Qm5FLElBQUlwRixZQUFZLElBQUlvRixJQUFJcEYsWUFBWSxDQUFDZ0YsTUFBTSxHQUFHLG1CQUM3Qyw4REFBQ3NFO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2pPLG9KQUFHQTt3REFBQ2lPLFdBQVU7Ozs7OztrRUFDZiw4REFBQ3NCO3dEQUFLdEIsV0FBVTs7NERBQ2JuRSxJQUFJcEYsWUFBWSxDQUFDZ0YsTUFBTTs0REFBQzs7Ozs7Ozs7Ozs7Ozs7dUNBNUMxQkksSUFBSVUsRUFBRTs7Ozs7Ozs7O3FEQW9EakIsOERBQUN3RDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUMvTixvSkFBT0E7d0NBQUMrTixXQUFVOzs7Ozs7a0RBQ25CLDhEQUFDRTt3Q0FBRUYsV0FBVTtrREFBcUI7Ozs7OztrREFDbEMsOERBQUM5TSx5REFBTUE7d0NBQ0xrTixTQUFTeEQ7d0NBQ1RsQixTQUFRO3dDQUNSc0UsV0FBVTs7MERBRVYsOERBQUNyTyxvSkFBSUE7Z0RBQUNxTyxXQUFVOzs7Ozs7NENBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQVF6Qyw4REFBQ0Q7OzBDQUNDLDhEQUFDQTtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzlOLG9KQUFJQTtnREFBQzhOLFdBQVU7Ozs7OzswREFDaEIsOERBQUNRO2dEQUFHUixXQUFVOzBEQUFtQzs7Ozs7OzBEQUNqRCw4REFBQ3NCO2dEQUFLdEIsV0FBVTs7b0RBQXdCO29EQUFFN0wsRUFBQUEsbUJBQUFBLFNBQVN5QixNQUFNLGNBQWZ6Qix1Q0FBQUEsaUJBQWlCc0gsTUFBTSxLQUFJO29EQUFFOzs7Ozs7Ozs7Ozs7O2tEQUV6RSw4REFBQ3ZJLHlEQUFNQTt3Q0FDTGtOLFNBQVM3Qjt3Q0FDVHlCLFdBQVU7OzBEQUVWLDhEQUFDck8sb0pBQUlBO2dEQUFDcU8sV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs0QkFLcEM3TCxTQUFTeUIsTUFBTSxJQUFJekIsU0FBU3lCLE1BQU0sQ0FBQzZGLE1BQU0sR0FBRyxrQkFDM0MsOERBQUNzRTtnQ0FBSUMsV0FBVTswQ0FDWjdMLFNBQVN5QixNQUFNLENBQUNnRyxHQUFHLENBQUMsQ0FBQ0ssT0FBT0gsc0JBQzNCLDhEQUFDaUU7d0NBRUNDLFdBQVU7a0RBRVYsNEVBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDdUI7NERBQUd2QixXQUFVO3NFQUF5Qy9ELE1BQU1yRixLQUFLOzs7Ozs7c0VBQ2xFLDhEQUFDbUo7NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDc0I7b0VBQUt0QixXQUFVOzhFQUNiL0QsTUFBTXBGLElBQUksS0FBSyxTQUFTLE9BQU9vRixNQUFNcEYsSUFBSSxLQUFLLFVBQVUsa0JBQWtCOzs7Ozs7Z0VBRTVFb0YsTUFBTWxGLFFBQVEsa0JBQ2IsOERBQUN1SztvRUFBS3RCLFdBQVU7OEVBQW9EOzs7Ozs7Ozs7Ozs7d0RBS3ZFL0QsTUFBTW5GLFdBQVcsa0JBQ2hCLDhEQUFDb0o7NERBQUVGLFdBQVU7O2dFQUE2QjtnRUFBRS9ELE1BQU1uRixXQUFXO2dFQUFDOzs7Ozs7Ozs7Ozs7OzhEQUdsRSw4REFBQ2lKO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQzlNLHlEQUFNQTs0REFDTHdJLFNBQVE7NERBQ1J5RSxNQUFLOzREQUNMQyxTQUFTLElBQU01QixVQUFVMUM7NERBQ3pCa0UsV0FBVTs7OEVBRVYsOERBQUNwTyxvSkFBSUE7b0VBQUNvTyxXQUFVOzs7Ozs7Z0VBQWlCOzs7Ozs7O3NFQUduQyw4REFBQzlNLHlEQUFNQTs0REFDTHdJLFNBQVE7NERBQ1J5RSxNQUFLOzREQUNMQyxTQUFTLElBQU1SLFlBQVk5RDs0REFDM0JrRSxXQUFVO3NFQUVWLDRFQUFDbk8sb0pBQU1BO2dFQUFDbU8sV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7dUNBcENuQi9ELE1BQU1NLEVBQUU7Ozs7Ozs7OztxREE0Q25CLDhEQUFDd0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDOU4sb0pBQUlBO3dDQUFDOE4sV0FBVTs7Ozs7O2tEQUNoQiw4REFBQ0U7d0NBQUVGLFdBQVU7a0RBQXFCOzs7Ozs7a0RBQ2xDLDhEQUFDOU0seURBQU1BO3dDQUNMa04sU0FBUzdCO3dDQUNUN0MsU0FBUTt3Q0FDUnNFLFdBQVU7OzBEQUVWLDhEQUFDck8sb0pBQUlBO2dEQUFDcU8sV0FBVTs7Ozs7OzRDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRekMsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQzlNLHlEQUFNQTtnQ0FDTGtOLFNBQVNqRjtnQ0FDVHFHLFVBQVVuTjtnQ0FDVjJMLFdBQVU7Z0NBQ1ZHLE1BQUs7MENBRUo5TCwwQkFDQyw4REFBQzBMO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7Ozs7OztzREFDZiw4REFBQ3NCO3NEQUFLOzs7Ozs7Ozs7Ozt5REFHUiw4REFBQ3ZCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQy9OLG9KQUFPQTs0Q0FBQytOLFdBQVU7Ozs7OztzREFDbkIsOERBQUNzQjtzREFBTXBOLFlBQVksaUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJMUMsOERBQUNoQix5REFBTUE7Z0NBQ0x3SSxTQUFRO2dDQUNSMEUsU0FBU047Z0NBQ1QwQixVQUFVbk47Z0NBQ1YyTCxXQUFVO2dDQUNWRyxNQUFLOzBDQUNOOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT0wsOERBQUN4Tix5REFBTUE7Z0JBQUM4TyxNQUFNNU07Z0JBQXFCNk0sY0FBYzVNOzBCQUMvQyw0RUFBQ2xDLGdFQUFhQTtvQkFBQ29OLFdBQVU7O3NDQUN2Qiw4REFBQ25OLCtEQUFZQTtzQ0FDWCw0RUFBQ0MsOERBQVdBO2dDQUFDa04sV0FBVTs7a0RBQ3JCLDhEQUFDL04sb0pBQU9BO3dDQUFDK04sV0FBVTs7Ozs7O29DQUNsQi9LLHdCQUF3QixPQUFPLGlCQUFpQjs7Ozs7Ozs7Ozs7O3NDQUlyRCw4REFBQzhLOzRCQUFJQyxXQUFVOzs4Q0FFYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDRDs7OERBQ0MsOERBQUNuSjtvREFBTW9KLFdBQVU7OERBQWlDOzs7Ozs7OERBQ2xELDhEQUFDUztvREFDQzVKLE1BQUs7b0RBQ0xvRCxPQUFPOUQsWUFBWWIsSUFBSTtvREFDdkJvTCxVQUFVLENBQUNwSCxJQUFNbEQsZUFBZStELENBQUFBLE9BQVM7Z0VBQUUsR0FBR0EsSUFBSTtnRUFBRTdFLE1BQU1nRSxFQUFFVSxNQUFNLENBQUNDLEtBQUs7NERBQUM7b0RBQ3pFbkQsYUFBWTtvREFDWmtKLFdBQVU7Ozs7Ozs7Ozs7OztzREFJZCw4REFBQ0Q7OzhEQUNDLDhEQUFDbko7b0RBQU1vSixXQUFVOzhEQUFpQzs7Ozs7OzhEQUNsRCw4REFBQ1M7b0RBQ0M1SixNQUFLO29EQUNMb0QsT0FBTzlELFlBQVlFLE1BQU07b0RBQ3pCcUssVUFBVSxDQUFDcEgsSUFBTWxELGVBQWUrRCxDQUFBQSxPQUFTO2dFQUFFLEdBQUdBLElBQUk7Z0VBQUU5RCxRQUFRaUQsRUFBRVUsTUFBTSxDQUFDQyxLQUFLOzREQUFDO29EQUMzRW5ELGFBQVk7b0RBQ1prSixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBTWhCLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7O3NFQUNDLDhEQUFDbko7NERBQU1vSixXQUFVO3NFQUFpQzs7Ozs7O3NFQUNsRCw4REFBQ1M7NERBQ0M1SixNQUFLOzREQUNMOEssTUFBSzs0REFDTDVHLEtBQUk7NERBQ0pkLE9BQU85RCxZQUFZRyxLQUFLOzREQUN4Qm9LLFVBQVUsQ0FBQ3BILElBQU1sRCxlQUFlK0QsQ0FBQUEsT0FBUzt3RUFBRSxHQUFHQSxJQUFJO3dFQUFFN0QsT0FBT3NMLE9BQU90SSxFQUFFVSxNQUFNLENBQUNDLEtBQUs7b0VBQUU7NERBQ2xGbkQsYUFBWTs0REFDWmtKLFdBQVU7Ozs7OztzRUFFWiw4REFBQ0U7NERBQUVGLFdBQVU7c0VBQTZCOzs7Ozs7Ozs7Ozs7OERBRzVDLDhEQUFDRDs7c0VBQ0MsOERBQUNuSjs0REFBTW9KLFdBQVU7c0VBQWlDOzs7Ozs7c0VBQ2xELDhEQUFDUzs0REFDQzVKLE1BQUs7NERBQ0w4SyxNQUFLOzREQUNMNUcsS0FBSTs0REFDSmQsT0FBTzlELFlBQVlJLGFBQWE7NERBQ2hDbUssVUFBVSxDQUFDcEgsSUFBTWxELGVBQWUrRCxDQUFBQSxPQUFTO3dFQUFFLEdBQUdBLElBQUk7d0VBQUU1RCxlQUFlcUwsT0FBT3RJLEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSztvRUFBRTs0REFDMUZuRCxhQUFZOzREQUNaa0osV0FBVTs7Ozs7O3NFQUVaLDhEQUFDRTs0REFBRUYsV0FBVTtzRUFBNkI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozt3Q0FLN0M3SixZQUFZSSxhQUFhLEdBQUcsS0FBS0osWUFBWUcsS0FBSyxHQUFHLG1CQUNwRCw4REFBQ3lKOzRDQUFJQyxXQUFVO3NEQUNaN0osWUFBWUksYUFBYSxHQUFHSixZQUFZRyxLQUFLLGlCQUM1Qyw4REFBQ3lKO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ3NCO3dEQUFLdEIsV0FBVTs7NERBQXlCOzREQUNsQzFOLHVFQUEyQkEsQ0FBQzZELFlBQVlJLGFBQWEsRUFBRUosWUFBWUcsS0FBSzs0REFBRTs0REFDdEVILENBQUFBLFlBQVlJLGFBQWEsR0FBR0osWUFBWUcsS0FBSyxFQUFFdUwsT0FBTyxDQUFDOzREQUFHOzs7Ozs7Ozs7Ozs7dURBR3JFMUwsWUFBWUksYUFBYSxLQUFLSixZQUFZRyxLQUFLLGlCQUNqRCw4REFBQ3lKO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0Q7d0RBQUlDLFdBQVU7Ozs7OztrRUFDZiw4REFBQ3NCO3dEQUFLdEIsV0FBVTtrRUFBMEI7Ozs7Ozs7Ozs7O3FFQUs1Qyw4REFBQ0Q7Z0RBQUlDLFdBQVU7O2tFQUNiLDhEQUFDRDt3REFBSUMsV0FBVTs7Ozs7O2tFQUNmLDhEQUFDc0I7d0RBQUt0QixXQUFVO2tFQUF1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OENBVWpELDhEQUFDRDs7c0RBQ0MsOERBQUNuSjs0Q0FBTW9KLFdBQVU7c0RBQWlDOzs7Ozs7c0RBQ2xELDhEQUFDVzs0Q0FDQzFHLE9BQU85RCxZQUFZWixXQUFXOzRDQUM5Qm1MLFVBQVUsQ0FBQ3BILElBQU1sRCxlQUFlK0QsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFNUUsYUFBYStELEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSztvREFBQzs0Q0FDaEZuRCxhQUFZOzRDQUNaOEosTUFBTTs0Q0FDTlosV0FBVTs7Ozs7Ozs7Ozs7OzhDQUtkLDhEQUFDRDs7c0RBQ0MsOERBQUNBOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ2pPLG9KQUFHQTtvREFBQ2lPLFdBQVU7Ozs7Ozs4REFDZiw4REFBQ3BKO29EQUFNb0osV0FBVTs4REFBNEI7Ozs7Ozs4REFDN0MsOERBQUNzQjtvREFBS3RCLFdBQVU7OERBQXdCOzs7Ozs7Ozs7Ozs7c0RBRzFDLDhEQUFDRDs0Q0FBSUMsV0FBVTs7OERBQ2IsOERBQUNFO29EQUFFRixXQUFVOzhEQUE2Qjs7Ozs7OzhEQUMxQyw4REFBQ0s7b0RBQUdMLFdBQVU7O3NFQUNaLDhEQUFDTTtzRUFBRzs7Ozs7O3NFQUNKLDhEQUFDQTtzRUFBRzs7Ozs7O3NFQUNKLDhEQUFDQTtzRUFBRzs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NEQUlSLDhEQUFDSzs0Q0FDQzFHLE9BQU85RCxZQUFZTSxZQUFZOzRDQUMvQmlLLFVBQVUsQ0FBQ3BILElBQU1sRCxlQUFlK0QsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFMUQsY0FBYzZDLEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSztvREFBQzs0Q0FDakZuRCxhQUFZOzRDQUNaOEosTUFBTTs0Q0FDTlosV0FBVTs7Ozs7Ozs7Ozs7OzhDQUtkLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ3BKO3dDQUFNb0osV0FBVTs7MERBQ2YsOERBQUNTO2dEQUNDNUosTUFBSztnREFDTHdLLFNBQVNsTCxZQUFZSyxPQUFPO2dEQUM1QmtLLFVBQVUsQ0FBQ3BILElBQU1sRCxlQUFlK0QsQ0FBQUEsT0FBUzs0REFBRSxHQUFHQSxJQUFJOzREQUFFM0QsU0FBUzhDLEVBQUVVLE1BQU0sQ0FBQ3FILE9BQU87d0RBQUM7Z0RBQzlFckIsV0FBVTs7Ozs7OzBEQUVaLDhEQUFDc0I7MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU1aLDhEQUFDdkI7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDOU0seURBQU1BO29DQUNMd0ksU0FBUTtvQ0FDUjBFLFNBQVMsSUFBTXRMLHVCQUF1QjtvQ0FDdENrTCxXQUFVOzhDQUNYOzs7Ozs7OENBR0QsOERBQUM5TSx5REFBTUE7b0NBQ0xrTixTQUFTbkQ7b0NBQ1QrQyxXQUFVOzhDQUVUL0ssd0JBQXdCLE9BQU8saUJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPekQsOERBQUN0Qyx5REFBTUE7Z0JBQUM4TyxNQUFNMU07Z0JBQW1CMk0sY0FBYzFNOzBCQUM3Qyw0RUFBQ3BDLGdFQUFhQTtvQkFBQ29OLFdBQVU7O3NDQUN2Qiw4REFBQ25OLCtEQUFZQTtzQ0FDWCw0RUFBQ0MsOERBQVdBO2dDQUFDa04sV0FBVTs7a0RBQ3JCLDhEQUFDOU4sb0pBQUlBO3dDQUFDOE4sV0FBVTs7Ozs7O29DQUNmN0ssc0JBQXNCLE9BQU8sZ0JBQWdCOzs7Ozs7Ozs7Ozs7c0NBSWxELDhEQUFDNEs7NEJBQUlDLFdBQVU7OzhDQUViLDhEQUFDRDs7c0RBQ0MsOERBQUNuSjs0Q0FBTW9KLFdBQVU7c0RBQWlDOzs7Ozs7c0RBQ2xELDhEQUFDUzs0Q0FDQzVKLE1BQUs7NENBQ0xvRCxPQUFPdkQsVUFBVUUsS0FBSzs0Q0FDdEI4SixVQUFVLENBQUNwSCxJQUFNM0MsYUFBYXdELENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRXZELE9BQU8wQyxFQUFFVSxNQUFNLENBQUNDLEtBQUs7b0RBQUM7NENBQ3hFbkQsYUFBWTs0Q0FDWmtKLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FLZCw4REFBQ0Q7O3NEQUNDLDhEQUFDbko7NENBQU1vSixXQUFVO3NEQUFpQzs7Ozs7O3NEQUNsRCw4REFBQzhCOzRDQUNDN0gsT0FBT3ZELFVBQVVHLElBQUk7NENBQ3JCNkosVUFBVSxDQUFDcEgsSUFBTTNDLGFBQWF3RCxDQUFBQSxPQUFTO3dEQUFFLEdBQUdBLElBQUk7d0RBQUV0RCxNQUFNeUMsRUFBRVUsTUFBTSxDQUFDQyxLQUFLO29EQUFnQzs0Q0FDdEcrRixXQUFVOzs4REFFViw4REFBQytCO29EQUFPOUgsT0FBTTs4REFBTzs7Ozs7OzhEQUNyQiw4REFBQzhIO29EQUFPOUgsT0FBTTs4REFBUTs7Ozs7OzhEQUN0Qiw4REFBQzhIO29EQUFPOUgsT0FBTTs4REFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUszQiw4REFBQzhGOztzREFDQyw4REFBQ25KOzRDQUFNb0osV0FBVTtzREFBaUM7Ozs7OztzREFDbEQsOERBQUNTOzRDQUNDNUosTUFBSzs0Q0FDTG9ELE9BQU92RCxVQUFVSSxXQUFXOzRDQUM1QjRKLFVBQVUsQ0FBQ3BILElBQU0zQyxhQUFhd0QsQ0FBQUEsT0FBUzt3REFBRSxHQUFHQSxJQUFJO3dEQUFFckQsYUFBYXdDLEVBQUVVLE1BQU0sQ0FBQ0MsS0FBSztvREFBQzs0Q0FDOUVuRCxhQUFZOzRDQUNaa0osV0FBVTs7Ozs7Ozs7Ozs7OzhDQUtkLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNTOzRDQUNDNUosTUFBSzs0Q0FDTDBGLElBQUc7NENBQ0g4RSxTQUFTM0ssVUFBVUssUUFBUTs0Q0FDM0IySixVQUFVLENBQUNwSCxJQUFNM0MsYUFBYXdELENBQUFBLE9BQVM7d0RBQUUsR0FBR0EsSUFBSTt3REFBRXBELFVBQVV1QyxFQUFFVSxNQUFNLENBQUNxSCxPQUFPO29EQUFDOzRDQUM3RXJCLFdBQVU7Ozs7OztzREFFWiw4REFBQ3BKOzRDQUFNb0wsU0FBUTs0Q0FBaUJoQyxXQUFVO3NEQUFhOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTzNELDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM5TSx5REFBTUE7b0NBQ0x3SSxTQUFRO29DQUNSMEUsU0FBUyxJQUFNcEwscUJBQXFCO29DQUNwQ2dMLFdBQVU7OENBQ1g7Ozs7Ozs4Q0FHRCw4REFBQzlNLHlEQUFNQTtvQ0FDTGtOLFNBQVNaO29DQUNUUSxXQUFVOzhDQUVUN0ssc0JBQXNCLE9BQU8sZ0JBQWdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPdEQsOERBQUN4Qyx5REFBTUE7Z0JBQUM4TyxNQUFNNUo7Z0JBQXVCNkosY0FBYzVKOzBCQUNqRCw0RUFBQ2xGLGdFQUFhQTtvQkFBQ29OLFdBQVU7O3NDQUN2Qiw4REFBQ25OLCtEQUFZQTtzQ0FDWCw0RUFBQ0MsOERBQVdBO2dDQUFDa04sV0FBVTs7a0RBQ3JCLDhEQUFDN04sb0pBQUlBO3dDQUFDNk4sV0FBVTs7Ozs7O29DQUEyQjs7Ozs7Ozs7Ozs7O3dCQUs5Qy9ILDhCQUNDLDhEQUFDZ0s7NEJBQ0NsSyxVQUFVRTs0QkFDVmlLLFFBQVFqSDs0QkFDUmhILFVBQVUsSUFBTTZELHlCQUF5Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPdkQ7R0FodUN3QmhFOztRQUNKWCw4REFBUUE7OztLQURKVztBQXl1Q3hCLFNBQVNtTyxhQUFhLEtBQWlEO1FBQWpELEVBQUVsSyxRQUFRLEVBQUVtSyxNQUFNLEVBQUVqTyxRQUFRLEVBQXFCLEdBQWpEOztJQUNwQixNQUFNa08sWUFBWTNRLDZDQUFNQSxDQUFvQjtJQUM1QyxNQUFNNEgsV0FBVzVILDZDQUFNQSxDQUFtQjtJQUMxQyxNQUFNLENBQUM2RyxVQUFVQyxZQUFZLEdBQUcvRywrQ0FBUUEsQ0FBQztRQUFFZ0gsR0FBRztRQUFJQyxHQUFHO1FBQUliLE9BQU87UUFBS0MsUUFBUTtJQUFJO0lBQ2pGLE1BQU0sQ0FBQ2UsWUFBWUMsY0FBYyxHQUFHckgsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDNEcsYUFBYUMsZUFBZSxHQUFHN0csK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDa0gsV0FBV0MsYUFBYSxHQUFHbkgsK0NBQVFBLENBQUM7UUFBRW9HLE9BQU87UUFBR0MsUUFBUTtJQUFFO0lBRWpFLHFDQUFxQztJQUNyQyxNQUFNd0ssbUJBQW1CLENBQUM5STtRQUN4QixJQUFJLGFBQWFBLEdBQUc7WUFDbEIsT0FBTztnQkFBRWYsR0FBR2UsRUFBRW1CLE9BQU8sQ0FBQyxFQUFFLENBQUNELE9BQU87Z0JBQUVoQyxHQUFHYyxFQUFFbUIsT0FBTyxDQUFDLEVBQUUsQ0FBQ0MsT0FBTztZQUFDO1FBQzVEO1FBQ0EsT0FBTztZQUFFbkMsR0FBR2UsRUFBRWtCLE9BQU87WUFBRWhDLEdBQUdjLEVBQUVvQixPQUFPO1FBQUM7SUFDdEM7SUFFQSxNQUFNSCxjQUFjLENBQUNqQjtRQUNuQkEsRUFBRUMsY0FBYztRQUNoQlgsY0FBYztJQUNoQjtJQUVBLE1BQU0rQixhQUFhLENBQUNyQjtRQUNsQixJQUFJLENBQUNYLGNBQWMsQ0FBQ1MsU0FBU2lKLE9BQU8sRUFBRTtRQUN0Qy9JLEVBQUVDLGNBQWM7UUFFaEIsTUFBTStJLE9BQU9sSixTQUFTaUosT0FBTyxDQUFDRSxxQkFBcUI7UUFDbkQsTUFBTUMsTUFBTUosaUJBQWlCOUk7UUFFN0IsTUFBTW1KLFlBQVlELElBQUlqSyxDQUFDLEdBQUcrSixLQUFLSSxJQUFJO1FBQ25DLE1BQU1DLFlBQVlILElBQUloSyxDQUFDLEdBQUc4SixLQUFLTSxHQUFHO1FBRWxDLHFDQUFxQztRQUNyQyxNQUFNQyxPQUFPN0osS0FBSzhCLEdBQUcsQ0FBQyxHQUFHOUIsS0FBSytCLEdBQUcsQ0FBQzBILFlBQVlwSyxTQUFTVixLQUFLLEdBQUcsR0FBRzJLLEtBQUszSyxLQUFLLEdBQUdVLFNBQVNWLEtBQUs7UUFDN0YsTUFBTW1MLE9BQU85SixLQUFLOEIsR0FBRyxDQUFDLEdBQUc5QixLQUFLK0IsR0FBRyxDQUFDNEgsWUFBWXRLLFNBQVNULE1BQU0sR0FBRyxHQUFHMEssS0FBSzFLLE1BQU0sR0FBR1MsU0FBU1QsTUFBTTtRQUVoR1UsWUFBWTZCLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRTVCLEdBQUdzSztnQkFBTXJLLEdBQUdzSztZQUFLO0lBQ25EO0lBRUEsTUFBTTlILFlBQVk7UUFDaEJwQyxjQUFjO0lBQ2hCO0lBRUEsTUFBTW1LLGFBQWE7UUFDakIsTUFBTUMsU0FBU2IsVUFBVUUsT0FBTztRQUNoQyxNQUFNM00sUUFBUTBELFNBQVNpSixPQUFPO1FBQzlCLElBQUksQ0FBQ1csVUFBVSxDQUFDdE4sT0FBTztRQUV2QixNQUFNdU4sTUFBTUQsT0FBT0UsVUFBVSxDQUFDO1FBQzlCLElBQUksQ0FBQ0QsS0FBSztRQUVWLDBCQUEwQjtRQUMxQixNQUFNRSxTQUFTek4sTUFBTTBOLFlBQVksR0FBRzFOLE1BQU0yTixXQUFXO1FBQ3JELE1BQU1DLFNBQVM1TixNQUFNNk4sYUFBYSxHQUFHN04sTUFBTThOLFlBQVk7UUFFdkQseUNBQXlDO1FBQ3pDLE1BQU1DLGFBQWE7UUFDbkJULE9BQU9yTCxLQUFLLEdBQUc4TDtRQUNmVCxPQUFPcEwsTUFBTSxHQUFHNkw7UUFFaEIsaUNBQWlDO1FBQ2pDUixJQUFJUyxTQUFTLENBQ1hoTyxPQUNBMkMsU0FBU0UsQ0FBQyxHQUFHNEssUUFDYjlLLFNBQVNHLENBQUMsR0FBRzhLLFFBQ2JqTCxTQUFTVixLQUFLLEdBQUd3TCxRQUNqQjlLLFNBQVNULE1BQU0sR0FBRzBMLFFBQ2xCLEdBQ0EsR0FDQUcsWUFDQUE7UUFHRixvQkFBb0I7UUFDcEIsTUFBTUUsbUJBQW1CWCxPQUFPWSxTQUFTLENBQUMsY0FBYztRQUN4RDFCLE9BQU95QjtJQUNUO0lBRUEsTUFBTUUsY0FBYyxDQUFDMUQ7UUFDbkIsTUFBTTJELFVBQVU5SyxLQUFLK0IsR0FBRyxDQUFDdEMsVUFBVWQsS0FBSyxFQUFFYyxVQUFVYixNQUFNLElBQUk7UUFDOUQsTUFBTW1NLFVBQVUvSyxLQUFLK0IsR0FBRyxDQUFDb0YsTUFBTTJEO1FBQy9CeEwsWUFBWTZCLENBQUFBLE9BQVM7Z0JBQ25CLEdBQUdBLElBQUk7Z0JBQ1B4QyxPQUFPb007Z0JBQ1BuTSxRQUFRbU07Z0JBQ1J4TCxHQUFHUyxLQUFLOEIsR0FBRyxDQUFDLEdBQUc5QixLQUFLK0IsR0FBRyxDQUFDWixLQUFLNUIsQ0FBQyxFQUFFRSxVQUFVZCxLQUFLLEdBQUdvTTtnQkFDbER2TCxHQUFHUSxLQUFLOEIsR0FBRyxDQUFDLEdBQUc5QixLQUFLK0IsR0FBRyxDQUFDWixLQUFLM0IsQ0FBQyxFQUFFQyxVQUFVYixNQUFNLEdBQUdtTTtZQUNyRDtJQUNGO0lBRUEscUJBQ0UsOERBQUNoRTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDOU0seURBQU1BO3dCQUNMMkQsTUFBSzt3QkFDTHNKLE1BQUs7d0JBQ0x6RSxTQUFRO3dCQUNSMEUsU0FBUyxJQUFNeUQsWUFBWTt3QkFDM0I3RCxXQUFVO2tDQUNYOzs7Ozs7a0NBR0QsOERBQUM5TSx5REFBTUE7d0JBQ0wyRCxNQUFLO3dCQUNMc0osTUFBSzt3QkFDTHpFLFNBQVE7d0JBQ1IwRSxTQUFTLElBQU15RCxZQUFZO3dCQUMzQjdELFdBQVU7a0NBQ1g7Ozs7OztrQ0FHRCw4REFBQzlNLHlEQUFNQTt3QkFDTDJELE1BQUs7d0JBQ0xzSixNQUFLO3dCQUNMekUsU0FBUTt3QkFDUjBFLFNBQVMsSUFBTXlELFlBQVk7d0JBQzNCN0QsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7OzBCQUtILDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNFO3dCQUFFRixXQUFVO2tDQUFxQjs7Ozs7O2tDQUVsQyw4REFBQ0Q7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDZ0U7Z0NBQ0NDLEtBQUs3SztnQ0FDTDhLLEtBQUtuTTtnQ0FDTG9NLEtBQUk7Z0NBQ0puRSxXQUFVO2dDQUNWb0UsUUFBUTtvQ0FDTixJQUFJaEwsU0FBU2lKLE9BQU8sRUFBRTt3Q0FDcEIsTUFBTSxFQUFFZ0IsV0FBVyxFQUFFRyxZQUFZLEVBQUUsR0FBR3BLLFNBQVNpSixPQUFPO3dDQUN0RDNKLGFBQWE7NENBQUVmLE9BQU8wTDs0Q0FBYXpMLFFBQVE0TDt3Q0FBYTt3Q0FDeEQsTUFBTXJELE9BQU9uSCxLQUFLK0IsR0FBRyxDQUFDc0ksYUFBYUcsZ0JBQWdCO3dDQUNuRGxMLFlBQVk7NENBQ1ZDLEdBQUcsQ0FBQzhLLGNBQWNsRCxJQUFHLElBQUs7NENBQzFCM0gsR0FBRyxDQUFDZ0wsZUFBZXJELElBQUcsSUFBSzs0Q0FDM0J4SSxPQUFPd0k7NENBQ1B2SSxRQUFRdUk7d0NBQ1Y7d0NBQ0EvSCxlQUFlO29DQUNqQjtnQ0FDRjtnQ0FDQWlNLGFBQWExSjtnQ0FDYjJKLFdBQVd0SjtnQ0FDWHVKLGNBQWN2SjtnQ0FDZHdKLGFBQWE3SjtnQ0FDYjhKLFlBQVl6Sjs7Ozs7OzRCQUliN0MsNkJBQ0MsOERBQUM0SDtnQ0FDQ0MsV0FBVTtnQ0FDVjBFLE9BQU87b0NBQ0xoQyxNQUFNckssU0FBU0UsQ0FBQztvQ0FDaEJxSyxLQUFLdkssU0FBU0csQ0FBQztvQ0FDZmIsT0FBT1UsU0FBU1YsS0FBSztvQ0FDckJDLFFBQVFTLFNBQVNULE1BQU07b0NBQ3ZCK00sWUFBWTtvQ0FDWkMsa0JBQWtCO29DQUNsQkMsYUFBYTtnQ0FDZjtnQ0FDQUMsYUFBYXZLO2dDQUNid0ssY0FBY3hLOztrREFHZCw4REFBQ3dGO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDRDs0Q0FBSUMsV0FBVTtzREFDYiw0RUFBQzdOLG9KQUFJQTtnREFBQzZOLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBS3BCLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU12Qiw4REFBQ2dEO2dCQUFPaUIsS0FBSzlCO2dCQUFXbkMsV0FBVTs7Ozs7OzBCQUVsQyw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBOEI7Ozs7OztrQ0FDM0MsOERBQUNLO3dCQUFHTCxXQUFVOzswQ0FDWiw4REFBQ007MENBQUc7Ozs7OzswQ0FDSiw4REFBQ0E7MENBQUc7Ozs7OzswQ0FDSiw4REFBQ0E7MENBQUc7Ozs7OzswQ0FDSiw4REFBQ0E7MENBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFJUiw4REFBQ1A7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDOU0seURBQU1BO3dCQUNMd0ksU0FBUTt3QkFDUjBFLFNBQVNuTTt3QkFDVCtMLFdBQVU7a0NBQ1g7Ozs7OztrQ0FHRCw4REFBQzlNLHlEQUFNQTt3QkFDTGtOLFNBQVMyQzt3QkFDVHZCLFVBQVUsQ0FBQ3JKO3dCQUNYNkgsV0FBVTs7MENBRVYsOERBQUM3TixvSkFBSUE7Z0NBQUM2TixXQUFVOzs7Ozs7NEJBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTNDO0lBeE5TaUM7TUFBQUEiLCJzb3VyY2VzIjpbIkQ6XFxWUy1wcm9qZWN0c1xcdHJ5XFxhbHJheWEtc3RvcmVcXGNvbXBvbmVudHNcXGFkbWluXFxTaW1wbGVQcm9kdWN0Rm9ybS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0LCB1c2VDYWxsYmFjayB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBQbHVzLCBFZGl0LCBUcmFzaDIsIFgsIFVwbG9hZCwgS2V5LCBBbGVydENpcmNsZSwgUGFja2FnZSwgVHlwZSwgTG9hZGVyMiwgUmVmcmVzaEN3LCBDcm9wLCBTYXZlLCBBbGVydFRyaWFuZ2xlIH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiXG5pbXBvcnQge1xuICBQcm9kdWN0VGVtcGxhdGUsXG4gIFByb2R1Y3RQYWNrYWdlLFxuICBEeW5hbWljRmllbGQsXG4gIERpZ2l0YWxDb2RlLFxuICBEcm9wZG93bk9wdGlvbixcbiAgY2FsY3VsYXRlRGlzY291bnRQZXJjZW50YWdlLFxuICB2YWxpZGF0ZURpc2NvdW50UHJpY2luZyxcbiAgZW5oYW5jZVBhY2thZ2VXaXRoRGlzY291bnRJbmZvXG59IGZyb20gXCJAL2xpYi90eXBlc1wiXG5pbXBvcnQgeyBjcmVhdGVQcm9kdWN0LCB1cGRhdGVQcm9kdWN0IH0gZnJvbSBcIkAvbGliL3NlcnZpY2VzL3Byb2R1Y3RTZXJ2aWNlXCJcbmltcG9ydCB7IGdldENhdGVnb3JpZXMgfSBmcm9tIFwiQC9saWIvc2VydmljZXMvY2F0ZWdvcnlTZXJ2aWNlXCJcbmltcG9ydCB7XG4gIERpYWxvZyxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nSGVhZGVyLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nVHJpZ2dlcixcbiAgRGlhbG9nRGVzY3JpcHRpb24sXG4gIERpYWxvZ0Zvb3Rlcixcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kaWFsb2dcIlxuaW1wb3J0IHtcbiAgQWxlcnQsXG4gIEFsZXJ0RGVzY3JpcHRpb24sXG4gIEFsZXJ0VGl0bGUsXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYWxlcnRcIlxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3VzZS10b2FzdFwiXG5pbXBvcnQgSW1hZ2VVcGxvYWRlciBmcm9tIFwiLi9JbWFnZVVwbG9hZGVyXCJcbmltcG9ydCB7XG4gIHZhbGlkYXRlUHJvZHVjdE5hbWUsXG4gIHZhbGlkYXRlUHJvZHVjdENhdGVnb3J5LFxuICB2YWxpZGF0ZVBhY2thZ2VOYW1lLFxuICB2YWxpZGF0ZVBhY2thZ2VQcmljZSxcbiAgdmFsaWRhdGVEaWdpdGFsQ29kZXMsXG4gIHZhbGlkYXRlRmllbGRMYWJlbCxcbiAgdmFsaWRhdGVEcm9wZG93bk9wdGlvbnMsXG4gIHZhbGlkYXRlUHJvZHVjdERhdGEsXG4gIHZhbGlkYXRlUHJpY2VEaXN0cmlidXRpb24sXG4gIHNhbml0aXplVGV4dCxcbiAgc2FuaXRpemVEaWdpdGFsQ29kZSxcbiAgc2FuaXRpemVOdW1iZXJcbn0gZnJvbSBcIkAvbGliL3V0aWxzL3ZhbGlkYXRpb25cIlxuXG5pbnRlcmZhY2UgU2ltcGxlUHJvZHVjdEZvcm1Qcm9wcyB7XG4gIHByb2R1Y3Q/OiBQcm9kdWN0VGVtcGxhdGVcbiAgb25TYXZlOiAocHJvZHVjdDogUHJvZHVjdFRlbXBsYXRlKSA9PiB2b2lkXG4gIG9uQ2FuY2VsOiAoKSA9PiB2b2lkXG4gIGlzRWRpdGluZz86IGJvb2xlYW5cbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2ltcGxlUHJvZHVjdEZvcm0oeyBwcm9kdWN0LCBvblNhdmUsIG9uQ2FuY2VsLCBpc0VkaXRpbmcgPSBmYWxzZSB9OiBTaW1wbGVQcm9kdWN0Rm9ybVByb3BzKSB7XG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KClcblxuICAvLyBDb3JlIGZvcm0gc3RhdGVcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaGFzVW5zYXZlZENoYW5nZXMsIHNldEhhc1Vuc2F2ZWRDaGFuZ2VzXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbdmFsaWRhdGlvbkVycm9ycywgc2V0VmFsaWRhdGlvbkVycm9yc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG4gIGNvbnN0IFt2YWxpZGF0aW9uV2FybmluZ3MsIHNldFZhbGlkYXRpb25XYXJuaW5nc10gPSB1c2VTdGF0ZTxzdHJpbmdbXT4oW10pXG5cbiAgLy8gRGlhbG9nIHN0YXRlc1xuICBjb25zdCBbaXNQYWNrYWdlRGlhbG9nT3Blbiwgc2V0SXNQYWNrYWdlRGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2lzRmllbGREaWFsb2dPcGVuLCBzZXRJc0ZpZWxkRGlhbG9nT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2VkaXRpbmdQYWNrYWdlSW5kZXgsIHNldEVkaXRpbmdQYWNrYWdlSW5kZXhdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgW2VkaXRpbmdGaWVsZEluZGV4LCBzZXRFZGl0aW5nRmllbGRJbmRleF0gPSB1c2VTdGF0ZTxudW1iZXIgfCBudWxsPihudWxsKVxuXG4gIGNvbnN0IFtmb3JtRGF0YSwgc2V0Rm9ybURhdGFdID0gdXNlU3RhdGU8UGFydGlhbDxQcm9kdWN0VGVtcGxhdGU+Pih7XG4gICAgbmFtZTogcHJvZHVjdD8ubmFtZSB8fCBcIlwiLFxuICAgIGRlc2NyaXB0aW9uOiBwcm9kdWN0Py5kZXNjcmlwdGlvbiB8fCBcIlwiLFxuICAgIGNhdGVnb3J5OiBwcm9kdWN0Py5jYXRlZ29yeSB8fCBcIlwiLFxuICAgIHRhZ3M6IHByb2R1Y3Q/LnRhZ3MgfHwgW10sXG4gICAgaW1hZ2U6IHByb2R1Y3Q/LmltYWdlIHx8IFwiXCIsXG4gICAgcGFja2FnZXM6IHByb2R1Y3Q/LnBhY2thZ2VzIHx8IFtdLFxuICAgIGZpZWxkczogcHJvZHVjdD8uZmllbGRzIHx8IFtdLFxuICAgIGZlYXR1cmVzOiBwcm9kdWN0Py5mZWF0dXJlcyB8fCBbXSxcbiAgICBpc0FjdGl2ZTogcHJvZHVjdD8uaXNBY3RpdmUgPz8gdHJ1ZSxcbiAgICBpc0ZlYXR1cmVkOiBwcm9kdWN0Py5pc0ZlYXR1cmVkIHx8IGZhbHNlLFxuICAgIGRlbGl2ZXJ5VHlwZTogcHJvZHVjdD8uZGVsaXZlcnlUeXBlIHx8IFwiY29kZV9iYXNlZFwiLFxuICAgIHByb2R1Y3RUeXBlOiBwcm9kdWN0Py5wcm9kdWN0VHlwZSB8fCBcImRpZ2l0YWxcIixcbiAgICBwcm9jZXNzaW5nVHlwZTogcHJvZHVjdD8ucHJvY2Vzc2luZ1R5cGUgfHwgXCJpbnN0YW50XCIsXG4gIH0pXG5cblxuXG4gIC8vIFBhY2thZ2UgZGlhbG9nIGZvcm0gc3RhdGUgKHVwZGF0ZWQgZm9yIG5ldyBkaXNjb3VudCBzeXN0ZW0pXG4gIGNvbnN0IFtwYWNrYWdlRm9ybSwgc2V0UGFja2FnZUZvcm1dID0gdXNlU3RhdGUoe1xuICAgIG5hbWU6IFwiXCIsXG4gICAgYW1vdW50OiBcIlwiLFxuICAgIHByaWNlOiAwLFxuICAgIG9yaWdpbmFsUHJpY2U6IDAsIC8vIE9wdGlvbmFsIC0gZm9yIGRpc2NvdW50IGNhbGN1bGF0aW9uXG4gICAgZGVzY3JpcHRpb246IFwiXCIsXG4gICAgcG9wdWxhcjogZmFsc2UsXG4gICAgZGlnaXRhbENvZGVzOiBcIlwiXG4gIH0pXG5cbiAgLy8gRmllbGQgZGlhbG9nIGZvcm0gc3RhdGUgKHVwZGF0ZWQgdG8gc3VwcG9ydCBkcm9wZG93biBmaWVsZHMpXG4gIGNvbnN0IFtmaWVsZEZvcm0sIHNldEZpZWxkRm9ybV0gPSB1c2VTdGF0ZSh7XG4gICAgbGFiZWw6IFwiXCIsXG4gICAgdHlwZTogXCJ1bml2ZXJzYWxfaW5wdXRcIiBhcyBcInVuaXZlcnNhbF9pbnB1dFwiIHwgXCJkcm9wZG93blwiLFxuICAgIHBsYWNlaG9sZGVyOiBcIlwiLFxuICAgIHJlcXVpcmVkOiBmYWxzZSxcbiAgICBvcHRpb25zOiBbXSBhcyBEcm9wZG93bk9wdGlvbltdXG4gIH0pXG5cbiAgLy8gRHJvcGRvd24gb3B0aW9ucyBtYW5hZ2VtZW50XG4gIGNvbnN0IFtuZXdPcHRpb25UZXh0LCBzZXROZXdPcHRpb25UZXh0XSA9IHVzZVN0YXRlKFwiXCIpXG5cbiAgLy8gSW1hZ2UgdXBsb2FkIHN0YXRlICh0ZW1wb3JhcnkgZml4IGZvciB0aGUgZXJyb3IpXG4gIGNvbnN0IFt0ZW1wVXJsLCBzZXRUZW1wVXJsXSA9IHVzZVN0YXRlKFwiXCIpXG4gIGNvbnN0IFtpc1ZhbGlkSW1hZ2UsIHNldElzVmFsaWRJbWFnZV0gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbaXNUZXN0aW5nVXJsLCBzZXRJc1Rlc3RpbmdVcmxdID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtpbWFnZURpbWVuc2lvbnMsIHNldEltYWdlRGltZW5zaW9uc10gPSB1c2VTdGF0ZSh7IHdpZHRoOiAwLCBoZWlnaHQ6IDAgfSlcbiAgY29uc3QgW2lzSW1hZ2VDcm9wRGlhbG9nT3Blbiwgc2V0SXNJbWFnZUNyb3BEaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbaW1hZ2VTcmMsIHNldEltYWdlU3JjXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpbWFnZVByZXZpZXcsIHNldEltYWdlUHJldmlld10gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbaW1hZ2VMb2FkZWQsIHNldEltYWdlTG9hZGVkXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbY3JvcEFyZWEsIHNldENyb3BBcmVhXSA9IHVzZVN0YXRlKHsgeDogMCwgeTogMCwgd2lkdGg6IDAsIGhlaWdodDogMCB9KVxuICBjb25zdCBbaW1hZ2VTaXplLCBzZXRJbWFnZVNpemVdID0gdXNlU3RhdGUoeyB3aWR0aDogMCwgaGVpZ2h0OiAwIH0pXG4gIGNvbnN0IFtpc0RyYWdnaW5nLCBzZXRJc0RyYWdnaW5nXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbZHJhZ1N0YXJ0LCBzZXREcmFnU3RhcnRdID0gdXNlU3RhdGUoeyB4OiAwLCB5OiAwIH0pXG4gIGNvbnN0IGlucHV0SWQgPSB1c2VSZWYoYGltYWdlLXVwbG9hZC0ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gKVxuICBjb25zdCBpbWFnZVJlZiA9IHVzZVJlZjxIVE1MSW1hZ2VFbGVtZW50PihudWxsKVxuXG4gIC8vIFRyYWNrIHVuc2F2ZWQgY2hhbmdlc1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9IChlOiBCZWZvcmVVbmxvYWRFdmVudCkgPT4ge1xuICAgICAgaWYgKGhhc1Vuc2F2ZWRDaGFuZ2VzKSB7XG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgICAgICBlLnJldHVyblZhbHVlID0gJydcbiAgICAgIH1cbiAgICB9XG5cbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKVxuICB9LCBbaGFzVW5zYXZlZENoYW5nZXNdKVxuXG4gIC8vIE1hcmsgZm9ybSBhcyBjaGFuZ2VkIHdoZW4gZGF0YSB1cGRhdGVzXG4gIGNvbnN0IHVwZGF0ZUZvcm1EYXRhID0gdXNlQ2FsbGJhY2soKHVwZGF0ZXI6IChwcmV2OiBQYXJ0aWFsPFByb2R1Y3RUZW1wbGF0ZT4pID0+IFBhcnRpYWw8UHJvZHVjdFRlbXBsYXRlPikgPT4ge1xuICAgIHNldEZvcm1EYXRhKHVwZGF0ZXIpXG4gICAgc2V0SGFzVW5zYXZlZENoYW5nZXModHJ1ZSlcbiAgfSwgW10pXG5cbiAgY29uc3QgcmVzZXRGb3JtID0gKCkgPT4ge1xuICAgIHNldEZvcm1EYXRhKHtcbiAgICAgIG5hbWU6IFwiXCIsXG4gICAgICBkZXNjcmlwdGlvbjogXCJcIixcbiAgICAgIGNhdGVnb3J5OiBcIlwiLFxuICAgICAgdGFnczogW10sXG4gICAgICBpbWFnZTogXCJcIixcbiAgICAgIHBhY2thZ2VzOiBbXSxcbiAgICAgIGZpZWxkczogW10sXG4gICAgICBmZWF0dXJlczogW10sXG4gICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgIGlzRmVhdHVyZWQ6IGZhbHNlLFxuICAgICAgZGVsaXZlcnlUeXBlOiBcImNvZGVfYmFzZWRcIixcbiAgICAgIHByb2R1Y3RUeXBlOiBcImRpZ2l0YWxcIixcbiAgICAgIHByb2Nlc3NpbmdUeXBlOiBcImluc3RhbnRcIixcbiAgICB9KVxuICAgIHNldFRlbXBVcmwoXCJcIilcbiAgICBzZXRIYXNVbnNhdmVkQ2hhbmdlcyhmYWxzZSlcbiAgICBzZXRWYWxpZGF0aW9uRXJyb3JzKFtdKVxuICAgIHNldFZhbGlkYXRpb25XYXJuaW5ncyhbXSlcbiAgfVxuXG4gIC8vIFRlbXBvcmFyeSBoYW5kbGVycyBmb3IgdGhlIG9sZCBpbWFnZSB1cGxvYWQgKHdpbGwgYmUgcmVwbGFjZWQpXG4gIGNvbnN0IGhhbmRsZUlucHV0Q2hhbmdlID0gKGU6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+KSA9PiB7XG4gICAgc2V0VGVtcFVybChlLnRhcmdldC52YWx1ZSlcbiAgICBzZXRJc1ZhbGlkSW1hZ2UodHJ1ZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZUFwcGx5VXJsID0gYXN5bmMgKCkgPT4ge1xuICAgIC8vIFRlbXBvcmFyeSBpbXBsZW1lbnRhdGlvblxuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgaW1hZ2U6IHRlbXBVcmwgfSkpXG4gIH1cblxuICBjb25zdCBoYW5kbGVJbWFnZUVycm9yID0gKCkgPT4ge1xuICAgIHNldElzVmFsaWRJbWFnZShmYWxzZSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZVVwbG9hZEJ1dHRvbkNsaWNrID0gKCkgPT4ge1xuICAgIC8vIFRlbXBvcmFyeSBpbXBsZW1lbnRhdGlvblxuICB9XG5cbiAgY29uc3Qgb25TZWxlY3RGaWxlID0gKCkgPT4ge1xuICAgIC8vIFRlbXBvcmFyeSBpbXBsZW1lbnRhdGlvblxuICB9XG5cbiAgLy8gSW1hZ2UgY3JvcHBpbmcgaGFuZGxlcnNcbiAgY29uc3QgaGFuZGxlU3RhcnQgPSAoZTogUmVhY3QuTW91c2VFdmVudCB8IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpXG4gICAgY29uc3QgY2xpZW50WCA9ICd0b3VjaGVzJyBpbiBlID8gZS50b3VjaGVzWzBdLmNsaWVudFggOiBlLmNsaWVudFhcbiAgICBjb25zdCBjbGllbnRZID0gJ3RvdWNoZXMnIGluIGUgPyBlLnRvdWNoZXNbMF0uY2xpZW50WSA6IGUuY2xpZW50WVxuICAgIHNldERyYWdTdGFydCh7IHg6IGNsaWVudFgsIHk6IGNsaWVudFkgfSlcbiAgfVxuXG4gIGNvbnN0IGhhbmRsZU1vdmUgPSAoZTogUmVhY3QuTW91c2VFdmVudCB8IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBpZiAoIWlzRHJhZ2dpbmcpIHJldHVyblxuICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgIGNvbnN0IGNsaWVudFggPSAndG91Y2hlcycgaW4gZSA/IGUudG91Y2hlc1swXS5jbGllbnRYIDogZS5jbGllbnRYXG4gICAgY29uc3QgY2xpZW50WSA9ICd0b3VjaGVzJyBpbiBlID8gZS50b3VjaGVzWzBdLmNsaWVudFkgOiBlLmNsaWVudFlcblxuICAgIGNvbnN0IGRlbHRhWCA9IGNsaWVudFggLSBkcmFnU3RhcnQueFxuICAgIGNvbnN0IGRlbHRhWSA9IGNsaWVudFkgLSBkcmFnU3RhcnQueVxuXG4gICAgc2V0Q3JvcEFyZWEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHg6IE1hdGgubWF4KDAsIE1hdGgubWluKGltYWdlU2l6ZS53aWR0aCAtIHByZXYud2lkdGgsIHByZXYueCArIGRlbHRhWCkpLFxuICAgICAgeTogTWF0aC5tYXgoMCwgTWF0aC5taW4oaW1hZ2VTaXplLmhlaWdodCAtIHByZXYuaGVpZ2h0LCBwcmV2LnkgKyBkZWx0YVkpKVxuICAgIH0pKVxuXG4gICAgc2V0RHJhZ1N0YXJ0KHsgeDogY2xpZW50WCwgeTogY2xpZW50WSB9KVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRW5kID0gKCkgPT4ge1xuICAgIHNldElzRHJhZ2dpbmcoZmFsc2UpXG4gIH1cblxuICAvLyBIYW5kbGUgaW1hZ2UgY3JvcCBjb21wbGV0aW9uXG4gIGNvbnN0IGhhbmRsZUltYWdlQ3JvcCA9IChjcm9wcGVkSW1hZ2VVcmw6IHN0cmluZykgPT4ge1xuICAgIHNldEZvcm1EYXRhKHByZXYgPT4gKHsgLi4ucHJldiwgaW1hZ2U6IGNyb3BwZWRJbWFnZVVybCB9KSlcbiAgICBzZXRJc0ltYWdlQ3JvcERpYWxvZ09wZW4oZmFsc2UpXG4gICAgc2V0SW1hZ2VQcmV2aWV3KG51bGwpXG4gIH1cblxuICBjb25zdCBoYW5kbGVTYXZlID0gYXN5bmMgKCkgPT4ge1xuICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgIHNldFZhbGlkYXRpb25FcnJvcnMoW10pXG4gICAgc2V0VmFsaWRhdGlvbldhcm5pbmdzKFtdKVxuXG4gICAgdHJ5IHtcbiAgICAgIC8vIENvbXByZWhlbnNpdmUgdmFsaWRhdGlvblxuICAgICAgY29uc3QgdmFsaWRhdGlvbiA9IHZhbGlkYXRlUHJvZHVjdERhdGEoZm9ybURhdGEpXG5cbiAgICAgIGlmICghdmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICAgIHNldFZhbGlkYXRpb25FcnJvcnModmFsaWRhdGlvbi5lcnJvcnMpXG4gICAgICAgIHNldFZhbGlkYXRpb25XYXJuaW5ncyh2YWxpZGF0aW9uLndhcm5pbmdzKVxuXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCLYrti32KMg2YHZiiDYp9mE2KjZitin2YbYp9iqXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGDZitix2KzZiSDYqti12K3ZititICR7dmFsaWRhdGlvbi5lcnJvcnMubGVuZ3RofSDYrti32KMg2YLYqNmEINin2YTYrdmB2LhgLFxuICAgICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcbiAgICAgICAgfSlcblxuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICAvLyBTaG93IHdhcm5pbmdzIGlmIGFueVxuICAgICAgaWYgKHZhbGlkYXRpb24ud2FybmluZ3MubGVuZ3RoID4gMCkge1xuICAgICAgICBzZXRWYWxpZGF0aW9uV2FybmluZ3ModmFsaWRhdGlvbi53YXJuaW5ncylcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcItiq2K3YsNmK2LHYp9iqXCIsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGAke3ZhbGlkYXRpb24ud2FybmluZ3MubGVuZ3RofSDYqtit2LDZitixIC0g2YrZhdmD2YbZgyDYp9mE2YXYqtin2KjYudipINij2Ygg2KrYrdiz2YrZhiDYp9mE2KjZitin2YbYp9iqYCxcbiAgICAgICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgLy8gRW5oYW5jZSBwYWNrYWdlcyB3aXRoIGRpc2NvdW50IGNhbGN1bGF0aW9uc1xuICAgICAgY29uc3QgZW5oYW5jZWRQYWNrYWdlcyA9IGZvcm1EYXRhLnBhY2thZ2VzPy5tYXAoKHBrZywgaW5kZXgpID0+ICh7XG4gICAgICAgIC4uLnBrZyxcbiAgICAgICAgc29ydE9yZGVyOiBpbmRleCxcbiAgICAgICAgLi4uZW5oYW5jZVBhY2thZ2VXaXRoRGlzY291bnRJbmZvKHBrZylcbiAgICAgIH0pKSB8fCBbXVxuXG4gICAgICAvLyBQcmVwYXJlIHByb2R1Y3QgZGF0YVxuICAgICAgY29uc3QgcHJvZHVjdERhdGE6IE9taXQ8UHJvZHVjdFRlbXBsYXRlLCAnaWQnIHwgJ2NyZWF0ZWRBdCcgfCAndXBkYXRlZEF0Jz4gPSB7XG4gICAgICAgIG5hbWU6IHNhbml0aXplVGV4dChmb3JtRGF0YS5uYW1lISksXG4gICAgICAgIGRlc2NyaXB0aW9uOiBzYW5pdGl6ZVRleHQoZm9ybURhdGEuZGVzY3JpcHRpb24gfHwgXCJcIiksXG4gICAgICAgIGNhdGVnb3J5OiBzYW5pdGl6ZVRleHQoZm9ybURhdGEuY2F0ZWdvcnkhKSxcbiAgICAgICAgaW1hZ2U6IGZvcm1EYXRhLmltYWdlLFxuICAgICAgICBkZWxpdmVyeVR5cGU6IGZvcm1EYXRhLmRlbGl2ZXJ5VHlwZSEsXG4gICAgICAgIHByb2R1Y3RUeXBlOiBmb3JtRGF0YS5wcm9kdWN0VHlwZSEsXG4gICAgICAgIHByb2Nlc3NpbmdUeXBlOiBmb3JtRGF0YS5wcm9jZXNzaW5nVHlwZSEsXG4gICAgICAgIGZpZWxkczogZm9ybURhdGEuZmllbGRzPy5tYXAoKGZpZWxkLCBpbmRleCkgPT4gKHtcbiAgICAgICAgICAuLi5maWVsZCxcbiAgICAgICAgICBzb3J0T3JkZXI6IGluZGV4LFxuICAgICAgICAgIGxhYmVsOiBzYW5pdGl6ZVRleHQoZmllbGQubGFiZWwpLFxuICAgICAgICAgIG5hbWU6IGZpZWxkLm5hbWUgfHwgYGZpZWxkXyR7RGF0ZS5ub3coKX1fJHtpbmRleH1gXG4gICAgICAgIH0pKSB8fCBbXSxcbiAgICAgICAgcGFja2FnZXM6IGVuaGFuY2VkUGFja2FnZXMsXG4gICAgICAgIGZlYXR1cmVzOiBmb3JtRGF0YS5mZWF0dXJlcz8ubWFwKHNhbml0aXplVGV4dCkgfHwgW10sXG4gICAgICAgIHRhZ3M6IGZvcm1EYXRhLnRhZ3M/Lm1hcChzYW5pdGl6ZVRleHQpIHx8IFtdLFxuICAgICAgICBpc0FjdGl2ZTogZm9ybURhdGEuaXNBY3RpdmUhLFxuICAgICAgICBpc0ZlYXR1cmVkOiBmb3JtRGF0YS5pc0ZlYXR1cmVkISxcbiAgICAgICAgY3JlYXRlZEJ5OiB1bmRlZmluZWQgLy8gVE9ETzogR2V0IGZyb20gYXV0aFxuICAgICAgfVxuXG4gICAgICBsZXQgc2F2ZWRQcm9kdWN0OiBQcm9kdWN0VGVtcGxhdGVcblxuICAgICAgaWYgKGlzRWRpdGluZyAmJiBwcm9kdWN0KSB7XG4gICAgICAgIHNhdmVkUHJvZHVjdCA9IGF3YWl0IHVwZGF0ZVByb2R1Y3QocHJvZHVjdC5pZCwgcHJvZHVjdERhdGEpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCLYqtmFINin2YTYqtit2K/ZitirINio2YbYrNin2K1cIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCLYqtmFINiq2K3Yr9mK2Ksg2KfZhNmF2YbYqtisINio2YbYrNin2K1cIixcbiAgICAgICAgfSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNhdmVkUHJvZHVjdCA9IGF3YWl0IGNyZWF0ZVByb2R1Y3QocHJvZHVjdERhdGEpXG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogXCLYqtmFINin2YTYpdmG2LTYp9ihINio2YbYrNin2K1cIixcbiAgICAgICAgICBkZXNjcmlwdGlvbjogXCLYqtmFINil2YbYtNin2KEg2KfZhNmF2YbYqtisINio2YbYrNin2K1cIixcbiAgICAgICAgfSlcbiAgICAgIH1cblxuICAgICAgc2V0SGFzVW5zYXZlZENoYW5nZXMoZmFsc2UpXG4gICAgICBvblNhdmUoc2F2ZWRQcm9kdWN0KVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBzYXZpbmcgcHJvZHVjdDpcIiwgZXJyb3IpXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcItiu2LfYoyDZgdmKINin2YTYrdmB2LhcIixcbiAgICAgICAgZGVzY3JpcHRpb246IFwi2K3Yr9irINiu2LfYoyDYo9ir2YbYp9ihINit2YHYuCDYp9mE2YXZhtiq2KwuINmK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJLlwiLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgLy8gUmVzZXQgcGFja2FnZSBmb3JtXG4gIGNvbnN0IHJlc2V0UGFja2FnZUZvcm0gPSAoKSA9PiB7XG4gICAgc2V0UGFja2FnZUZvcm0oe1xuICAgICAgbmFtZTogXCJcIixcbiAgICAgIGFtb3VudDogXCJcIixcbiAgICAgIHByaWNlOiAwLFxuICAgICAgb3JpZ2luYWxQcmljZTogMCxcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlwiLFxuICAgICAgcG9wdWxhcjogZmFsc2UsXG4gICAgICBkaWdpdGFsQ29kZXM6IFwiXCJcbiAgICB9KVxuICB9XG5cbiAgLy8gUmVzZXQgZmllbGQgZm9ybVxuICBjb25zdCByZXNldEZpZWxkRm9ybSA9ICgpID0+IHtcbiAgICBzZXRGaWVsZEZvcm0oe1xuICAgICAgbGFiZWw6IFwiXCIsXG4gICAgICB0eXBlOiBcInVuaXZlcnNhbF9pbnB1dFwiLFxuICAgICAgcGxhY2Vob2xkZXI6IFwiXCIsXG4gICAgICByZXF1aXJlZDogZmFsc2UsXG4gICAgICBvcHRpb25zOiBbXVxuICAgIH0pXG4gICAgc2V0TmV3T3B0aW9uVGV4dChcIlwiKVxuICB9XG5cbiAgLy8gT3BlbiBwYWNrYWdlIGRpYWxvZyBmb3IgY3JlYXRpbmcgbmV3IHBhY2thZ2VcbiAgY29uc3Qgb3BlblBhY2thZ2VEaWFsb2cgPSAoKSA9PiB7XG4gICAgcmVzZXRQYWNrYWdlRm9ybSgpXG4gICAgc2V0RWRpdGluZ1BhY2thZ2VJbmRleChudWxsKVxuICAgIHNldElzUGFja2FnZURpYWxvZ09wZW4odHJ1ZSlcbiAgfVxuXG4gIC8vIE9wZW4gcGFja2FnZSBkaWFsb2cgZm9yIGVkaXRpbmcgZXhpc3RpbmcgcGFja2FnZVxuICBjb25zdCBlZGl0UGFja2FnZSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgcGtnID0gZm9ybURhdGEucGFja2FnZXMhW2luZGV4XVxuICAgIHNldFBhY2thZ2VGb3JtKHtcbiAgICAgIG5hbWU6IHBrZy5uYW1lLFxuICAgICAgYW1vdW50OiBwa2cuYW1vdW50LFxuICAgICAgcHJpY2U6IHBrZy5wcmljZSxcbiAgICAgIG9yaWdpbmFsUHJpY2U6IHBrZy5vcmlnaW5hbFByaWNlIHx8IDAsXG4gICAgICBkZXNjcmlwdGlvbjogcGtnLmRlc2NyaXB0aW9uIHx8IFwiXCIsXG4gICAgICBwb3B1bGFyOiBwa2cucG9wdWxhciB8fCBmYWxzZSxcbiAgICAgIGRpZ2l0YWxDb2RlczogcGtnLmRpZ2l0YWxDb2Rlcz8ubWFwKGNvZGUgPT4gY29kZS5rZXkpLmpvaW4oJ1xcbicpIHx8IFwiXCJcbiAgICB9KVxuICAgIHNldEVkaXRpbmdQYWNrYWdlSW5kZXgoaW5kZXgpXG4gICAgc2V0SXNQYWNrYWdlRGlhbG9nT3Blbih0cnVlKVxuICB9XG5cbiAgLy8gU2F2ZSBwYWNrYWdlIGZyb20gZGlhbG9nXG4gIGNvbnN0IHNhdmVQYWNrYWdlID0gKCkgPT4ge1xuICAgIC8vIFZhbGlkYXRlIHBhY2thZ2UgbmFtZVxuICAgIGNvbnN0IG5hbWVWYWxpZGF0aW9uID0gdmFsaWRhdGVQYWNrYWdlTmFtZShwYWNrYWdlRm9ybS5uYW1lKVxuICAgIGlmICghbmFtZVZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCLYrti32KMg2YHZiiDYp9iz2YUg2KfZhNit2LLZhdipXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBuYW1lVmFsaWRhdGlvbi5lcnJvcixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIHBhY2thZ2UgcHJpY2VcbiAgICBjb25zdCBwcmljZVZhbGlkYXRpb24gPSB2YWxpZGF0ZVBhY2thZ2VQcmljZShwYWNrYWdlRm9ybS5wcmljZSlcbiAgICBpZiAoIXByaWNlVmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcItiu2LfYoyDZgdmKINin2YTYs9i52LFcIixcbiAgICAgICAgZGVzY3JpcHRpb246IHByaWNlVmFsaWRhdGlvbi5lcnJvcixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIFZhbGlkYXRlIGRpc2NvdW50IHByaWNpbmcgaWYgb3JpZ2luYWwgcHJpY2UgaXMgcHJvdmlkZWRcbiAgICBpZiAocGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSA+IDApIHtcbiAgICAgIGNvbnN0IGRpc2NvdW50VmFsaWRhdGlvbiA9IHZhbGlkYXRlRGlzY291bnRQcmljaW5nKHBhY2thZ2VGb3JtLm9yaWdpbmFsUHJpY2UsIHBhY2thZ2VGb3JtLnByaWNlKVxuICAgICAgaWYgKCFkaXNjb3VudFZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IFwi2K7Yt9ijINmB2Yog2KfZhNiu2LXZhVwiLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBkaXNjb3VudFZhbGlkYXRpb24uZXJyb3IsXG4gICAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgICB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBQcm9jZXNzIGFuZCB2YWxpZGF0ZSBkaWdpdGFsIGNvZGVzXG4gICAgY29uc3QgY29kZUxpbmVzID0gcGFja2FnZUZvcm0uZGlnaXRhbENvZGVzXG4gICAgICAuc3BsaXQoJ1xcbicpXG4gICAgICAubWFwKGxpbmUgPT4gbGluZS50cmltKCkpXG4gICAgICAuZmlsdGVyKEJvb2xlYW4pXG5cbiAgICBjb25zdCBjb2Rlc1ZhbGlkYXRpb24gPSB2YWxpZGF0ZURpZ2l0YWxDb2Rlcyhjb2RlTGluZXMpXG4gICAgaWYgKCFjb2Rlc1ZhbGlkYXRpb24uaXNWYWxpZCkge1xuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCLYrti32KMg2YHZiiDYp9mE2KPZg9mI2KfYryDYp9mE2LHZgtmF2YrYqVwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogY29kZXNWYWxpZGF0aW9uLmVycm9yLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgLy8gQ3JlYXRlIGRpZ2l0YWwgY29kZXMgd2l0aCBwcm9wZXIgc3RydWN0dXJlXG4gICAgY29uc3QgZGlnaXRhbENvZGVzOiBEaWdpdGFsQ29kZVtdID0gKGNvZGVzVmFsaWRhdGlvbi5zYW5pdGl6ZWRDb2RlcyB8fCBbXSkubWFwKChrZXksIGkpID0+ICh7XG4gICAgICBpZDogYCR7RGF0ZS5ub3coKX0tJHtpfWAsXG4gICAgICBrZXk6IHNhbml0aXplRGlnaXRhbENvZGUoa2V5KSxcbiAgICAgIHVzZWQ6IGZhbHNlLFxuICAgICAgYXNzaWduZWRUb09yZGVySWQ6IG51bGwsXG4gICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKClcbiAgICB9KSlcblxuICAgIGNvbnN0IG5ld1BhY2thZ2U6IFByb2R1Y3RQYWNrYWdlID0ge1xuICAgICAgaWQ6IGVkaXRpbmdQYWNrYWdlSW5kZXggIT09IG51bGwgPyBmb3JtRGF0YS5wYWNrYWdlcyFbZWRpdGluZ1BhY2thZ2VJbmRleF0uaWQgOiBEYXRlLm5vdygpLnRvU3RyaW5nKCksXG4gICAgICBuYW1lOiBzYW5pdGl6ZVRleHQocGFja2FnZUZvcm0ubmFtZSksXG4gICAgICBhbW91bnQ6IHNhbml0aXplVGV4dChwYWNrYWdlRm9ybS5hbW91bnQpLFxuICAgICAgcHJpY2U6IHNhbml0aXplTnVtYmVyKHBhY2thZ2VGb3JtLnByaWNlKSxcbiAgICAgIG9yaWdpbmFsUHJpY2U6IHBhY2thZ2VGb3JtLm9yaWdpbmFsUHJpY2UgPiAwID8gc2FuaXRpemVOdW1iZXIocGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSkgOiB1bmRlZmluZWQsXG4gICAgICBkZXNjcmlwdGlvbjogcGFja2FnZUZvcm0uZGVzY3JpcHRpb24gPyBzYW5pdGl6ZVRleHQocGFja2FnZUZvcm0uZGVzY3JpcHRpb24pIDogdW5kZWZpbmVkLFxuICAgICAgcG9wdWxhcjogcGFja2FnZUZvcm0ucG9wdWxhcixcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgc29ydE9yZGVyOiBlZGl0aW5nUGFja2FnZUluZGV4ICE9PSBudWxsID8gZWRpdGluZ1BhY2thZ2VJbmRleCA6IChmb3JtRGF0YS5wYWNrYWdlcz8ubGVuZ3RoIHx8IDApLFxuICAgICAgZGlnaXRhbENvZGVzXG4gICAgfVxuXG4gICAgdXBkYXRlRm9ybURhdGEoKHByZXYpID0+IHtcbiAgICAgIGNvbnN0IHBhY2thZ2VzID0gWy4uLihwcmV2LnBhY2thZ2VzIHx8IFtdKV1cbiAgICAgIGlmIChlZGl0aW5nUGFja2FnZUluZGV4ICE9PSBudWxsKSB7XG4gICAgICAgIHBhY2thZ2VzW2VkaXRpbmdQYWNrYWdlSW5kZXhdID0gbmV3UGFja2FnZVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcGFja2FnZXMucHVzaChuZXdQYWNrYWdlKVxuICAgICAgfVxuICAgICAgcmV0dXJuIHsgLi4ucHJldiwgcGFja2FnZXMgfVxuICAgIH0pXG5cbiAgICB0b2FzdCh7XG4gICAgICB0aXRsZTogXCLYqtmFINin2YTYrdmB2LhcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBlZGl0aW5nUGFja2FnZUluZGV4ICE9PSBudWxsID8gXCLYqtmFINiq2K3Yr9mK2Ksg2KfZhNit2LLZhdipINio2YbYrNin2K1cIiA6IFwi2KrZhSDYpdi22KfZgdipINin2YTYrdiy2YXYqSDYqNmG2KzYp9itXCIsXG4gICAgfSlcblxuICAgIHNldElzUGFja2FnZURpYWxvZ09wZW4oZmFsc2UpXG4gICAgcmVzZXRQYWNrYWdlRm9ybSgpXG4gIH1cblxuICAvLyBSZW1vdmUgcGFja2FnZVxuICBjb25zdCByZW1vdmVQYWNrYWdlID0gKGluZGV4OiBudW1iZXIpID0+IHtcbiAgICBjb25zdCBwYWNrYWdlTmFtZSA9IGZvcm1EYXRhLnBhY2thZ2VzPy5baW5kZXhdPy5uYW1lIHx8IGDYp9mE2K3YstmF2KkgJHtpbmRleCArIDF9YFxuXG4gICAgaWYgKGNvbmZpcm0oYNmH2YQg2KPZhtiqINmF2KrYo9mD2K8g2YXZhiDYrdiw2YEgXCIke3BhY2thZ2VOYW1lfVwi2J9cXG5cXG7Ys9mK2KrZhSDYrdiw2YEg2KzZhdmK2Lkg2KfZhNij2YPZiNin2K8g2KfZhNix2YLZhdmK2Kkg2KfZhNmF2LHYqtio2LfYqSDYqNmH2Kcg2KPZiti22KfZiy5gKSkge1xuICAgICAgdXBkYXRlRm9ybURhdGEoKHByZXYpID0+ICh7XG4gICAgICAgIC4uLnByZXYsXG4gICAgICAgIHBhY2thZ2VzOiBwcmV2LnBhY2thZ2VzPy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KSB8fCBbXSxcbiAgICAgIH0pKVxuXG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcItiq2YUg2KfZhNit2LDZgVwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogYNiq2YUg2K3YsNmBIFwiJHtwYWNrYWdlTmFtZX1cIiDYqNmG2KzYp9itYCxcbiAgICAgIH0pXG4gICAgfVxuICB9XG5cbiAgLy8gT3BlbiBmaWVsZCBkaWFsb2cgZm9yIGNyZWF0aW5nIG5ldyBmaWVsZFxuICBjb25zdCBvcGVuRmllbGREaWFsb2cgPSAoKSA9PiB7XG4gICAgcmVzZXRGaWVsZEZvcm0oKVxuICAgIHNldEVkaXRpbmdGaWVsZEluZGV4KG51bGwpXG4gICAgc2V0SXNGaWVsZERpYWxvZ09wZW4odHJ1ZSlcbiAgfVxuXG4gIC8vIE9wZW4gZmllbGQgZGlhbG9nIGZvciBlZGl0aW5nIGV4aXN0aW5nIGZpZWxkXG4gIGNvbnN0IGVkaXRGaWVsZCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgZmllbGQgPSBmb3JtRGF0YS5maWVsZHMhW2luZGV4XVxuICAgIHNldEZpZWxkRm9ybSh7XG4gICAgICBsYWJlbDogZmllbGQubGFiZWwsXG4gICAgICB0eXBlOiBmaWVsZC50eXBlLFxuICAgICAgcGxhY2Vob2xkZXI6IGZpZWxkLnBsYWNlaG9sZGVyIHx8IFwiXCIsXG4gICAgICByZXF1aXJlZDogZmllbGQucmVxdWlyZWQsXG4gICAgICBvcHRpb25zOiBmaWVsZC5vcHRpb25zIHx8IFtdXG4gICAgfSlcbiAgICBzZXRFZGl0aW5nRmllbGRJbmRleChpbmRleClcbiAgICBzZXRJc0ZpZWxkRGlhbG9nT3Blbih0cnVlKVxuICB9XG5cbiAgLy8gRHJvcGRvd24gb3B0aW9uIG1hbmFnZW1lbnQgZnVuY3Rpb25zXG4gIGNvbnN0IGFkZERyb3Bkb3duT3B0aW9uID0gKCkgPT4ge1xuICAgIGNvbnN0IHNhbml0aXplZFRleHQgPSBzYW5pdGl6ZVRleHQobmV3T3B0aW9uVGV4dClcbiAgICBpZiAoIXNhbml0aXplZFRleHQpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwi2K7Yt9ijXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcItmK2LHYrNmJINil2K/Yrtin2YQg2YbYtSDYp9mE2K7Zitin2LFcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBkdXBsaWNhdGVzXG4gICAgY29uc3QgZXhpc3RzID0gZmllbGRGb3JtLm9wdGlvbnMuc29tZShvcHQgPT4gb3B0LmxhYmVsID09PSBzYW5pdGl6ZWRUZXh0KVxuICAgIGlmIChleGlzdHMpIHtcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IFwi2K7Yt9ijXCIsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcItmH2LDYpyDYp9mE2K7Zitin2LEg2YXZiNis2YjYryDYqNin2YTZgdi52YRcIixcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxuICAgICAgfSlcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGNvbnN0IG5ld09wdGlvbjogRHJvcGRvd25PcHRpb24gPSB7XG4gICAgICBpZDogYG9wdGlvbl8ke0RhdGUubm93KCl9YCxcbiAgICAgIHZhbHVlOiBzYW5pdGl6ZWRUZXh0LnRvTG93ZXJDYXNlKCkucmVwbGFjZSgvXFxzKy9nLCAnXycpLFxuICAgICAgbGFiZWw6IHNhbml0aXplZFRleHQsXG4gICAgICBzb3J0T3JkZXI6IGZpZWxkRm9ybS5vcHRpb25zLmxlbmd0aCxcbiAgICAgIGlzQWN0aXZlOiB0cnVlXG4gICAgfVxuXG4gICAgc2V0RmllbGRGb3JtKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBvcHRpb25zOiBbLi4ucHJldi5vcHRpb25zLCBuZXdPcHRpb25dXG4gICAgfSkpXG4gICAgc2V0TmV3T3B0aW9uVGV4dChcIlwiKVxuICB9XG5cbiAgY29uc3QgcmVtb3ZlRHJvcGRvd25PcHRpb24gPSAob3B0aW9uSWQ6IHN0cmluZykgPT4ge1xuICAgIHNldEZpZWxkRm9ybShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgb3B0aW9uczogcHJldi5vcHRpb25zLmZpbHRlcihvcHQgPT4gb3B0LmlkICE9PSBvcHRpb25JZClcbiAgICB9KSlcbiAgfVxuXG4gIGNvbnN0IG1vdmVEcm9wZG93bk9wdGlvbiA9IChvcHRpb25JZDogc3RyaW5nLCBkaXJlY3Rpb246ICd1cCcgfCAnZG93bicpID0+IHtcbiAgICBzZXRGaWVsZEZvcm0ocHJldiA9PiB7XG4gICAgICBjb25zdCBvcHRpb25zID0gWy4uLnByZXYub3B0aW9uc11cbiAgICAgIGNvbnN0IGluZGV4ID0gb3B0aW9ucy5maW5kSW5kZXgob3B0ID0+IG9wdC5pZCA9PT0gb3B0aW9uSWQpXG5cbiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHJldHVybiBwcmV2XG5cbiAgICAgIGNvbnN0IG5ld0luZGV4ID0gZGlyZWN0aW9uID09PSAndXAnID8gaW5kZXggLSAxIDogaW5kZXggKyAxXG4gICAgICBpZiAobmV3SW5kZXggPCAwIHx8IG5ld0luZGV4ID49IG9wdGlvbnMubGVuZ3RoKSByZXR1cm4gcHJldlxuXG4gICAgICAvLyBTd2FwIG9wdGlvbnNcbiAgICAgIFtvcHRpb25zW2luZGV4XSwgb3B0aW9uc1tuZXdJbmRleF1dID0gW29wdGlvbnNbbmV3SW5kZXhdLCBvcHRpb25zW2luZGV4XV1cblxuICAgICAgLy8gVXBkYXRlIHNvcnQgb3JkZXJzXG4gICAgICBvcHRpb25zLmZvckVhY2goKG9wdCwgaSkgPT4ge1xuICAgICAgICBvcHQuc29ydE9yZGVyID0gaVxuICAgICAgfSlcblxuICAgICAgcmV0dXJuIHsgLi4ucHJldiwgb3B0aW9ucyB9XG4gICAgfSlcbiAgfVxuXG4gIC8vIFNhdmUgZmllbGQgZnJvbSBkaWFsb2dcbiAgY29uc3Qgc2F2ZUZpZWxkID0gKCkgPT4ge1xuICAgIC8vIFZhbGlkYXRlIGZpZWxkIGxhYmVsXG4gICAgY29uc3QgbGFiZWxWYWxpZGF0aW9uID0gdmFsaWRhdGVGaWVsZExhYmVsKGZpZWxkRm9ybS5sYWJlbClcbiAgICBpZiAoIWxhYmVsVmFsaWRhdGlvbi5pc1ZhbGlkKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiBcItiu2LfYoyDZgdmKINiq2LPZhdmK2Kkg2KfZhNit2YLZhFwiLFxuICAgICAgICBkZXNjcmlwdGlvbjogbGFiZWxWYWxpZGF0aW9uLmVycm9yLFxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgLy8gVmFsaWRhdGUgZHJvcGRvd24gb3B0aW9ucyBpZiBpdCdzIGEgZHJvcGRvd24gZmllbGRcbiAgICBpZiAoZmllbGRGb3JtLnR5cGUgPT09ICdkcm9wZG93bicpIHtcbiAgICAgIGNvbnN0IG9wdGlvbnNWYWxpZGF0aW9uID0gdmFsaWRhdGVEcm9wZG93bk9wdGlvbnMoZmllbGRGb3JtLm9wdGlvbnMpXG4gICAgICBpZiAoIW9wdGlvbnNWYWxpZGF0aW9uLmlzVmFsaWQpIHtcbiAgICAgICAgdG9hc3Qoe1xuICAgICAgICAgIHRpdGxlOiBcItiu2LfYoyDZgdmKINiu2YrYp9ix2KfYqiDYp9mE2YLYp9im2YXYqVwiLFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBvcHRpb25zVmFsaWRhdGlvbi5lcnJvcixcbiAgICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuICAgIH1cblxuICAgIGNvbnN0IG5ld0ZpZWxkOiBEeW5hbWljRmllbGQgPSB7XG4gICAgICBpZDogZWRpdGluZ0ZpZWxkSW5kZXggIT09IG51bGwgPyBmb3JtRGF0YS5maWVsZHMhW2VkaXRpbmdGaWVsZEluZGV4XS5pZCA6IERhdGUubm93KCkudG9TdHJpbmcoKSxcbiAgICAgIHR5cGU6IGZpZWxkRm9ybS50eXBlLFxuICAgICAgbmFtZTogZWRpdGluZ0ZpZWxkSW5kZXggIT09IG51bGwgPyBmb3JtRGF0YS5maWVsZHMhW2VkaXRpbmdGaWVsZEluZGV4XS5uYW1lIDogYGZpZWxkXyR7RGF0ZS5ub3coKX1gLFxuICAgICAgbGFiZWw6IHNhbml0aXplVGV4dChmaWVsZEZvcm0ubGFiZWwpLFxuICAgICAgcGxhY2Vob2xkZXI6IGZpZWxkRm9ybS5wbGFjZWhvbGRlciA/IHNhbml0aXplVGV4dChmaWVsZEZvcm0ucGxhY2Vob2xkZXIpIDogdW5kZWZpbmVkLFxuICAgICAgcmVxdWlyZWQ6IGZpZWxkRm9ybS5yZXF1aXJlZCxcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgc29ydE9yZGVyOiBlZGl0aW5nRmllbGRJbmRleCAhPT0gbnVsbCA/IGVkaXRpbmdGaWVsZEluZGV4IDogKGZvcm1EYXRhLmZpZWxkcz8ubGVuZ3RoIHx8IDApLFxuICAgICAgdmFsaWRhdGlvbjoge30sXG4gICAgICBvcHRpb25zOiBmaWVsZEZvcm0udHlwZSA9PT0gJ2Ryb3Bkb3duJyA/IGZpZWxkRm9ybS5vcHRpb25zIDogdW5kZWZpbmVkXG4gICAgfVxuXG4gICAgdXBkYXRlRm9ybURhdGEoKHByZXYpID0+IHtcbiAgICAgIGNvbnN0IGZpZWxkcyA9IFsuLi4ocHJldi5maWVsZHMgfHwgW10pXVxuICAgICAgaWYgKGVkaXRpbmdGaWVsZEluZGV4ICE9PSBudWxsKSB7XG4gICAgICAgIGZpZWxkc1tlZGl0aW5nRmllbGRJbmRleF0gPSBuZXdGaWVsZFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZmllbGRzLnB1c2gobmV3RmllbGQpXG4gICAgICB9XG4gICAgICByZXR1cm4geyAuLi5wcmV2LCBmaWVsZHMgfVxuICAgIH0pXG5cbiAgICB0b2FzdCh7XG4gICAgICB0aXRsZTogXCLYqtmFINin2YTYrdmB2LhcIixcbiAgICAgIGRlc2NyaXB0aW9uOiBlZGl0aW5nRmllbGRJbmRleCAhPT0gbnVsbCA/IFwi2KrZhSDYqtit2K/ZitirINin2YTYrdmC2YQg2KjZhtis2KfYrVwiIDogXCLYqtmFINil2LbYp9mB2Kkg2KfZhNit2YLZhCDYqNmG2KzYp9itXCIsXG4gICAgfSlcblxuICAgIHNldElzRmllbGREaWFsb2dPcGVuKGZhbHNlKVxuICAgIHJlc2V0RmllbGRGb3JtKClcbiAgfVxuXG4gIC8vIFJlbW92ZSBmaWVsZFxuICBjb25zdCByZW1vdmVGaWVsZCA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgZmllbGRMYWJlbCA9IGZvcm1EYXRhLmZpZWxkcz8uW2luZGV4XT8ubGFiZWwgfHwgYNin2YTYrdmC2YQgJHtpbmRleCArIDF9YFxuXG4gICAgaWYgKGNvbmZpcm0oYNmH2YQg2KPZhtiqINmF2KrYo9mD2K8g2YXZhiDYrdiw2YEgXCIke2ZpZWxkTGFiZWx9XCLYn2ApKSB7XG4gICAgICB1cGRhdGVGb3JtRGF0YSgocHJldikgPT4gKHtcbiAgICAgICAgLi4ucHJldixcbiAgICAgICAgZmllbGRzOiBwcmV2LmZpZWxkcz8uZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleCkgfHwgW10sXG4gICAgICB9KSlcblxuICAgICAgdG9hc3Qoe1xuICAgICAgICB0aXRsZTogXCLYqtmFINin2YTYrdiw2YFcIixcbiAgICAgICAgZGVzY3JpcHRpb246IGDYqtmFINit2LDZgSBcIiR7ZmllbGRMYWJlbH1cIiDYqNmG2KzYp9itYCxcbiAgICAgIH0pXG4gICAgfVxuICB9XG5cbiAgLy8gSGFuZGxlIGNhbmNlbCB3aXRoIHVuc2F2ZWQgY2hhbmdlcyB3YXJuaW5nXG4gIGNvbnN0IGhhbmRsZUNhbmNlbCA9ICgpID0+IHtcbiAgICBpZiAoaGFzVW5zYXZlZENoYW5nZXMpIHtcbiAgICAgIGlmIChjb25maXJtKFwi2YTYr9mK2YMg2KrYutmK2YrYsdin2Kog2LrZitixINmF2K3ZgdmI2LjYqS4g2YfZhCDYo9mG2Kog2YXYqtij2YPYryDZhdmGINin2YTYpdmE2LrYp9ih2J9cIikpIHtcbiAgICAgICAgb25DYW5jZWwoKVxuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBvbkNhbmNlbCgpXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLWJyIGZyb20tZ3JheS05MDAgdG8tZ3JheS04MDAgYmFja2Ryb3AtYmx1ci1tZCByb3VuZGVkLTJ4bCBtYXgtdy02eGwgdy1mdWxsIG1heC1oLVs5NXZoXSBtZDptYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvIGJvcmRlciBib3JkZXItZ3JheS03MDAvMzAgc2hhZG93LTJ4bFwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgbWQ6cC04IGJvcmRlci1iIGJvcmRlci1ncmF5LTcwMC8zMCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS04MDAvNTAgdG8tZ3JheS03MDAvNTBcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1ibHVlLTYwMCByb3VuZGVkLXhsXCI+XG4gICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNiBoLTYgdGV4dC13aGl0ZVwiIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBtZDp0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgIHtpc0VkaXRpbmcgPyBcItiq2LnYr9mK2YQg2KfZhNmF2YbYqtisXCIgOiBcItil2YbYtNin2KEg2YXZhtiq2Kwg2KzYr9mK2K9cIn1cbiAgICAgICAgICAgICAgPC9oMz5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAge2lzRWRpdGluZyA/IFwi2YLZhSDYqNiq2K3Yr9mK2Ksg2YXYudmE2YjZhdin2Kog2KfZhNmF2YbYqtisXCIgOiBcItij2LbZgSDZhdmG2KrYrCDYrNiv2YrYryDYpdmE2Ykg2KfZhNmF2KrYrNixXCJ9XG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDYW5jZWx9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTQwMCBob3ZlcjpiZy1ncmF5LTcwMCBob3Zlcjp0ZXh0LXdoaXRlXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICA8WCBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBtZDpwLTggc3BhY2UteS04XCI+XG4gICAgICAgIHsvKiBWYWxpZGF0aW9uIEVycm9ycyAqL31cbiAgICAgICAge3ZhbGlkYXRpb25FcnJvcnMubGVuZ3RoID4gMCAmJiAoXG4gICAgICAgICAgPEFsZXJ0IHZhcmlhbnQ9XCJkZXN0cnVjdGl2ZVwiPlxuICAgICAgICAgICAgPEFsZXJ0VHJpYW5nbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICA8QWxlcnRUaXRsZT7Zitix2KzZiSDYqti12K3ZititINin2YTYo9iu2LfYp9ihINin2YTYqtin2YTZitipOjwvQWxlcnRUaXRsZT5cbiAgICAgICAgICAgIDxBbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBtdC0yXCI+XG4gICAgICAgICAgICAgICAge3ZhbGlkYXRpb25FcnJvcnMubWFwKChlcnJvciwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgIDxsaSBrZXk9e2luZGV4fT57ZXJyb3J9PC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBWYWxpZGF0aW9uIFdhcm5pbmdzICovfVxuICAgICAgICB7dmFsaWRhdGlvbldhcm5pbmdzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgIDxBbGVydD5cbiAgICAgICAgICAgIDxBbGVydENpcmNsZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIDxBbGVydFRpdGxlPtiq2K3YsNmK2LHYp9iqOjwvQWxlcnRUaXRsZT5cbiAgICAgICAgICAgIDxBbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIHNwYWNlLXktMSBtdC0yXCI+XG4gICAgICAgICAgICAgICAge3ZhbGlkYXRpb25XYXJuaW5ncy5tYXAoKHdhcm5pbmcsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICA8bGkga2V5PXtpbmRleH0+e3dhcm5pbmd9PC9saT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBVbnNhdmVkIENoYW5nZXMgSW5kaWNhdG9yICovfVxuICAgICAgICB7aGFzVW5zYXZlZENoYW5nZXMgJiYgKFxuICAgICAgICAgIDxBbGVydD5cbiAgICAgICAgICAgIDxTYXZlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgPEFsZXJ0VGl0bGU+2KrYutmK2YrYsdin2Kog2LrZitixINmF2K3ZgdmI2LjYqTwvQWxlcnRUaXRsZT5cbiAgICAgICAgICAgIDxBbGVydERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICDZhNiv2YrZgyDYqti62YrZitix2KfYqiDYutmK2LEg2YXYrdmB2YjYuNipLiDYqtij2YPYryDZhdmGINit2YHYuCDYudmF2YTZgyDZgtio2YQg2KfZhNmF2LrYp9iv2LHYqS5cbiAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBCYXNpYyBJbmZvICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmF5LTgwMC80MCB0by1ncmF5LTcwMC80MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQtMnhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzIwXCI+XG4gICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTYgZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy01IGgtNSB0ZXh0LXdoaXRlXCIgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAg2KfZhNmF2LnZhNmI2YXYp9iqINin2YTYo9iz2KfYs9mK2KlcbiAgICAgICAgICA8L2g0PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTMgdGV4dC13aGl0ZVwiPtin2LPZhSDYp9mE2YXZhtiq2KwgKjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEubmFtZSB8fCBcIlwifVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgbmFtZTogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwLzUwIGJhY2tkcm9wLWJsdXItc20gYm9yZGVyIGJvcmRlci1ncmF5LTYwMC81MCByb3VuZGVkLXhsIHB4LTQgcHktMyBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDAgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctcHVycGxlLTUwMC8yMCB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgdGV4dC13aGl0ZSBwbGFjZWhvbGRlci1ncmF5LTQwMFwiXG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINin2LPZhSDYp9mE2YXZhtiq2KxcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gbWItMyB0ZXh0LXdoaXRlXCI+2KfZhNmB2KbYqSAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jYXRlZ29yeSB8fCBcIlwifVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcnk6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItZ3JheS02MDAvNTAgcm91bmRlZC14bCBweC00IHB5LTMgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1wdXJwbGUtNTAwIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLXB1cnBsZS01MDAvMjAgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIHRleHQtd2hpdGUgcGxhY2Vob2xkZXItZ3JheS00MDBcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLZhdir2YQ6IE1PQkEsIFJQRywg2KjYp9iq2YQg2LHZiNmK2KfZhFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gbWItMyB0ZXh0LXdoaXRlXCI+2KfZhNmI2LXZgTwvbGFiZWw+XG4gICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbiB8fCBcIlwifVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoKHByZXYpID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzUwIHJvdW5kZWQteGwgcHgtNCBweS0zIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItcHVycGxlLTUwMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwLzIwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwIHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmI2LXZgSDYp9mE2YXZhtiq2KxcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTMgdGV4dC13aGl0ZVwiPtin2YTYudmE2KfZhdin2Ko8L2xhYmVsPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwidGV4dFwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRhZ3M/LmpvaW4oXCIsIFwiKSB8fCBcIlwifVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICAgIHVwZGF0ZUZvcm1EYXRhKChwcmV2KSA9PiAoe1xuICAgICAgICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICAgICAgICB0YWdzOiBlLnRhcmdldC52YWx1ZVxuICAgICAgICAgICAgICAgICAgICAgIC5zcGxpdChcIixcIilcbiAgICAgICAgICAgICAgICAgICAgICAubWFwKCh0YWcpID0+IHRhZy50cmltKCkpXG4gICAgICAgICAgICAgICAgICAgICAgLmZpbHRlcihCb29sZWFuKSxcbiAgICAgICAgICAgICAgICAgIH0pKVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzUwIHJvdW5kZWQteGwgcHgtNCBweS0zIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItcHVycGxlLTUwMCBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1wdXJwbGUtNTAwLzIwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LXdoaXRlIHBsYWNlaG9sZGVyLWdyYXktNDAwXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIti02KfYpti5LCDZhdmF2YrYsiwg2KzYr9mK2K8gKNmF2YHYtdmI2YTYqSDYqNmB2KfYtdmE2KkpXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0zIHRleHQtd2hpdGVcIj7YtdmI2LHYqSDYp9mE2LrZhNin2YE8L2xhYmVsPlxuXG4gICAgICAgICAgICAgIDxJbWFnZVVwbG9hZGVyXG4gICAgICAgICAgICAgICAgY3VycmVudEltYWdlPXtmb3JtRGF0YS5pbWFnZSB8fCBcIlwifVxuICAgICAgICAgICAgICAgIG9uSW1hZ2VDaGFuZ2VkPXsodXJsKSA9PiB1cGRhdGVGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIGltYWdlOiB1cmwgfSkpfVxuICAgICAgICAgICAgICAgIGxhYmVsPVwi2LXZiNix2Kkg2KfZhNmF2YbYqtisXCJcbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlclRleHQ9XCLYo9iv2K7ZhCDYsdin2KjYtyDYtdmI2LHYqSDYp9mE2YXZhtiq2Kwg2KPZiCDZgtmFINio2LHZgdi5INi12YjYsdipXCJcbiAgICAgICAgICAgICAgICBhc3BlY3RSYXRpbz17MX1cbiAgICAgICAgICAgICAgICBtYXhGaWxlU2l6ZT17MTB9XG4gICAgICAgICAgICAgICAgc2hvd1VybElucHV0PXt0cnVlfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInNwYWNlLXktM1wiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFN0YXR1cyBUb2dnbGVzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBnYXAtNiBwdC00IGJvcmRlci10IGJvcmRlci1ncmF5LTYwMC8zMFwiPlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTMgdGV4dC13aGl0ZSBjdXJzb3ItcG9pbnRlclwiPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImNoZWNrYm94XCJcbiAgICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2Zvcm1EYXRhLmlzRmVhdHVyZWQgfHwgZmFsc2V9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZvcm1EYXRhKChwcmV2KSA9PiAoeyAuLi5wcmV2LCBpc0ZlYXR1cmVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgcm91bmRlZCBib3JkZXItZ3JheS02MDAgYmctZ3JheS03MDAgdGV4dC1wdXJwbGUtNjAwIGZvY3VzOnJpbmctcHVycGxlLTUwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj7ZhdmG2KrYrCDZhdmF2YrYsjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zIHRleHQtd2hpdGUgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtmb3JtRGF0YS5pc0FjdGl2ZSA/PyB0cnVlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGb3JtRGF0YSgocHJldikgPT4gKHsgLi4ucHJldiwgaXNBY3RpdmU6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCByb3VuZGVkIGJvcmRlci1ncmF5LTYwMCBiZy1ncmF5LTcwMCB0ZXh0LXB1cnBsZS02MDAgZm9jdXM6cmluZy1wdXJwbGUtNTAwXCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPtmF2YbYqtisINmG2LTYtzwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUGFja2FnZXMgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtcHVycGxlLTQwMFwiIC8+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZVwiPtin2YTYrdiy2YU8L2g0PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj4oe2Zvcm1EYXRhLnBhY2thZ2VzPy5sZW5ndGggfHwgMH0pPC9zcGFuPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5QYWNrYWdlRGlhbG9nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tcHVycGxlLTYwMCB0by1wdXJwbGUtNzAwIGhvdmVyOmZyb20tcHVycGxlLTcwMCBob3Zlcjp0by1wdXJwbGUtODAwIHRleHQtd2hpdGUgc2hhZG93LWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAg2KXYttin2YHYqSDYrdiy2YXYqVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Zm9ybURhdGEucGFja2FnZXMgJiYgZm9ybURhdGEucGFja2FnZXMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7Zm9ybURhdGEucGFja2FnZXMubWFwKChwa2csIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtwa2cuaWR9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS04MDAvNTAgdG8tZ3JheS03MDAvNTAgYmFja2Ryb3AtYmx1ci1zbSByb3VuZGVkLXhsIHAtNiBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzMwIGhvdmVyOmJvcmRlci1wdXJwbGUtNTAwLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlbiBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGg1IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LXdoaXRlIG1iLTFcIj57cGtnLm5hbWV9PC9oNT5cbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXB1cnBsZS0zMDAgZm9udC1ib2xkIHRleHQteGxcIj4ke3BrZy5wcmljZX08L3A+XG4gICAgICAgICAgICAgICAgICAgICAge3BrZy5hbW91bnQgJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCB0ZXh0LXNtXCI+e3BrZy5hbW91bnR9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAge3BrZy5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIHRleHQtc20gbXQtMlwiPntwa2cuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3BrZy5wb3B1bGFyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXllbGxvdy01MDAvMjAgdGV4dC15ZWxsb3ctMzAwIHB4LTIgcHktMSByb3VuZGVkLWZ1bGwgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICDYtNin2KbYuVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGVkaXRQYWNrYWdlKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJ3LTMgaC0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAg2KrYudiv2YrZhFxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVQYWNrYWdlKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1yZWQtNjAwIHRleHQtcmVkLTQwMCBob3ZlcjpiZy1yZWQtNjAwLzEwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHJhc2gyIGNsYXNzTmFtZT1cInctMyBoLTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7cGtnLmRpZ2l0YWxDb2RlcyAmJiBwa2cuZGlnaXRhbENvZGVzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIG10LTMgcC0zIGJnLWJsdWUtNTAwLzEwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yMFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxLZXkgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNDAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtMzAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwa2cuZGlnaXRhbENvZGVzLmxlbmd0aH0g2YPZiNivINix2YLZhdmKINmF2KrYp9itXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgcHktMTIgYmctZ3JheS04MDAvMzAgcm91bmRlZC14bCBib3JkZXItMiBib3JkZXItZGFzaGVkIGJvcmRlci1ncmF5LTYwMFwiPlxuICAgICAgICAgICAgICA8UGFja2FnZSBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTUwMCBteC1hdXRvIG1iLTRcIiAvPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwIG1iLTRcIj7ZhNmFINmK2KrZhSDYpdi22KfZgdipINij2Yog2K3YstmFINio2LnYrzwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5QYWNrYWdlRGlhbG9nfVxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItcHVycGxlLTYwMCB0ZXh0LXB1cnBsZS00MDAgaG92ZXI6YmctcHVycGxlLTYwMC8xMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgINil2LbYp9mB2Kkg2KPZiNmEINit2LLZhdipXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEN1c3RvbSBGaWVsZHMgKi99XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICA8VHlwZSBjbGFzc05hbWU9XCJ3LTYgaC02IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtd2hpdGVcIj7Yp9mE2K3ZgtmI2YQg2KfZhNmF2K7Ytdi12Kk8L2g0PlxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj4oe2Zvcm1EYXRhLmZpZWxkcz8ubGVuZ3RoIHx8IDB9KTwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtvcGVuRmllbGREaWFsb2d9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLWJsdWUtODAwIHRleHQtd2hpdGUgc2hhZG93LWxnXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAg2KXYttin2YHYqSDYrdmC2YRcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAge2Zvcm1EYXRhLmZpZWxkcyAmJiBmb3JtRGF0YS5maWVsZHMubGVuZ3RoID4gMCA/IChcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7Zm9ybURhdGEuZmllbGRzLm1hcCgoZmllbGQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtmaWVsZC5pZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ncmF5LTgwMC81MCB0by1ncmF5LTcwMC81MCBiYWNrZHJvcC1ibHVyLXNtIHJvdW5kZWQteGwgcC02IGJvcmRlciBib3JkZXItZ3JheS02MDAvMzAgaG92ZXI6Ym9yZGVyLWJsdWUtNTAwLzMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxoNSBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC13aGl0ZSBtYi0xXCI+e2ZpZWxkLmxhYmVsfTwvaDU+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtNCB0ZXh0LXNtIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLWJsdWUtNTAwLzIwIHRleHQtYmx1ZS0zMDAgcHgtMiBweS0xIHJvdW5kZWQtZnVsbFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmllbGQudHlwZSA9PT0gXCJ0ZXh0XCIgPyBcItmG2LVcIiA6IGZpZWxkLnR5cGUgPT09IFwiZW1haWxcIiA/IFwi2KjYsdmK2K8g2KXZhNmD2KrYsdmI2YbZilwiIDogXCLYsdmC2YVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtmaWVsZC5yZXF1aXJlZCAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXJlZC01MDAvMjAgdGV4dC1yZWQtMzAwIHB4LTIgcHktMSByb3VuZGVkLWZ1bGxcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICDZhdi32YTZiNioXG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAge2ZpZWxkLnBsYWNlaG9sZGVyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgdGV4dC1zbSBtdC0yXCI+XCJ7ZmllbGQucGxhY2Vob2xkZXJ9XCI8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZWRpdEZpZWxkKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNzAwXCJcbiAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICA8RWRpdCBjbGFzc05hbWU9XCJ3LTMgaC0zIG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAg2KrYudiv2YrZhFxuICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVGaWVsZChpbmRleCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItcmVkLTYwMCB0ZXh0LXJlZC00MDAgaG92ZXI6YmctcmVkLTYwMC8xMFwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJ3LTMgaC0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApIDogKFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMiBiZy1ncmF5LTgwMC8zMCByb3VuZGVkLXhsIGJvcmRlci0yIGJvcmRlci1kYXNoZWQgYm9yZGVyLWdyYXktNjAwXCI+XG4gICAgICAgICAgICAgIDxUeXBlIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktNTAwIG14LWF1dG8gbWItNFwiIC8+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS00MDAgbWItNFwiPtmE2YUg2YrYqtmFINil2LbYp9mB2Kkg2KPZiiDYrdmC2YjZhCDZhdiu2LXYtdipINio2LnYrzwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e29wZW5GaWVsZERpYWxvZ31cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWJsdWUtNjAwIHRleHQtYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS02MDAvMTBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFBsdXMgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICDYpdi22KfZgdipINij2YjZhCDYrdmC2YRcbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBwdC04IGJvcmRlci10IGJvcmRlci1ncmF5LTYwMC8zMFwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1wdXJwbGUtNjAwIHRvLWJsdWUtNjAwIGhvdmVyOmZyb20tcHVycGxlLTcwMCBob3Zlcjp0by1ibHVlLTcwMCBkaXNhYmxlZDpmcm9tLWdyYXktNjAwIGRpc2FibGVkOnRvLWdyYXktNjAwIHRleHQtd2hpdGUgcHktNCB0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgc2hhZG93LWxnXCJcbiAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzTG9hZGluZyA/IChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtM1wiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4+2KzYp9ix2Yog2KfZhNit2YHYuC4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0zXCI+XG4gICAgICAgICAgICAgICAgPFBhY2thZ2UgY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+e2lzRWRpdGluZyA/IFwi2KrYrdiv2YrYqyDYp9mE2YXZhtiq2KxcIiA6IFwi2KXZhti02KfYoSDYp9mE2YXZhtiq2KxcIn08L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDYW5jZWx9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleC0xIHNtOmZsZXgtbm9uZSBib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMCBob3Zlcjp0ZXh0LXdoaXRlIHB5LTQgcHgtOCB0ZXh0LWxnXCJcbiAgICAgICAgICAgIHNpemU9XCJsZ1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAg2KXZhNi62KfYoVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUGFja2FnZSBEaWFsb2cgKi99XG4gICAgICA8RGlhbG9nIG9wZW49e2lzUGFja2FnZURpYWxvZ09wZW59IG9uT3BlbkNoYW5nZT17c2V0SXNQYWNrYWdlRGlhbG9nT3Blbn0+XG4gICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlci1ncmF5LTcwMCB0ZXh0LXdoaXRlIG1heC13LTJ4bCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxQYWNrYWdlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1wdXJwbGUtNDAwXCIgLz5cbiAgICAgICAgICAgICAge2VkaXRpbmdQYWNrYWdlSW5kZXggIT09IG51bGwgPyBcItiq2LnYr9mK2YQg2KfZhNit2LLZhdipXCIgOiBcItil2LbYp9mB2Kkg2K3YstmF2Kkg2KzYr9mK2K/YqVwifVxuICAgICAgICAgICAgPC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICA8L0RpYWxvZ0hlYWRlcj5cblxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02IHB5LTRcIj5cbiAgICAgICAgICAgIHsvKiBCYXNpYyBQYWNrYWdlIEluZm8gKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTRcIj5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+2KfYs9mFINin2YTYrdiy2YXYqSAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYWNrYWdlRm9ybS5uYW1lfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQYWNrYWdlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2YXYq9mEOiA2MCDZitmI2LPZilwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItcHVycGxlLTUwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+2KfZhNmD2YXZitipPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYWNrYWdlRm9ybS5hbW91bnR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBhY2thZ2VGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgYW1vdW50OiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmF2KvZhDogNjAg2YrZiNiz2YpcIlxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBFbmhhbmNlZCBQcmljaW5nIFN5c3RlbSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+2KfZhNiz2LnYsSDYp9mE2K3Yp9mE2YogKjwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgICAgICAgbWluPVwiMC4wMVwiXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwYWNrYWdlRm9ybS5wcmljZX1cbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQYWNrYWdlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIHByaWNlOiBOdW1iZXIoZS50YXJnZXQudmFsdWUpIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwLjAwXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+2KfZhNiz2LnYsSDYp9mE2LDZiiDYs9mK2K/Zgdi52Ycg2KfZhNi52YXZitmEPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj7Yp9mE2LPYudixINin2YTYo9i12YTZiiAo2KfYrtiq2YrYp9ix2YopPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgICAgc3RlcD1cIjAuMDFcIlxuICAgICAgICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgICAgICAgdmFsdWU9e3BhY2thZ2VGb3JtLm9yaWdpbmFsUHJpY2V9XG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UGFja2FnZUZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBvcmlnaW5hbFByaWNlOiBOdW1iZXIoZS50YXJnZXQudmFsdWUpIH0pKX1cbiAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwLjAwXCJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMCBtdC0xXCI+2YTZhNi52LHYtiDZg9iu2LXZhSAo2KfYqtix2YPZhyDZgdin2LHYutin2Ysg2KXYsNinINmE2YUg2YrZg9mGINmH2YbYp9mDINiu2LXZhSk8L3A+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBEaXNjb3VudCBDYWxjdWxhdGlvbiBEaXNwbGF5ICovfVxuICAgICAgICAgICAgICB7cGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSA+IDAgJiYgcGFja2FnZUZvcm0ucHJpY2UgPiAwICYmIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMC8xMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8yMCByb3VuZGVkLWxnIHAtM1wiPlxuICAgICAgICAgICAgICAgICAge3BhY2thZ2VGb3JtLm9yaWdpbmFsUHJpY2UgPiBwYWNrYWdlRm9ybS5wcmljZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmVlbi00MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg2K7YtdmFIHtjYWxjdWxhdGVEaXNjb3VudFBlcmNlbnRhZ2UocGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSwgcGFja2FnZUZvcm0ucHJpY2UpfSVcbiAgICAgICAgICAgICAgICAgICAgICAgICjYqtmI2YHZitixIHsocGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSAtIHBhY2thZ2VGb3JtLnByaWNlKS50b0ZpeGVkKDIpfSDYr9mI2YTYp9ixKVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICApIDogcGFja2FnZUZvcm0ub3JpZ2luYWxQcmljZSA9PT0gcGFja2FnZUZvcm0ucHJpY2UgPyAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmcteWVsbG93LTQwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXllbGxvdy0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg2YTYpyDZitmI2KzYryDYrti12YUgKNin2YTYs9i52LHYp9mGINmF2KrYs9in2YjZitin2YYpXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctcmVkLTQwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXJlZC0zMDAgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAg2K7Yt9ijOiDYp9mE2LPYudixINin2YTYo9i12YTZiiDZitis2Kgg2KPZhiDZitmD2YjZhiDYo9mD2KjYsSDZhdmGINin2YTYs9i52LEg2KfZhNit2KfZhNmKXG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIERlc2NyaXB0aW9uICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPtin2YTZiNi12YE8L2xhYmVsPlxuICAgICAgICAgICAgICA8dGV4dGFyZWFcbiAgICAgICAgICAgICAgICB2YWx1ZT17cGFja2FnZUZvcm0uZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRQYWNrYWdlRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIGRlc2NyaXB0aW9uOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLZiNi12YEg2KfZhNit2LLZhdipICjYp9iu2KrZitin2LHZiilcIlxuICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLXB1cnBsZS01MDAgcmVzaXplLW5vbmVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBEaWdpdGFsIENvZGVzICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPEtleSBjbGFzc05hbWU9XCJ3LTUgaC01IHRleHQtYmx1ZS00MDBcIiAvPlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KfZhNij2YPZiNin2K8g2KfZhNix2YLZhdmK2Kk8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPijYp9iu2KrZitin2LHZiik8L3NwYW4+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctYmx1ZS01MDAvMTAgYm9yZGVyIGJvcmRlci1ibHVlLTUwMC8yMCByb3VuZGVkLWxnIHAtMyBtYi0zXCI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtMzAwIG1iLTJcIj7wn5KhINil2LHYtNin2K/Yp9iqOjwvcD5cbiAgICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWJsdWUtMjAwIHNwYWNlLXktMVwiPlxuICAgICAgICAgICAgICAgICAgPGxpPuKAoiDYo9iv2K7ZhCDZg9mI2K8g2YjYp9it2K8g2YHZiiDZg9mEINiz2LfYsTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiINiz2YrYqtmFINiq2K7YtdmK2LUg2YPZiNivINmI2KfYrdivINmB2YLYtyDZhNmD2YQg2LfZhNioPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT7igKIg2KfZhNij2YPZiNin2K8g2KfZhNmF2LPYqtiu2K/ZhdipINmE2YYg2KrYuNmH2LEg2YTZhNmF2LTYqtix2YrZhiDYp9mE2KLYrtix2YrZhjwvbGk+XG4gICAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgdmFsdWU9e3BhY2thZ2VGb3JtLmRpZ2l0YWxDb2Rlc31cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFBhY2thZ2VGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgZGlnaXRhbENvZGVzOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYo9iv2K7ZhCDYp9mE2KPZg9mI2KfYryDYp9mE2LHZgtmF2YrYqSAo2YPZiNivINmI2KfYrdivINmB2Yog2YPZhCDYs9i32LEpJiMxMDvZhdir2KfZhDomIzEwO0FCMTItWFkzNC1aWjc4JiMxMDtDRDU2LVBMOTAtUVcxMlwiXG4gICAgICAgICAgICAgICAgcm93cz17Nn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctZ3JheS03MDAgYm9yZGVyIGJvcmRlci1ncmF5LTYwMCByb3VuZGVkLWxnIHB4LTMgcHktMiB0ZXh0LXdoaXRlIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpib3JkZXItcHVycGxlLTUwMCBmb250LW1vbm8gdGV4dC1zbSByZXNpemUtbm9uZVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIE9wdGlvbnMgKi99XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC00XCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LXdoaXRlXCI+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgICAgY2hlY2tlZD17cGFja2FnZUZvcm0ucG9wdWxhcn1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UGFja2FnZUZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBwb3B1bGFyOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWRcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+2K3YstmF2Kkg2LTYp9im2LnYqTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIERpYWxvZyBBY3Rpb25zICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBnYXAtMyBwdC00IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzUGFja2FnZURpYWxvZ09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgINil2YTYutin2KFcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtzYXZlUGFja2FnZX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLXB1cnBsZS02MDAgdG8tcHVycGxlLTcwMCBob3Zlcjpmcm9tLXB1cnBsZS03MDAgaG92ZXI6dG8tcHVycGxlLTgwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtlZGl0aW5nUGFja2FnZUluZGV4ICE9PSBudWxsID8gXCLYqtit2K/ZitirINin2YTYrdiy2YXYqVwiIDogXCLYpdi22KfZgdipINin2YTYrdiy2YXYqVwifVxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgIDwvRGlhbG9nPlxuXG4gICAgICB7LyogRmllbGQgRGlhbG9nICovfVxuICAgICAgPERpYWxvZyBvcGVuPXtpc0ZpZWxkRGlhbG9nT3Blbn0gb25PcGVuQ2hhbmdlPXtzZXRJc0ZpZWxkRGlhbG9nT3Blbn0+XG4gICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlci1ncmF5LTcwMCB0ZXh0LXdoaXRlIG1heC13LWxnXCI+XG4gICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxUeXBlIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ibHVlLTQwMFwiIC8+XG4gICAgICAgICAgICAgIHtlZGl0aW5nRmllbGRJbmRleCAhPT0gbnVsbCA/IFwi2KrYudiv2YrZhCDYp9mE2K3ZgtmEXCIgOiBcItil2LbYp9mB2Kkg2K3ZgtmEINis2K/ZitivXCJ9XG4gICAgICAgICAgICA8L0RpYWxvZ1RpdGxlPlxuICAgICAgICAgIDwvRGlhbG9nSGVhZGVyPlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTYgcHktNFwiPlxuICAgICAgICAgICAgey8qIEZpZWxkIExhYmVsICovfVxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gbWItMlwiPtiq2LPZhdmK2Kkg2KfZhNit2YLZhCAqPC9sYWJlbD5cbiAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmaWVsZEZvcm0ubGFiZWx9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGaWVsZEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCBsYWJlbDogZS50YXJnZXQudmFsdWUgfSkpfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi2YXYq9mEOiDYp9iz2YUg2KfZhNmF2LPYqtiu2K/ZhVwiXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdyYXktNzAwIGJvcmRlciBib3JkZXItZ3JheS02MDAgcm91bmRlZC1sZyBweC0zIHB5LTIgdGV4dC13aGl0ZSBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6Ym9yZGVyLWJsdWUtNTAwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogRmllbGQgVHlwZSAqL31cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIG1iLTJcIj7ZhtmI2Lkg2KfZhNit2YLZhDwvbGFiZWw+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZmllbGRGb3JtLnR5cGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGaWVsZEZvcm0ocHJldiA9PiAoeyAuLi5wcmV2LCB0eXBlOiBlLnRhcmdldC52YWx1ZSBhcyBcInRleHRcIiB8IFwiZW1haWxcIiB8IFwibnVtYmVyXCIgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgcHgtMyBweS0yIHRleHQtd2hpdGUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwidGV4dFwiPtmG2LU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiZW1haWxcIj7YqNix2YrYryDYpdmE2YPYqtix2YjZhtmKPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm51bWJlclwiPtix2YLZhTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogUGxhY2Vob2xkZXIgKi99XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSBtYi0yXCI+2KfZhNmG2LUg2KfZhNiq2YjYttmK2K3ZijwvbGFiZWw+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZmllbGRGb3JtLnBsYWNlaG9sZGVyfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0RmllbGRGb3JtKHByZXYgPT4gKHsgLi4ucHJldiwgcGxhY2Vob2xkZXI6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItmF2KvZhDog2KPYr9iu2YQg2KfYs9mFINin2YTZhdiz2KrYrtiv2YVcIlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbGcgcHgtMyBweS0yIHRleHQtd2hpdGUgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOmJvcmRlci1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIFJlcXVpcmVkICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICAgIGlkPVwiZmllbGQtcmVxdWlyZWRcIlxuICAgICAgICAgICAgICAgIGNoZWNrZWQ9e2ZpZWxkRm9ybS5yZXF1aXJlZH1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZpZWxkRm9ybShwcmV2ID0+ICh7IC4uLnByZXYsIHJlcXVpcmVkOiBlLnRhcmdldC5jaGVja2VkIH0pKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJmaWVsZC1yZXF1aXJlZFwiIGNsYXNzTmFtZT1cInRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICDYrdmC2YQg2YXYt9mE2YjYqFxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogRGlhbG9nIEFjdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktZW5kIGdhcC0zIHB0LTQgYm9yZGVyLXQgYm9yZGVyLWdyYXktNzAwXCI+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNGaWVsZERpYWxvZ09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJib3JkZXItZ3JheS02MDAgdGV4dC1ncmF5LTMwMCBob3ZlcjpiZy1ncmF5LTcwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgINil2YTYutin2KFcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtzYXZlRmllbGR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB0by1ibHVlLTcwMCBob3Zlcjpmcm9tLWJsdWUtNzAwIGhvdmVyOnRvLWJsdWUtODAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2VkaXRpbmdGaWVsZEluZGV4ICE9PSBudWxsID8gXCLYqtit2K/ZitirINin2YTYrdmC2YRcIiA6IFwi2KXYttin2YHYqSDYp9mE2K3ZgtmEXCJ9XG4gICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9EaWFsb2dDb250ZW50PlxuICAgICAgPC9EaWFsb2c+XG5cbiAgICAgIHsvKiBJbWFnZSBDcm9wIERpYWxvZyAqL31cbiAgICAgIDxEaWFsb2cgb3Blbj17aXNJbWFnZUNyb3BEaWFsb2dPcGVufSBvbk9wZW5DaGFuZ2U9e3NldElzSW1hZ2VDcm9wRGlhbG9nT3Blbn0+XG4gICAgICAgIDxEaWFsb2dDb250ZW50IGNsYXNzTmFtZT1cImJnLWdyYXktODAwIGJvcmRlci1ncmF5LTcwMCB0ZXh0LXdoaXRlIG1heC13LTR4bCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgPERpYWxvZ0hlYWRlcj5cbiAgICAgICAgICAgIDxEaWFsb2dUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxDcm9wIGNsYXNzTmFtZT1cInctNSBoLTUgdGV4dC1ncmVlbi00MDBcIiAvPlxuICAgICAgICAgICAgICDZgti1INmI2KrYudiv2YrZhCDYp9mE2LXZiNix2KlcbiAgICAgICAgICAgIDwvRGlhbG9nVGl0bGU+XG4gICAgICAgICAgPC9EaWFsb2dIZWFkZXI+XG5cbiAgICAgICAgICB7aW1hZ2VQcmV2aWV3ICYmIChcbiAgICAgICAgICAgIDxJbWFnZUNyb3BwZXJcbiAgICAgICAgICAgICAgaW1hZ2VTcmM9e2ltYWdlUHJldmlld31cbiAgICAgICAgICAgICAgb25Dcm9wPXtoYW5kbGVJbWFnZUNyb3B9XG4gICAgICAgICAgICAgIG9uQ2FuY2VsPXsoKSA9PiBzZXRJc0ltYWdlQ3JvcERpYWxvZ09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICApfVxuICAgICAgICA8L0RpYWxvZ0NvbnRlbnQ+XG4gICAgICA8L0RpYWxvZz5cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG4vLyBTaW1wbGUgSW1hZ2UgQ3JvcHBlciBDb21wb25lbnRcbmludGVyZmFjZSBJbWFnZUNyb3BwZXJQcm9wcyB7XG4gIGltYWdlU3JjOiBzdHJpbmdcbiAgb25Dcm9wOiAoY3JvcHBlZEltYWdlOiBzdHJpbmcpID0+IHZvaWRcbiAgb25DYW5jZWw6ICgpID0+IHZvaWRcbn1cblxuZnVuY3Rpb24gSW1hZ2VDcm9wcGVyKHsgaW1hZ2VTcmMsIG9uQ3JvcCwgb25DYW5jZWwgfTogSW1hZ2VDcm9wcGVyUHJvcHMpIHtcbiAgY29uc3QgY2FudmFzUmVmID0gdXNlUmVmPEhUTUxDYW52YXNFbGVtZW50PihudWxsKVxuICBjb25zdCBpbWFnZVJlZiA9IHVzZVJlZjxIVE1MSW1hZ2VFbGVtZW50PihudWxsKVxuICBjb25zdCBbY3JvcEFyZWEsIHNldENyb3BBcmVhXSA9IHVzZVN0YXRlKHsgeDogNTAsIHk6IDUwLCB3aWR0aDogMjAwLCBoZWlnaHQ6IDIwMCB9KVxuICBjb25zdCBbaXNEcmFnZ2luZywgc2V0SXNEcmFnZ2luZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2ltYWdlTG9hZGVkLCBzZXRJbWFnZUxvYWRlZF0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2ltYWdlU2l6ZSwgc2V0SW1hZ2VTaXplXSA9IHVzZVN0YXRlKHsgd2lkdGg6IDAsIGhlaWdodDogMCB9KVxuXG4gIC8vIEhhbmRsZSBib3RoIG1vdXNlIGFuZCB0b3VjaCBldmVudHNcbiAgY29uc3QgZ2V0RXZlbnRQb3NpdGlvbiA9IChlOiBSZWFjdC5Nb3VzZUV2ZW50IHwgUmVhY3QuVG91Y2hFdmVudCkgPT4ge1xuICAgIGlmICgndG91Y2hlcycgaW4gZSkge1xuICAgICAgcmV0dXJuIHsgeDogZS50b3VjaGVzWzBdLmNsaWVudFgsIHk6IGUudG91Y2hlc1swXS5jbGllbnRZIH1cbiAgICB9XG4gICAgcmV0dXJuIHsgeDogZS5jbGllbnRYLCB5OiBlLmNsaWVudFkgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU3RhcnQgPSAoZTogUmVhY3QuTW91c2VFdmVudCB8IFJlYWN0LlRvdWNoRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBzZXRJc0RyYWdnaW5nKHRydWUpXG4gIH1cblxuICBjb25zdCBoYW5kbGVNb3ZlID0gKGU6IFJlYWN0Lk1vdXNlRXZlbnQgfCBSZWFjdC5Ub3VjaEV2ZW50KSA9PiB7XG4gICAgaWYgKCFpc0RyYWdnaW5nIHx8ICFpbWFnZVJlZi5jdXJyZW50KSByZXR1cm5cbiAgICBlLnByZXZlbnREZWZhdWx0KClcblxuICAgIGNvbnN0IHJlY3QgPSBpbWFnZVJlZi5jdXJyZW50LmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpXG4gICAgY29uc3QgcG9zID0gZ2V0RXZlbnRQb3NpdGlvbihlKVxuXG4gICAgY29uc3QgcmVsYXRpdmVYID0gcG9zLnggLSByZWN0LmxlZnRcbiAgICBjb25zdCByZWxhdGl2ZVkgPSBwb3MueSAtIHJlY3QudG9wXG5cbiAgICAvLyBLZWVwIGNyb3AgYXJlYSB3aXRoaW4gaW1hZ2UgYm91bmRzXG4gICAgY29uc3QgbmV3WCA9IE1hdGgubWF4KDAsIE1hdGgubWluKHJlbGF0aXZlWCAtIGNyb3BBcmVhLndpZHRoIC8gMiwgcmVjdC53aWR0aCAtIGNyb3BBcmVhLndpZHRoKSlcbiAgICBjb25zdCBuZXdZID0gTWF0aC5tYXgoMCwgTWF0aC5taW4ocmVsYXRpdmVZIC0gY3JvcEFyZWEuaGVpZ2h0IC8gMiwgcmVjdC5oZWlnaHQgLSBjcm9wQXJlYS5oZWlnaHQpKVxuXG4gICAgc2V0Q3JvcEFyZWEocHJldiA9PiAoeyAuLi5wcmV2LCB4OiBuZXdYLCB5OiBuZXdZIH0pKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRW5kID0gKCkgPT4ge1xuICAgIHNldElzRHJhZ2dpbmcoZmFsc2UpXG4gIH1cblxuICBjb25zdCBoYW5kbGVDcm9wID0gKCkgPT4ge1xuICAgIGNvbnN0IGNhbnZhcyA9IGNhbnZhc1JlZi5jdXJyZW50XG4gICAgY29uc3QgaW1hZ2UgPSBpbWFnZVJlZi5jdXJyZW50XG4gICAgaWYgKCFjYW52YXMgfHwgIWltYWdlKSByZXR1cm5cblxuICAgIGNvbnN0IGN0eCA9IGNhbnZhcy5nZXRDb250ZXh0KCcyZCcpXG4gICAgaWYgKCFjdHgpIHJldHVyblxuXG4gICAgLy8gQ2FsY3VsYXRlIHNjYWxlIGZhY3RvcnNcbiAgICBjb25zdCBzY2FsZVggPSBpbWFnZS5uYXR1cmFsV2lkdGggLyBpbWFnZS5vZmZzZXRXaWR0aFxuICAgIGNvbnN0IHNjYWxlWSA9IGltYWdlLm5hdHVyYWxIZWlnaHQgLyBpbWFnZS5vZmZzZXRIZWlnaHRcblxuICAgIC8vIFNldCBjYW52YXMgc2l6ZSB0byBkZXNpcmVkIG91dHB1dCBzaXplXG4gICAgY29uc3Qgb3V0cHV0U2l6ZSA9IDQwMFxuICAgIGNhbnZhcy53aWR0aCA9IG91dHB1dFNpemVcbiAgICBjYW52YXMuaGVpZ2h0ID0gb3V0cHV0U2l6ZVxuXG4gICAgLy8gRHJhdyBjcm9wcGVkIGFuZCByZXNpemVkIGltYWdlXG4gICAgY3R4LmRyYXdJbWFnZShcbiAgICAgIGltYWdlLFxuICAgICAgY3JvcEFyZWEueCAqIHNjYWxlWCxcbiAgICAgIGNyb3BBcmVhLnkgKiBzY2FsZVksXG4gICAgICBjcm9wQXJlYS53aWR0aCAqIHNjYWxlWCxcbiAgICAgIGNyb3BBcmVhLmhlaWdodCAqIHNjYWxlWSxcbiAgICAgIDAsXG4gICAgICAwLFxuICAgICAgb3V0cHV0U2l6ZSxcbiAgICAgIG91dHB1dFNpemVcbiAgICApXG5cbiAgICAvLyBDb252ZXJ0IHRvIGJhc2U2NFxuICAgIGNvbnN0IGNyb3BwZWRJbWFnZURhdGEgPSBjYW52YXMudG9EYXRhVVJMKCdpbWFnZS9qcGVnJywgMC45KVxuICAgIG9uQ3JvcChjcm9wcGVkSW1hZ2VEYXRhKVxuICB9XG5cbiAgY29uc3Qgc2V0Q3JvcFNpemUgPSAoc2l6ZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgbWF4U2l6ZSA9IE1hdGgubWluKGltYWdlU2l6ZS53aWR0aCwgaW1hZ2VTaXplLmhlaWdodCkgKiAwLjhcbiAgICBjb25zdCBuZXdTaXplID0gTWF0aC5taW4oc2l6ZSwgbWF4U2l6ZSlcbiAgICBzZXRDcm9wQXJlYShwcmV2ID0+ICh7XG4gICAgICAuLi5wcmV2LFxuICAgICAgd2lkdGg6IG5ld1NpemUsXG4gICAgICBoZWlnaHQ6IG5ld1NpemUsXG4gICAgICB4OiBNYXRoLm1heCgwLCBNYXRoLm1pbihwcmV2LngsIGltYWdlU2l6ZS53aWR0aCAtIG5ld1NpemUpKSxcbiAgICAgIHk6IE1hdGgubWF4KDAsIE1hdGgubWluKHByZXYueSwgaW1hZ2VTaXplLmhlaWdodCAtIG5ld1NpemUpKVxuICAgIH0pKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNiBweS00XCI+XG4gICAgICB7LyogQ3JvcCBTaXplIENvbnRyb2xzICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGdhcC0yIG1iLTRcIj5cbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENyb3BTaXplKDE1MCl9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyLWJsdWUtNjAwIHRleHQtYmx1ZS00MDAgaG92ZXI6YmctYmx1ZS02MDAvMTBcIlxuICAgICAgICA+XG4gICAgICAgICAg2LXYutmK2LFcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDcm9wU2l6ZSgyMDApfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ibHVlLTYwMCB0ZXh0LWJsdWUtNDAwIGhvdmVyOmJnLWJsdWUtNjAwLzEwXCJcbiAgICAgICAgPlxuICAgICAgICAgINmF2KrZiNiz2LdcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lXCJcbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDcm9wU2l6ZSgzMDApfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ibHVlLTYwMCB0ZXh0LWJsdWUtNDAwIGhvdmVyOmJnLWJsdWUtNjAwLzEwXCJcbiAgICAgICAgPlxuICAgICAgICAgINmD2KjZitixXG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTMwMCBtYi00XCI+2KfYtti62Lcg2YjYp9iz2K3YqCDZhNiq2K3YsdmK2YMg2YXZhti32YLYqSDYp9mE2YLYtTwvcD5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGlubGluZS1ibG9jayBiZy1ncmF5LTkwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgIDxpbWdcbiAgICAgICAgICAgIHJlZj17aW1hZ2VSZWZ9XG4gICAgICAgICAgICBzcmM9e2ltYWdlU3JjfVxuICAgICAgICAgICAgYWx0PVwi2LXZiNix2Kkg2YTZhNmC2LVcIlxuICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctZnVsbCBtYXgtaC05NiBvYmplY3QtY29udGFpbiBibG9jayBzZWxlY3Qtbm9uZVwiXG4gICAgICAgICAgICBvbkxvYWQ9eygpID0+IHtcbiAgICAgICAgICAgICAgaWYgKGltYWdlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IG9mZnNldFdpZHRoLCBvZmZzZXRIZWlnaHQgfSA9IGltYWdlUmVmLmN1cnJlbnRcbiAgICAgICAgICAgICAgICBzZXRJbWFnZVNpemUoeyB3aWR0aDogb2Zmc2V0V2lkdGgsIGhlaWdodDogb2Zmc2V0SGVpZ2h0IH0pXG4gICAgICAgICAgICAgICAgY29uc3Qgc2l6ZSA9IE1hdGgubWluKG9mZnNldFdpZHRoLCBvZmZzZXRIZWlnaHQpICogMC42XG4gICAgICAgICAgICAgICAgc2V0Q3JvcEFyZWEoe1xuICAgICAgICAgICAgICAgICAgeDogKG9mZnNldFdpZHRoIC0gc2l6ZSkgLyAyLFxuICAgICAgICAgICAgICAgICAgeTogKG9mZnNldEhlaWdodCAtIHNpemUpIC8gMixcbiAgICAgICAgICAgICAgICAgIHdpZHRoOiBzaXplLFxuICAgICAgICAgICAgICAgICAgaGVpZ2h0OiBzaXplXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICBzZXRJbWFnZUxvYWRlZCh0cnVlKVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9fVxuICAgICAgICAgICAgb25Nb3VzZU1vdmU9e2hhbmRsZU1vdmV9XG4gICAgICAgICAgICBvbk1vdXNlVXA9e2hhbmRsZUVuZH1cbiAgICAgICAgICAgIG9uTW91c2VMZWF2ZT17aGFuZGxlRW5kfVxuICAgICAgICAgICAgb25Ub3VjaE1vdmU9e2hhbmRsZU1vdmV9XG4gICAgICAgICAgICBvblRvdWNoRW5kPXtoYW5kbGVFbmR9XG4gICAgICAgICAgLz5cblxuICAgICAgICAgIHsvKiBTaW1wbGUgY3JvcCBvdmVybGF5ICovfVxuICAgICAgICAgIHtpbWFnZUxvYWRlZCAmJiAoXG4gICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIGJvcmRlci00IGJvcmRlci1ncmVlbi00MDAgYmctZ3JlZW4tNDAwLzEwIGN1cnNvci1tb3ZlIHNlbGVjdC1ub25lIHRvdWNoLW5vbmVcIlxuICAgICAgICAgICAgICBzdHlsZT17e1xuICAgICAgICAgICAgICAgIGxlZnQ6IGNyb3BBcmVhLngsXG4gICAgICAgICAgICAgICAgdG9wOiBjcm9wQXJlYS55LFxuICAgICAgICAgICAgICAgIHdpZHRoOiBjcm9wQXJlYS53aWR0aCxcbiAgICAgICAgICAgICAgICBoZWlnaHQ6IGNyb3BBcmVhLmhlaWdodCxcbiAgICAgICAgICAgICAgICB1c2VyU2VsZWN0OiAnbm9uZScsXG4gICAgICAgICAgICAgICAgV2Via2l0VXNlclNlbGVjdDogJ25vbmUnLFxuICAgICAgICAgICAgICAgIHRvdWNoQWN0aW9uOiAnbm9uZSdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgb25Nb3VzZURvd249e2hhbmRsZVN0YXJ0fVxuICAgICAgICAgICAgICBvblRvdWNoU3RhcnQ9e2hhbmRsZVN0YXJ0fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7LyogU2ltcGxlIGNlbnRlciBpbmRpY2F0b3IgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTEvMiBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiAtdHJhbnNsYXRlLXktMS8yXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJvcmRlci0yIGJvcmRlci13aGl0ZSByb3VuZGVkLWZ1bGwgYmctZ3JlZW4tNDAwLzgwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICAgICAgICA8Q3JvcCBjbGFzc05hbWU9XCJ3LTQgaC00IHRleHQtd2hpdGVcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogQ29ybmVyIGluZGljYXRvcnMgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1sZWZ0LTIgdy00IGgtNCBiZy1ncmVlbi00MDAgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMiAtcmlnaHQtMiB3LTQgaC00IGJnLWdyZWVuLTQwMCBib3JkZXItMiBib3JkZXItd2hpdGUgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLWJvdHRvbS0yIC1sZWZ0LTIgdy00IGgtNCBiZy1ncmVlbi00MDAgYm9yZGVyLTIgYm9yZGVyLXdoaXRlIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIC1ib3R0b20tMiAtcmlnaHQtMiB3LTQgaC00IGJnLWdyZWVuLTQwMCBib3JkZXItMiBib3JkZXItd2hpdGUgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8Y2FudmFzIHJlZj17Y2FudmFzUmVmfSBjbGFzc05hbWU9XCJoaWRkZW5cIiAvPlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMC8xMCBib3JkZXIgYm9yZGVyLWdyZWVuLTUwMC8yMCByb3VuZGVkLWxnIHAtNCBtdC00XCI+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JlZW4tMzAwIHRleHQtc20gbWItMlwiPvCfkqEg2YPZitmB2YrYqSDYp9mE2KfYs9iq2K7Yr9in2YU6PC9wPlxuICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC1ncmVlbi0yMDAgdGV4dC14cyBzcGFjZS15LTFcIj5cbiAgICAgICAgICA8bGk+4oCiINin2K7YqtixINit2KzZhSDZhdmG2LfZgtipINin2YTZgti1INmF2YYg2KfZhNij2LLYsdin2LEg2KPYudmE2KfZhzwvbGk+XG4gICAgICAgICAgPGxpPuKAoiDYp9i22LrYtyDZiNin2LPYrdioINin2YTZhdix2KjYuSDYp9mE2KPYrti22LEg2YTYqtit2LHZitmD2Yc8L2xpPlxuICAgICAgICAgIDxsaT7igKIg2YrYudmF2YQg2KjYp9mE2YTZhdizINi52YTZiSDYp9mE2YfYp9iq2YEg2YjYp9mE2YXYp9mI2LMg2LnZhNmJINin2YTZg9mF2KjZitmI2KrYsTwvbGk+XG4gICAgICAgICAgPGxpPuKAoiDYp9mE2LXZiNix2Kkg2LPYqtmP2K3Zgdi4INio2KzZiNiv2Kkg2LnYp9mE2YrYqSDZhdix2KjYudipINin2YTYtNmD2YQ8L2xpPlxuICAgICAgICA8L3VsPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBnYXAtMyBwdC02IGJvcmRlci10IGJvcmRlci1ncmF5LTcwMFwiPlxuICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2FuY2VsfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJvcmRlci1ncmF5LTYwMCB0ZXh0LWdyYXktMzAwIGhvdmVyOmJnLWdyYXktNzAwXCJcbiAgICAgICAgPlxuICAgICAgICAgINil2YTYutin2KFcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVDcm9wfVxuICAgICAgICAgIGRpc2FibGVkPXshaW1hZ2VMb2FkZWR9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JhZGllbnQtdG8tciBmcm9tLWdyZWVuLTYwMCB0by1ncmVlbi03MDAgaG92ZXI6ZnJvbS1ncmVlbi03MDAgaG92ZXI6dG8tZ3JlZW4tODAwIGRpc2FibGVkOm9wYWNpdHktNTBcIlxuICAgICAgICA+XG4gICAgICAgICAgPENyb3AgY2xhc3NOYW1lPVwidy00IGgtNCBtci0yXCIgLz5cbiAgICAgICAgICDZgti1INmI2KfYs9iq2K7Yr9in2YVcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlUmVmIiwidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJQbHVzIiwiRWRpdCIsIlRyYXNoMiIsIlgiLCJLZXkiLCJBbGVydENpcmNsZSIsIlBhY2thZ2UiLCJUeXBlIiwiQ3JvcCIsIlNhdmUiLCJBbGVydFRyaWFuZ2xlIiwiY2FsY3VsYXRlRGlzY291bnRQZXJjZW50YWdlIiwidmFsaWRhdGVEaXNjb3VudFByaWNpbmciLCJlbmhhbmNlUGFja2FnZVdpdGhEaXNjb3VudEluZm8iLCJjcmVhdGVQcm9kdWN0IiwidXBkYXRlUHJvZHVjdCIsIkRpYWxvZyIsIkRpYWxvZ0NvbnRlbnQiLCJEaWFsb2dIZWFkZXIiLCJEaWFsb2dUaXRsZSIsIkFsZXJ0IiwiQWxlcnREZXNjcmlwdGlvbiIsIkFsZXJ0VGl0bGUiLCJCdXR0b24iLCJ1c2VUb2FzdCIsIkltYWdlVXBsb2FkZXIiLCJ2YWxpZGF0ZVBhY2thZ2VOYW1lIiwidmFsaWRhdGVQYWNrYWdlUHJpY2UiLCJ2YWxpZGF0ZURpZ2l0YWxDb2RlcyIsInZhbGlkYXRlRmllbGRMYWJlbCIsInZhbGlkYXRlRHJvcGRvd25PcHRpb25zIiwidmFsaWRhdGVQcm9kdWN0RGF0YSIsInNhbml0aXplVGV4dCIsInNhbml0aXplRGlnaXRhbENvZGUiLCJzYW5pdGl6ZU51bWJlciIsIlNpbXBsZVByb2R1Y3RGb3JtIiwicHJvZHVjdCIsIm9uU2F2ZSIsIm9uQ2FuY2VsIiwiaXNFZGl0aW5nIiwiZm9ybURhdGEiLCJ0b2FzdCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImhhc1Vuc2F2ZWRDaGFuZ2VzIiwic2V0SGFzVW5zYXZlZENoYW5nZXMiLCJ2YWxpZGF0aW9uRXJyb3JzIiwic2V0VmFsaWRhdGlvbkVycm9ycyIsInZhbGlkYXRpb25XYXJuaW5ncyIsInNldFZhbGlkYXRpb25XYXJuaW5ncyIsImlzUGFja2FnZURpYWxvZ09wZW4iLCJzZXRJc1BhY2thZ2VEaWFsb2dPcGVuIiwiaXNGaWVsZERpYWxvZ09wZW4iLCJzZXRJc0ZpZWxkRGlhbG9nT3BlbiIsImVkaXRpbmdQYWNrYWdlSW5kZXgiLCJzZXRFZGl0aW5nUGFja2FnZUluZGV4IiwiZWRpdGluZ0ZpZWxkSW5kZXgiLCJzZXRFZGl0aW5nRmllbGRJbmRleCIsInNldEZvcm1EYXRhIiwibmFtZSIsImRlc2NyaXB0aW9uIiwiY2F0ZWdvcnkiLCJ0YWdzIiwiaW1hZ2UiLCJwYWNrYWdlcyIsImZpZWxkcyIsImZlYXR1cmVzIiwiaXNBY3RpdmUiLCJpc0ZlYXR1cmVkIiwiZGVsaXZlcnlUeXBlIiwicHJvZHVjdFR5cGUiLCJwcm9jZXNzaW5nVHlwZSIsInBhY2thZ2VGb3JtIiwic2V0UGFja2FnZUZvcm0iLCJhbW91bnQiLCJwcmljZSIsIm9yaWdpbmFsUHJpY2UiLCJwb3B1bGFyIiwiZGlnaXRhbENvZGVzIiwiZmllbGRGb3JtIiwic2V0RmllbGRGb3JtIiwibGFiZWwiLCJ0eXBlIiwicGxhY2Vob2xkZXIiLCJyZXF1aXJlZCIsIm9wdGlvbnMiLCJuZXdPcHRpb25UZXh0Iiwic2V0TmV3T3B0aW9uVGV4dCIsInRlbXBVcmwiLCJzZXRUZW1wVXJsIiwiaXNWYWxpZEltYWdlIiwic2V0SXNWYWxpZEltYWdlIiwiaXNUZXN0aW5nVXJsIiwic2V0SXNUZXN0aW5nVXJsIiwiaW1hZ2VEaW1lbnNpb25zIiwic2V0SW1hZ2VEaW1lbnNpb25zIiwid2lkdGgiLCJoZWlnaHQiLCJpc0ltYWdlQ3JvcERpYWxvZ09wZW4iLCJzZXRJc0ltYWdlQ3JvcERpYWxvZ09wZW4iLCJpbWFnZVNyYyIsInNldEltYWdlU3JjIiwiaW1hZ2VQcmV2aWV3Iiwic2V0SW1hZ2VQcmV2aWV3IiwiaW1hZ2VMb2FkZWQiLCJzZXRJbWFnZUxvYWRlZCIsImNyb3BBcmVhIiwic2V0Q3JvcEFyZWEiLCJ4IiwieSIsImltYWdlU2l6ZSIsInNldEltYWdlU2l6ZSIsImlzRHJhZ2dpbmciLCJzZXRJc0RyYWdnaW5nIiwiZHJhZ1N0YXJ0Iiwic2V0RHJhZ1N0YXJ0IiwiaW5wdXRJZCIsIk1hdGgiLCJyYW5kb20iLCJ0b1N0cmluZyIsInN1YnN0ciIsImltYWdlUmVmIiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwiZSIsInByZXZlbnREZWZhdWx0IiwicmV0dXJuVmFsdWUiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInVwZGF0ZUZvcm1EYXRhIiwidXBkYXRlciIsInJlc2V0Rm9ybSIsImhhbmRsZUlucHV0Q2hhbmdlIiwidGFyZ2V0IiwidmFsdWUiLCJoYW5kbGVBcHBseVVybCIsInByZXYiLCJoYW5kbGVJbWFnZUVycm9yIiwiaGFuZGxlVXBsb2FkQnV0dG9uQ2xpY2siLCJvblNlbGVjdEZpbGUiLCJoYW5kbGVTdGFydCIsImNsaWVudFgiLCJ0b3VjaGVzIiwiY2xpZW50WSIsImhhbmRsZU1vdmUiLCJkZWx0YVgiLCJkZWx0YVkiLCJtYXgiLCJtaW4iLCJoYW5kbGVFbmQiLCJoYW5kbGVJbWFnZUNyb3AiLCJjcm9wcGVkSW1hZ2VVcmwiLCJoYW5kbGVTYXZlIiwidmFsaWRhdGlvbiIsImlzVmFsaWQiLCJlcnJvcnMiLCJ3YXJuaW5ncyIsInRpdGxlIiwibGVuZ3RoIiwidmFyaWFudCIsImVuaGFuY2VkUGFja2FnZXMiLCJtYXAiLCJwa2ciLCJpbmRleCIsInNvcnRPcmRlciIsInByb2R1Y3REYXRhIiwiZmllbGQiLCJEYXRlIiwibm93IiwiY3JlYXRlZEJ5IiwidW5kZWZpbmVkIiwic2F2ZWRQcm9kdWN0IiwiaWQiLCJlcnJvciIsImNvbnNvbGUiLCJyZXNldFBhY2thZ2VGb3JtIiwicmVzZXRGaWVsZEZvcm0iLCJvcGVuUGFja2FnZURpYWxvZyIsImVkaXRQYWNrYWdlIiwiY29kZSIsImtleSIsImpvaW4iLCJzYXZlUGFja2FnZSIsIm5hbWVWYWxpZGF0aW9uIiwicHJpY2VWYWxpZGF0aW9uIiwiZGlzY291bnRWYWxpZGF0aW9uIiwiY29kZUxpbmVzIiwic3BsaXQiLCJsaW5lIiwidHJpbSIsImZpbHRlciIsIkJvb2xlYW4iLCJjb2Rlc1ZhbGlkYXRpb24iLCJzYW5pdGl6ZWRDb2RlcyIsImkiLCJ1c2VkIiwiYXNzaWduZWRUb09yZGVySWQiLCJjcmVhdGVkQXQiLCJuZXdQYWNrYWdlIiwicHVzaCIsInJlbW92ZVBhY2thZ2UiLCJwYWNrYWdlTmFtZSIsImNvbmZpcm0iLCJfIiwib3BlbkZpZWxkRGlhbG9nIiwiZWRpdEZpZWxkIiwiYWRkRHJvcGRvd25PcHRpb24iLCJzYW5pdGl6ZWRUZXh0IiwiZXhpc3RzIiwic29tZSIsIm9wdCIsIm5ld09wdGlvbiIsInRvTG93ZXJDYXNlIiwicmVwbGFjZSIsInJlbW92ZURyb3Bkb3duT3B0aW9uIiwib3B0aW9uSWQiLCJtb3ZlRHJvcGRvd25PcHRpb24iLCJkaXJlY3Rpb24iLCJmaW5kSW5kZXgiLCJuZXdJbmRleCIsImZvckVhY2giLCJzYXZlRmllbGQiLCJsYWJlbFZhbGlkYXRpb24iLCJvcHRpb25zVmFsaWRhdGlvbiIsIm5ld0ZpZWxkIiwicmVtb3ZlRmllbGQiLCJmaWVsZExhYmVsIiwiaGFuZGxlQ2FuY2VsIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJwIiwic2l6ZSIsIm9uQ2xpY2siLCJ1bCIsImxpIiwid2FybmluZyIsImg0IiwiaW5wdXQiLCJvbkNoYW5nZSIsInRleHRhcmVhIiwicm93cyIsInRhZyIsImN1cnJlbnRJbWFnZSIsIm9uSW1hZ2VDaGFuZ2VkIiwidXJsIiwicGxhY2Vob2xkZXJUZXh0IiwiYXNwZWN0UmF0aW8iLCJtYXhGaWxlU2l6ZSIsInNob3dVcmxJbnB1dCIsImNoZWNrZWQiLCJzcGFuIiwiaDUiLCJkaXNhYmxlZCIsIm9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJzdGVwIiwiTnVtYmVyIiwidG9GaXhlZCIsInNlbGVjdCIsIm9wdGlvbiIsImh0bWxGb3IiLCJJbWFnZUNyb3BwZXIiLCJvbkNyb3AiLCJjYW52YXNSZWYiLCJnZXRFdmVudFBvc2l0aW9uIiwiY3VycmVudCIsInJlY3QiLCJnZXRCb3VuZGluZ0NsaWVudFJlY3QiLCJwb3MiLCJyZWxhdGl2ZVgiLCJsZWZ0IiwicmVsYXRpdmVZIiwidG9wIiwibmV3WCIsIm5ld1kiLCJoYW5kbGVDcm9wIiwiY2FudmFzIiwiY3R4IiwiZ2V0Q29udGV4dCIsInNjYWxlWCIsIm5hdHVyYWxXaWR0aCIsIm9mZnNldFdpZHRoIiwic2NhbGVZIiwibmF0dXJhbEhlaWdodCIsIm9mZnNldEhlaWdodCIsIm91dHB1dFNpemUiLCJkcmF3SW1hZ2UiLCJjcm9wcGVkSW1hZ2VEYXRhIiwidG9EYXRhVVJMIiwic2V0Q3JvcFNpemUiLCJtYXhTaXplIiwibmV3U2l6ZSIsImltZyIsInJlZiIsInNyYyIsImFsdCIsIm9uTG9hZCIsIm9uTW91c2VNb3ZlIiwib25Nb3VzZVVwIiwib25Nb3VzZUxlYXZlIiwib25Ub3VjaE1vdmUiLCJvblRvdWNoRW5kIiwic3R5bGUiLCJ1c2VyU2VsZWN0IiwiV2Via2l0VXNlclNlbGVjdCIsInRvdWNoQWN0aW9uIiwib25Nb3VzZURvd24iLCJvblRvdWNoU3RhcnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});