"use client"

import React, { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, Refresh<PERSON><PERSON>, Check, Loader2, Camera } from "lucide-react"
import ReactCrop, { centerCrop, makeAspectCrop, Crop, PixelCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"

// Function to generate a blob from an image with a crop applied
function getCroppedImg(
  image: HTMLImageElement,
  crop: PixelCrop,
  fileName: string = 'cropped.jpg',
  qualityFactor: number = 0.98
): Promise<Blob> {
  const canvas = document.createElement('canvas')
  const scaleX = image.naturalWidth / image.width
  const scaleY = image.naturalHeight / image.height
  
  // Set to the actual pixel dimensions of the crop for high resolution
  canvas.width = crop.width * scaleX
  canvas.height = crop.height * scaleY
  
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('No 2d context')
  }

  // Use high quality rendering
  ctx.imageSmoothingQuality = 'high'
  ctx.imageSmoothingEnabled = true

  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width * scaleX,
    crop.height * scaleY
  )

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'))
          return
        }
        resolve(blob)
      },
      'image/jpeg',
      qualityFactor
    )
  })
}

// Image Uploader Component
interface ImageUploaderProps {
  currentImage?: string
  onImageChanged: (url: string) => void
  label?: string
  placeholderText?: string
  aspectRatio?: number
  className?: string
  showUrlInput?: boolean
  maxFileSize?: number // in MB
  allowedTypes?: string[]
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  currentImage = "",
  onImageChanged,
  label = "صورة",
  placeholderText = "أدخل رابط الصورة أو قم برفع صورة",
  aspectRatio = 1,
  className = "",
  showUrlInput = true,
  maxFileSize = 10,
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
}) => {
  const { toast } = useToast()
  
  // Create a unique ID for the file input to avoid conflicts when multiple instances are used
  const inputId = useRef(`image-upload-${Math.random().toString(36).substr(2, 9)}`)
  const [previewUrl, setPreviewUrl] = useState(currentImage)
  const [tempUrl, setTempUrl] = useState(currentImage)
  const [isUploading, setIsUploading] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [imgSrc, setImgSrc] = useState<string | null>(null)
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  const imgRef = useRef<HTMLImageElement>(null)
  const [isValidImage, setIsValidImage] = useState(true)
  const [isTestingUrl, setIsTestingUrl] = useState(false)
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 })

  // Update preview URL when currentImage prop changes
  useEffect(() => {
    if (currentImage !== previewUrl) {
      setPreviewUrl(currentImage)
      setTempUrl(currentImage)
    }
  }, [currentImage, previewUrl])

  const onSelectFile = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "خطأ",
        description: `يرجى اختيار ملف صورة صالح. الأنواع المدعومة: ${allowedTypes.map(type => type.split('/')[1].toUpperCase()).join(', ')}`,
        variant: "destructive",
      })
      return
    }

    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      toast({
        title: "خطأ",
        description: `حجم الصورة يجب أن يكون أقل من ${maxFileSize} ميجابايت`,
        variant: "destructive",
      })
      return
    }
    
    const reader = new FileReader()
    reader.addEventListener('load', () => {
      setImgSrc(reader.result?.toString() || '')
      setIsDialogOpen(true)
    })
    reader.readAsDataURL(file)
  }, [toast, allowedTypes, maxFileSize])

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget
    
    // Initialize with centered crop
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        aspectRatio,
        width,
        height
      ),
      width,
      height
    )
    
    setCrop(crop)
  }, [aspectRatio])

  const handleCropComplete = useCallback(async () => {
    if (!imgRef.current || !completedCrop) return
    
    try {
      setIsUploading(true)
      
      // Always use high quality
      const qualityFactor = 0.98
      
      const croppedBlob = await getCroppedImg(
        imgRef.current, 
        completedCrop, 
        `cropped_image.jpg`,
        qualityFactor
      )
      
      const file = new File([croppedBlob], `cropped_image.jpg`, { 
        type: "image/jpeg" 
      })
      
      // Convert to base64 for preview and storage
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = () => {
        const base64data = reader.result as string
        onImageChanged(base64data)
        setPreviewUrl(base64data)
        setTempUrl(base64data)
        setIsDialogOpen(false)
        
        toast({
          title: "تم بنجاح",
          description: "تم معالجة الصورة بنجاح",
        })
      }
    } catch (error) {
      console.error(error)
      toast({
        title: "خطأ",
        description: "فشل في معالجة الصورة",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setImgSrc(null)
    }
  }, [completedCrop, onImageChanged, toast])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempUrl(e.target.value)
    setIsValidImage(true) // Reset validation
  }

  const handleApplyUrl = async () => {
    try {
      setIsTestingUrl(true)
      
      // If URL is empty, just clear it
      if (!tempUrl) {
        setPreviewUrl("")
        onImageChanged("")
        setIsTestingUrl(false)
        return
      }
      
      // Test if the URL is a valid image before applying
      const isValid = await testImageUrl(tempUrl)
      
      if (isValid) {
        setPreviewUrl(tempUrl)
        onImageChanged(tempUrl)
        setIsValidImage(true)
      } else {
        setIsValidImage(false)
        toast({
          title: "خطأ",
          description: "رابط الصورة غير صالح",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error validating image URL:", error)
      setIsValidImage(false)
      toast({
        title: "خطأ",
        description: "فشل التحقق من رابط الصورة",
        variant: "destructive",
      })
    } finally {
      setIsTestingUrl(false)
    }
  }

  // Helper function to test if an image URL is valid
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = url
    })
  }

  const handleImageError = () => {
    setIsValidImage(false)
    setPreviewUrl("")
    setTempUrl("")
  }

  // This function will trigger the file input click
  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(inputId.current)
    if (fileInput) {
      fileInput.click()
    }
  }

  const removeImage = () => {
    onImageChanged("")
    setPreviewUrl("")
    setTempUrl("")
  }

  return (
    <div className={`space-y-3 ${className}`}>
      <div className="grid grid-cols-1 gap-3">
        {/* Image Preview Section */}
        {previewUrl && isValidImage && (
          <div className="relative overflow-hidden rounded-lg border border-gray-600/50 bg-gray-700/30">
            <img
              src={previewUrl}
              alt={label}
              className="w-full h-auto object-cover rounded-lg"
              style={{ aspectRatio: `${aspectRatio}` }}
              onError={handleImageError}
              onLoad={(e) => {
                const img = e.target as HTMLImageElement
                setImageDimensions({
                  width: img.naturalWidth,
                  height: img.naturalHeight
                })
                setIsValidImage(true)
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-between p-3">
              <div className="text-white text-xs">
                {isValidImage && imageDimensions.width > 0 && (
                  <span>{imageDimensions.width} × {imageDimensions.height}px</span>
                )}
              </div>
              <Button
                type="button"
                variant="destructive"
                size="icon"
                className="h-8 w-8 rounded-full bg-red-500/80 hover:bg-red-500 shadow-md"
                onClick={removeImage}
              >
                <X className="h-4 w-4" />
                <span className="sr-only">حذف</span>
              </Button>
            </div>
          </div>
        )}

        {/* URL Input and Upload Button */}
        {showUrlInput && (
          <div className="flex flex-col space-y-2">
            <Label htmlFor={inputId.current}>{label}</Label>
            <div className="flex space-x-2 space-x-reverse">
              <Input
                id={`url-${inputId.current}`}
                value={tempUrl}
                onChange={handleInputChange}
                placeholder={placeholderText}
                className="flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400"
              />
              <Button
                type="button"
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700 px-4"
                onClick={handleApplyUrl}
                disabled={isTestingUrl}
              >
                {isTestingUrl ? (
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="ml-2 h-4 w-4" />
                )}
                تطبيق
              </Button>
            </div>
          </div>
        )}

        {/* Hidden File Input */}
        <input
          type="file"
          id={inputId.current}
          className="hidden"
          accept={allowedTypes.join(',')}
          onChange={onSelectFile}
        />

        {/* Upload Button */}
        <Button
          type="button"
          variant="outline"
          className="w-full border-purple-600 text-purple-400 hover:bg-purple-600/10"
          onClick={handleUploadButtonClick}
        >
          <Upload className="ml-2 h-4 w-4" />
          رفع صورة
        </Button>

        {/* Image Crop Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogContent className="bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-xl">تعديل وضبط الصورة</DialogTitle>
              <DialogDescription className="text-gray-400">
                قم بضبط الصورة بالشكل المناسب قبل رفعها
              </DialogDescription>
            </DialogHeader>

            <div className="mt-2 mb-4 overflow-hidden rounded-lg border border-gray-600/50 bg-gray-700/30">
              {imgSrc && (
                <ReactCrop
                  crop={crop}
                  onChange={(_, percentCrop) => setCrop(percentCrop)}
                  onComplete={(c) => setCompletedCrop(c)}
                  aspect={aspectRatio}
                  className="max-h-[60vh] mx-auto"
                >
                  <img
                    ref={imgRef}
                    src={imgSrc}
                    alt="صورة للقص"
                    className="max-w-full max-h-[60vh] mx-auto object-contain"
                    onLoad={onImageLoad}
                  />
                </ReactCrop>
              )}
            </div>

            <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2 sm:gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsDialogOpen(false)
                  setImgSrc(null)
                }}
                className="w-full sm:w-auto border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                إلغاء
              </Button>
              <Button
                type="button"
                disabled={isUploading || !completedCrop?.width || !completedCrop?.height}
                onClick={handleCropComplete}
                className="w-full sm:w-auto bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {isUploading ? (
                  <>
                    <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                    جاري المعالجة...
                  </>
                ) : (
                  <>
                    <Check className="ml-2 h-4 w-4" />
                    تطبيق القص
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}

export default ImageUploader
