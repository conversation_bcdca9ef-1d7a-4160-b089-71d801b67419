// <PERSON><PERSON>t to verify environment variables during build
console.log('Checking environment variables for the build...');

const requiredEnvVars = [
  'VITE_FIREBASE_API_KEY',
  'VITE_FIREBASE_AUTH_DOMAIN',
  'VITE_FIREBASE_PROJECT_ID',
  'VITE_FIREBASE_STORAGE_BUCKET',
  'VITE_FIREBASE_MESSAGING_SENDER_ID',
  'VITE_FIREBASE_APP_ID'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('⚠️ Missing required environment variables:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  console.error('Please set these in your Netlify environment variables or .env.production file.');
  // Don't exit with error to allow build to continue for debugging
} else {
  console.log('✅ All required environment variables are present.');
}

// Print some debug info about the build environment
console.log('Build environment summary:');
console.log(`Node version: ${process.version}`);
console.log(`Environment: ${process.env.NODE_ENV}`);
console.log(`Firebase Project: ${process.env.VITE_FIREBASE_PROJECT_ID || 'Not set'}`); 