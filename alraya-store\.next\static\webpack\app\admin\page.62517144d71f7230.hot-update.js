"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,AlertTriangle,Crop,Edit,Hash,Key,Package,Plus,Save,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/alert */ \"(app-pages-browser)/./components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\",\n        quantityLimit: undefined\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\",\n            quantityLimit: undefined\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        var _formData_packages;\n        // Validate package name\n        const nameValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackageName)(packageForm.name);\n        if (!nameValidation.isValid) {\n            toast({\n                title: \"خطأ في اسم الحزمة\",\n                description: nameValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate package price\n        const priceValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validatePackagePrice)(packageForm.price);\n        if (!priceValidation.isValid) {\n            toast({\n                title: \"خطأ في السعر\",\n                description: priceValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate discount pricing if original price is provided\n        if (packageForm.originalPrice > 0) {\n            const discountValidation = (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.validateDiscountPricing)(packageForm.originalPrice, packageForm.price);\n            if (!discountValidation.isValid) {\n                toast({\n                    title: \"خطأ في الخصم\",\n                    description: discountValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        // Process and validate digital codes\n        const codeLines = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean);\n        const codesValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDigitalCodes)(codeLines);\n        if (!codesValidation.isValid) {\n            toast({\n                title: \"خطأ في الأكواد الرقمية\",\n                description: codesValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create digital codes with proper structure\n        const digitalCodes = (codesValidation.sanitizedCodes || []).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeDigitalCode)(key),\n                used: false,\n                assignedToOrderId: null,\n                createdAt: new Date()\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.name),\n            amount: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.amount),\n            price: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.price),\n            originalPrice: packageForm.originalPrice > 0 ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeNumber)(packageForm.originalPrice) : undefined,\n            description: packageForm.description ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(packageForm.description) : undefined,\n            popular: packageForm.popular,\n            quantityLimit: packageForm.quantityLimit,\n            isActive: true,\n            sortOrder: editingPackageIndex !== null ? editingPackageIndex : ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n            digitalCodes\n        };\n        updateFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingPackageIndex !== null ? \"تم تحديث الحزمة بنجاح\" : \"تم إضافة الحزمة بنجاح\"\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        var _formData_packages_index, _formData_packages;\n        const packageName = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : (_formData_packages_index = _formData_packages[index]) === null || _formData_packages_index === void 0 ? void 0 : _formData_packages_index.name) || \"الحزمة \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(packageName, '\"؟\\n\\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.'))) {\n            updateFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(packageName, '\" بنجاح')\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required,\n            options: field.options || []\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Dropdown option management functions\n    const addDropdownOption = ()=>{\n        const sanitizedText = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(newOptionText);\n        if (!sanitizedText) {\n            toast({\n                title: \"خطأ\",\n                description: \"يرجى إدخال نص الخيار\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Check for duplicates\n        const exists = fieldForm.options.some((opt)=>opt.label === sanitizedText);\n        if (exists) {\n            toast({\n                title: \"خطأ\",\n                description: \"هذا الخيار موجود بالفعل\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const newOption = {\n            id: \"option_\".concat(Date.now()),\n            value: sanitizedText.toLowerCase().replace(/\\s+/g, '_'),\n            label: sanitizedText,\n            sortOrder: fieldForm.options.length,\n            isActive: true\n        };\n        setFieldForm((prev)=>({\n                ...prev,\n                options: [\n                    ...prev.options,\n                    newOption\n                ]\n            }));\n        setNewOptionText(\"\");\n    };\n    const removeDropdownOption = (optionId)=>{\n        setFieldForm((prev)=>({\n                ...prev,\n                options: prev.options.filter((opt)=>opt.id !== optionId)\n            }));\n    };\n    const moveDropdownOption = (optionId, direction)=>{\n        setFieldForm((prev)=>{\n            const options = [\n                ...prev.options\n            ];\n            const index = options.findIndex((opt)=>opt.id === optionId);\n            if (index === -1) return prev;\n            const newIndex = direction === 'up' ? index - 1 : index + 1;\n            if (newIndex < 0 || newIndex >= options.length) return prev[options[index], options[newIndex]] = [\n                options[newIndex],\n                options[index]\n            ];\n            // Update sort orders\n            options.forEach((opt, i)=>{\n                opt.sortOrder = i;\n            });\n            return {\n                ...prev,\n                options\n            };\n        });\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        var _formData_fields;\n        // Validate field label\n        const labelValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateFieldLabel)(fieldForm.label);\n        if (!labelValidation.isValid) {\n            toast({\n                title: \"خطأ في تسمية الحقل\",\n                description: labelValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate dropdown options if it's a dropdown field\n        if (fieldForm.type === 'dropdown') {\n            const optionsValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.validateDropdownOptions)(fieldForm.options);\n            if (!optionsValidation.isValid) {\n                toast({\n                    title: \"خطأ في خيارات القائمة\",\n                    description: optionsValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.label),\n            placeholder: fieldForm.placeholder ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_9__.sanitizeText)(fieldForm.placeholder) : undefined,\n            required: fieldForm.required,\n            isActive: true,\n            sortOrder: editingFieldIndex !== null ? editingFieldIndex : ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n            validation: {},\n            options: fieldForm.type === 'dropdown' ? fieldForm.options : undefined\n        };\n        updateFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingFieldIndex !== null ? \"تم تحديث الحقل بنجاح\" : \"تم إضافة الحقل بنجاح\"\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        var _formData_fields_index, _formData_fields;\n        const fieldLabel = ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : (_formData_fields_index = _formData_fields[index]) === null || _formData_fields_index === void 0 ? void 0 : _formData_fields_index.label) || \"الحقل \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(fieldLabel, '\"؟'))) {\n            updateFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(fieldLabel, '\" بنجاح')\n            });\n        }\n    };\n    // Handle cancel with unsaved changes warning\n    const handleCancel = ()=>{\n        if (hasUnsavedChanges) {\n            if (confirm(\"لديك تغييرات غير محفوظة. هل أنت متأكد من الإلغاء؟\")) {\n                onCancel();\n            }\n        } else {\n            onCancel();\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 666,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 665,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 672,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 668,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 664,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: handleCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 683,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 677,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 663,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 662,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    validationErrors.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"يرجى تصحيح الأخطاء التالية:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationErrors.map((error, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: error\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 696,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, this),\n                    validationWarnings.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 708,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تحذيرات:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside space-y-1 mt-2\",\n                                    children: validationWarnings.map((warning, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: warning\n                                        }, index, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 713,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 711,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 710,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 707,\n                        columnNumber: 11\n                    }, this),\n                    hasUnsavedChanges && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.Alert, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 723,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertTitle, {\n                                children: \"تغييرات غير محفوظة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 724,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_5__.AlertDescription, {\n                                children: \"لديك تغييرات غير محفوظة. تأكد من حفظ عملك قبل المغادرة.\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 722,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 735,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 733,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 742,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 754,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>updateFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 755,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 753,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 765,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 778,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 776,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>updateFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 798,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 813,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 819,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 812,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 822,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 828,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 821,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 811,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 740,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 732,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 838,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 837,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 846,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 842,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 836,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 860,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 861,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 863,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 866,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 859,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 871,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 881,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 875,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 890,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 869,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 897,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 898,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 852,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 908,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 909,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 910,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 907,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 835,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 926,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 927,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 928,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 925,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 924,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 948,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"universal_input\" ? \"حقل إدخال\" : field.type === \"dropdown\" ? \"قائمة منسدلة\" : field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 950,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 957,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 949,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 963,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        field.type === \"dropdown\" && field.options && field.options.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-400 text-sm mb-1\",\n                                                                    children: \"الخيارات:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 967,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-1\",\n                                                                    children: [\n                                                                        field.options.slice(0, 3).map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"bg-gray-600/50 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                                                children: option.label\n                                                                            }, option.id, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 970,\n                                                                                columnNumber: 31\n                                                                            }, this)),\n                                                                        field.options.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"bg-gray-600/50 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                                            children: [\n                                                                                \"+\",\n                                                                                field.options.length - 3,\n                                                                                \" أخرى\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 975,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 968,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 966,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 947,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 990,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 984,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 999,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 993,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 946,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1009,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1015,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1007,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 923,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1033,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1031,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1024,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                variant: \"outline\",\n                                onClick: handleCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1042,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1023,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 689,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1058,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1057,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1068,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1069,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1067,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1079,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1080,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1078,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1066,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الحالي *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1094,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0.01\",\n                                                            value: packageForm.price,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        price: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1095,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"السعر الذي سيدفعه العميل\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1104,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1093,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"block text-sm font-medium mb-2\",\n                                                            children: \"السعر الأصلي (اختياري)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1108,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"number\",\n                                                            step: \"0.01\",\n                                                            min: \"0\",\n                                                            value: packageForm.originalPrice,\n                                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                                        ...prev,\n                                                                        originalPrice: Number(e.target.value)\n                                                                    })),\n                                                            placeholder: \"0.00\",\n                                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1109,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-gray-400 mt-1\",\n                                                            children: \"للعرض كخصم (اتركه فارغاً إذا لم يكن هناك خصم)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1118,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1107,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1092,\n                                            columnNumber: 15\n                                        }, this),\n                                        packageForm.originalPrice > 0 && packageForm.price > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-3\",\n                                            children: packageForm.originalPrice > packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1127,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-green-300 text-sm\",\n                                                        children: [\n                                                            \"خصم \",\n                                                            (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.calculateDiscountPercentage)(packageForm.originalPrice, packageForm.price),\n                                                            \"% (توفير \",\n                                                            (packageForm.originalPrice - packageForm.price).toFixed(2),\n                                                            \" دولار)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1128,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1126,\n                                                columnNumber: 21\n                                            }, this) : packageForm.originalPrice === packageForm.price ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1135,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-300 text-sm\",\n                                                        children: \"لا يوجد خصم (السعران متساويان)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1136,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1134,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-red-400 rounded-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1142,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-red-300 text-sm\",\n                                                        children: \"خطأ: السعر الأصلي يجب أن يكون أكبر من السعر الحالي\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1143,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1141,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1091,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1168,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1169,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1173,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1175,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1176,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1177,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1174,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"w-5 h-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1193,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"حد الكمية اليدوي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(للمنتجات بدون أكواد)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1192,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-orange-500/10 border border-orange-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 متى تستخدم هذا:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-orange-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• للمنتجات الرقمية بدون أكواد (اشتراكات، تراخيص)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1201,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• للمنتجات الفيزيائية مع مخزون محدود\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1202,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• اتركه فارغاً للمنتجات غير المحدودة\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1203,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1200,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1198,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            min: \"0\",\n                                            value: packageForm.quantityLimit || '',\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        quantityLimit: e.target.value ? Number(e.target.value) : undefined\n                                                    })),\n                                            placeholder: \"اتركه فارغاً للكمية غير المحدودة\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-1\",\n                                            children: packageForm.quantityLimit ? 'سيتم عرض \"متبقي '.concat(packageForm.quantityLimit, '\" للعملاء') : 'سيتم عرض \"متوفر\" للعملاء (كمية غير محدودة)'\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1218,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1229,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1235,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1227,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1064,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1242,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1241,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1056,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1055,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1264,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1272,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1273,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1271,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value,\n                                                        options: e.target.value === \"dropdown\" ? prev.options : []\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"universal_input\",\n                                                    children: \"حقل إدخال (نص، بريد إلكتروني، رقم)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"dropdown\",\n                                                    children: \"قائمة منسدلة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1295,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1285,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400 mt-1\",\n                                            children: fieldForm.type === \"universal_input\" ? \"حقل إدخال عام يمكن استخدامه للنصوص والأرقام والبريد الإلكتروني\" : \"قائمة خيارات يختار منها المستخدم\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1297,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1283,\n                                    columnNumber: 13\n                                }, this),\n                                fieldForm.type === \"universal_input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1308,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1309,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1307,\n                                    columnNumber: 15\n                                }, this),\n                                fieldForm.type === \"dropdown\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-3\",\n                                            children: \"خيارات القائمة المنسدلة\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1322,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: newOptionText,\n                                                    onChange: (e)=>setNewOptionText(e.target.value),\n                                                    placeholder: \"أدخل خيار جديد...\",\n                                                    className: \"flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                                    onKeyPress: (e)=>{\n                                                        if (e.key === 'Enter') {\n                                                            e.preventDefault();\n                                                            addDropdownOption();\n                                                        }\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1326,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                    type: \"button\",\n                                                    onClick: addDropdownOption,\n                                                    className: \"bg-blue-600 hover:bg-blue-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 1344,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1339,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1325,\n                                            columnNumber: 17\n                                        }, this),\n                                        fieldForm.options.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-2 max-h-40 overflow-y-auto\",\n                                            children: fieldForm.options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 bg-gray-700/50 rounded-lg p-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-1 text-sm\",\n                                                            children: option.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1353,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>moveDropdownOption(option.id, 'up'),\n                                                                    disabled: index === 0,\n                                                                    className: \"h-6 w-6 p-0\",\n                                                                    children: \"↑\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1355,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>moveDropdownOption(option.id, 'down'),\n                                                                    disabled: index === fieldForm.options.length - 1,\n                                                                    className: \"h-6 w-6 p-0\",\n                                                                    children: \"↓\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1365,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                                                    type: \"button\",\n                                                                    size: \"sm\",\n                                                                    variant: \"ghost\",\n                                                                    onClick: ()=>removeDropdownOption(option.id),\n                                                                    className: \"h-6 w-6 p-0 text-red-400 hover:text-red-300\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 1382,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 1375,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 1354,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, option.id, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1352,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1350,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-4 text-gray-400 text-sm border border-gray-600 rounded-lg border-dashed\",\n                                            children: \"لا توجد خيارات. أضف خيار واحد على الأقل.\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1389,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1321,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1397,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1413,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1412,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1261,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1260,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1435,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1434,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1433,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1441,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1432,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1431,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 661,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"uAx9ljgi+/WsnhASeI0N9MU3dn4=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1553,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1562,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1571,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1552,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1583,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1586,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1631,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1630,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1629,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1636,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1637,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1638,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1639,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1614,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1585,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1582,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1645,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1648,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1650,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1651,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1652,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1653,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1649,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1647,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1658,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_AlertTriangle_Crop_Edit_Hash_Key_Package_Plus_Save_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1670,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1665,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1657,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1550,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});