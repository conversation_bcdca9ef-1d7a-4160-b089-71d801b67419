/**
 * Admin ImageUploader Component
 *
 * This is a wrapper around the enhanced ImageUploader component
 * located in @/components/ui/ImageUploader for backward compatibility
 * and admin-specific configurations.
 */

"use client"

import ImageUploader, { ImageUploaderProps } from "@/components/ui/ImageUploader"
import { base64UploadService } from "@/lib/services/uploadService"

// Admin-specific ImageUploader Props (extends the base props)
interface AdminImageUploaderProps extends Omit<ImageUploaderProps, 'uploadService'> {
  // Admin can override upload service if needed
  uploadService?: ImageUploaderProps['uploadService']
}

/**
 * Admin ImageUploader Component
 *
 * Wrapper around the enhanced ImageUploader with admin-specific defaults
 */
const AdminImageUploader: React.FC<AdminImageUploaderProps> = ({
  uploadService = base64UploadService,
  variant = 'default',
  ...props
}) => {
  return (
    <ImageUploader
      uploadService={uploadService}
      variant={variant}
      {...props}
    />
  )
}

// Export as default for backward compatibility
export default AdminImageUploader

// Also export the enhanced ImageUploader and upload services for direct use
export { default as ImageUploader } from "@/components/ui/ImageUploader"
export * from "@/lib/services/uploadService"
