"use client"

import { User, Home, MessageCircle, Wallet, Store } from "lucide-react"
import { ChatBadge } from '@/components/chat/ChatSystem'
import { useGlobalChat } from '@/components/chat/GlobalChatProvider'

interface MobileNavigationProps {
  activeTab: string
  onTabChange: (tab: string) => void
  // ## Chat Integration: Unread message count for chat badge
  unreadChatCount?: number
  // ## Digital Content: Notification count for wallet badge
  walletNotificationCount?: number
}

export function MobileNavigation({
  activeTab,
  onTabChange,
  unreadChatCount = 0,
  walletNotificationCount = 0
}: MobileNavigationProps) {
  const { openChat } = useGlobalChat()

  // Standardized navigation items matching desktop
  const navItems = [
    {
      id: "profile",
      icon: <User className="h-5 w-5" />,
      label: "حسابي",
      action: () => onTabChange("profile")
    },
    {
      id: "shop",
      icon: <Store className="h-5 w-5" />,
      label: "المتجر",
      action: () => onTabChange("shop")
    },
    {
      id: "home",
      icon: <Home className="h-6 w-6" />,
      label: "الرئيسية",
      center: true,
      action: () => onTabChange("home")
    },
    {
      id: "wallet",
      icon: (
        <div className="relative">
          <Wallet className="h-5 w-5" />
          {walletNotificationCount > 0 && (
            <ChatBadge
              count={walletNotificationCount}
              className="absolute -top-2 -right-2 scale-75"
            />
          )}
        </div>
      ),
      label: "المحفظة",
      action: () => onTabChange("wallet")
    },
    {
      id: "chat",
      icon: (
        <div className="relative">
          <MessageCircle className="h-5 w-5" />
          {unreadChatCount > 0 && (
            <ChatBadge
              count={unreadChatCount}
              className="absolute -top-2 -right-2 scale-75"
            />
          )}
        </div>
      ),
      label: "المحادثة",
      action: () => openChat() // Opens chat directly
    },
  ]

  return (
    <nav className="lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40">
      <div className="bg-slate-800/95 backdrop-blur-2xl rounded-3xl px-4 py-3 shadow-2xl border border-slate-700/50 ring-1 ring-white/10">
        <div className="flex items-center justify-between gap-1">
          {navItems.map(({ id, icon, label, center, action }) =>
            center ? (
              <button
                key={id}
                onClick={action}
                className={`relative flex flex-col items-center justify-center p-4 rounded-2xl shadow-lg transition-all duration-300 transform hover:scale-110 active:scale-95 ${
                  activeTab === id
                    ? "bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-yellow-500/30 ring-2 ring-yellow-400/20"
                    : "bg-gradient-to-r from-yellow-400/80 to-orange-500/80 text-slate-900 hover:from-yellow-400 hover:to-orange-500 hover:shadow-yellow-500/20"
                }`}
              >
                <div className="relative">
                  {icon}
                  {activeTab === id && (
                    <div className="absolute inset-0 bg-white/20 rounded-full animate-pulse" />
                  )}
                </div>
                {label && (
                  <span className="text-xs font-semibold mt-1 opacity-90 whitespace-nowrap">{label}</span>
                )}
              </button>
            ) : (
              <button
                key={id}
                onClick={action}
                className={`relative flex flex-col items-center justify-center space-y-1 p-3 rounded-2xl transition-all duration-300 transform hover:scale-110 active:scale-95 min-w-[3.5rem] ${
                  activeTab === id
                    ? "bg-white/20 text-yellow-400 shadow-lg shadow-yellow-400/20 ring-1 ring-yellow-400/30 scale-105"
                    : "text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md"
                }`}
              >
                <div className="relative">
                  {icon}
                  {activeTab === id && (
                    <div className="absolute -inset-1 bg-yellow-400/20 rounded-full animate-pulse" />
                  )}
                </div>
                <span className={`text-xs font-medium transition-all duration-300 whitespace-nowrap ${
                  activeTab === id ? "text-yellow-400 font-semibold" : "text-slate-400"
                }`}>
                  {label}
                </span>

                {/* Active indicator */}
                {activeTab === id && (
                  <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-yellow-400 rounded-full animate-pulse shadow-lg shadow-yellow-400/50" />
                )}
              </button>
            ),
          )}
        </div>
      </div>
    </nav>
  )
}
