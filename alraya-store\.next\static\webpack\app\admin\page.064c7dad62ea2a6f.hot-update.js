"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   assignDigitalCode: () => (/* binding */ assignDigitalCode),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getAvailableCodes: () => (/* binding */ getAvailableCodes),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   hardDeleteProduct: () => (/* binding */ hardDeleteProduct),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(\"Product with id \".concat(id, \" not found\"));\n        }\n        console.log('✅ Updated product: \"'.concat(updatedProduct.name, '\"'));\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Soft delete by setting isActive to false\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (!product) return false;\n        const updated = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, {\n            isActive: false\n        });\n        console.log('✅ Soft deleted product: \"'.concat(product.name, '\"'));\n        return !!updated;\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return false;\n    }\n}\n/**\n * Hard delete product (completely remove from storage)\n */ async function hardDeleteProduct(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const success = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.delete(id);\n        console.log(\"✅ Hard deleted product with ID: \".concat(id));\n        return success;\n    } catch (error) {\n        console.error('Error hard deleting product:', error);\n        return false;\n    }\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages.filter((pkg)=>pkg.isActive)) || [];\n}\n/**\n * Get products by category\n */ async function getProductsByCategory(category) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getByCategory(category);\n}\n/**\n * Search products\n */ async function searchProducts(query) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.search(query);\n}\n/**\n * Get available digital codes for a package\n */ async function getAvailableCodes(productId, packageId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAvailableCodes(productId, packageId);\n}\n/**\n * Assign digital code to order\n */ async function assignDigitalCode(productId, packageId, orderId) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.assignDigitalCode(productId, packageId, orderId);\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});