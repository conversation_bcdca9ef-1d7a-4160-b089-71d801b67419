"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_services_orderService_ts";
exports.ids = ["_ssr_lib_services_orderService_ts"];
exports.modules = {

/***/ "(ssr)/./lib/data/defaultProductTemplates.ts":
/*!*********************************************!*\
  !*** ./lib/data/defaultProductTemplates.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTemplates: () => (/* binding */ defaultTemplates),\n/* harmony export */   deleteProductTemplate: () => (/* binding */ deleteProductTemplate),\n/* harmony export */   forceReinitializeTemplates: () => (/* binding */ forceReinitializeTemplates),\n/* harmony export */   freeFireTemplate: () => (/* binding */ freeFireTemplate),\n/* harmony export */   googlePlayTemplate: () => (/* binding */ googlePlayTemplate),\n/* harmony export */   initializeDefaultTemplates: () => (/* binding */ initializeDefaultTemplates),\n/* harmony export */   loadProductTemplates: () => (/* binding */ loadProductTemplates),\n/* harmony export */   pubgMobileTemplate: () => (/* binding */ pubgMobileTemplate),\n/* harmony export */   saveProductTemplate: () => (/* binding */ saveProductTemplate),\n/* harmony export */   tiktokCoinsTemplate: () => (/* binding */ tiktokCoinsTemplate)\n/* harmony export */ });\n// =====================================================\n// DEFAULT PRODUCT TEMPLATES\n// =====================================================\n// ## TODO: Replace with Supabase data loading\n// These templates will be used to initialize the system with sample products\n/**\n * Generate consistent ID for templates (fixed IDs for stability)\n */ function generateId(prefix = '', suffix = '') {\n    // Use consistent IDs instead of random ones to avoid localStorage issues\n    return `${prefix}${Date.now().toString(36)}${suffix}${Math.random().toString(36).substr(2, 4)}`;\n}\n/**\n * Generate fixed ID for template components (for consistency)\n */ function fixedId(id) {\n    return id;\n}\n/**\n * PUBG Mobile UC Top-up Template\n */ const pubgMobileTemplate = {\n    id: \"pubg-mobile-uc\",\n    name: \"شحن يوسي PUBG Mobile\",\n    nameEnglish: \"PUBG Mobile UC Top-up\",\n    description: \"شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار\",\n    descriptionEnglish: \"Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 25,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"pubg-player-id\"),\n            type: \"text\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                minLength: 8,\n                maxLength: 12,\n                pattern: \"^[0-9]+$\"\n            },\n            sortOrder: 0,\n            isActive: true\n        },\n        {\n            id: fixedId(\"pubg-server\"),\n            type: \"dropdown\",\n            name: \"server\",\n            label: \"الخادم\",\n            placeholder: \"اختر الخادم...\",\n            required: true,\n            options: [\n                \"الشرق الأوسط\",\n                \"أوروبا\",\n                \"آسيا\",\n                \"أمريكا الشمالية\",\n                \"أمريكا الجنوبية\"\n            ],\n            sortOrder: 1,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"pubg-uc-60\"),\n            name: \"60 يوسي\",\n            amount: \"60 UC\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-325\"),\n            name: \"325 يوسي\",\n            amount: \"325 UC\",\n            price: 25,\n            originalPrice: 30,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-660\"),\n            name: \"660 يوسي\",\n            amount: \"660 UC\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-1800\"),\n            name: \"1800 يوسي\",\n            amount: \"1800 UC\",\n            price: 120,\n            originalPrice: 150,\n            discount: 20,\n            popular: false,\n            isActive: true,\n            sortOrder: 3,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        }\n    ],\n    features: [\n        \"🚀 تسليم فوري للأكواد\",\n        \"💯 ضمان الجودة والأمان\",\n        \"🔒 معاملات آمنة ومشفرة\",\n        \"📱 يعمل على جميع الأجهزة\",\n        \"🎮 دعم فني متخصص\",\n        \"💳 طرق دفع متعددة\"\n    ],\n    tags: [\n        \"pubg\",\n        \"mobile\",\n        \"uc\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Free Fire Diamonds Template\n */ const freeFireTemplate = {\n    id: \"free-fire-diamonds\",\n    name: \"شحن جواهر Free Fire\",\n    nameEnglish: \"Free Fire Diamonds Top-up\",\n    description: \"شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار\",\n    descriptionEnglish: \"Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.\",\n        expiryDays: 7\n    },\n    fields: [\n        {\n            id: fixedId(\"ff-player-id\"),\n            type: \"number\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            labelEnglish: \"Player ID\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                min: 100000000,\n                max: 9999999999\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"ff-diamonds-100\"),\n            name: \"100 جوهرة\",\n            amount: \"100 💎\",\n            price: 10,\n            originalPrice: 12,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-520\"),\n            name: \"520 جوهرة\",\n            amount: \"520 💎\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-1080\"),\n            name: \"1080 جوهرة\",\n            amount: \"1080 💎\",\n            price: 100,\n            originalPrice: 120,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💎 جواهر أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎮 دعم فني 24/7\"\n    ],\n    tags: [\n        \"free fire\",\n        \"diamonds\",\n        \"جواهر\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Google Play Gift Card Template\n */ const googlePlayTemplate = {\n    id: \"google-play-gift-card\",\n    name: \"بطاقة هدايا Google Play\",\n    nameEnglish: \"Google Play Gift Card\",\n    description: \"بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي\",\n    descriptionEnglish: \"Digital Google Play Gift Cards - Use them to buy apps, games, and digital content\",\n    category: \"بطاقات الهدايا\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"coupon\",\n        deliveryInstructions: \"استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.\",\n        expiryDays: 365\n    },\n    fields: [\n        {\n            id: fixedId(\"gp-email\"),\n            type: \"email\",\n            name: \"email\",\n            label: \"البريد الإلكتروني\",\n            labelEnglish: \"Email Address\",\n            placeholder: \"أدخل بريدك الإلكتروني...\",\n            required: true,\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"gp-usd-10\"),\n            name: \"$10 USD\",\n            nameArabic: \"10 دولار\",\n            amount: \"$10 USD\",\n            price: 10,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-25\"),\n            name: \"$25 USD\",\n            nameArabic: \"25 دولار\",\n            amount: \"$25 USD\",\n            price: 25,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-50\"),\n            name: \"$50 USD\",\n            nameArabic: \"50 دولار\",\n            amount: \"$50 USD\",\n            price: 50,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🎁 بطاقة هدايا رقمية\",\n        \"🚀 تسليم فوري\",\n        \"🌍 صالحة عالمياً\",\n        \"📱 لجميع أجهزة Android\",\n        \"🔒 آمنة ومضمونة\"\n    ],\n    tags: [\n        \"google play\",\n        \"gift card\",\n        \"بطاقة هدايا\",\n        \"تطبيقات\"\n    ],\n    isActive: true,\n    isFeatured: false,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * TikTok Coins Template\n */ const tiktokCoinsTemplate = {\n    id: \"tiktok-coins\",\n    name: \"شحن عملات TikTok\",\n    nameEnglish: \"TikTok Coins Top-up\",\n    description: \"شحن فوري لعملات TikTok - ادعم المبدعين المفضلين لديك واحصل على المزيد من المزايا\",\n    descriptionEnglish: \"Instant TikTok Coins top-up - Support your favorite creators and get more features\",\n    category: \"وسائل التواصل\",\n    basePrice: 5,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"gift_code\",\n        deliveryInstructions: \"سيتم إضافة العملات إلى حسابك في TikTok فوراً بعد الدفع.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"tiktok-username\"),\n            type: \"text\",\n            name: \"username\",\n            label: \"اسم المستخدم في TikTok\",\n            placeholder: \"أدخل اسم المستخدم...\",\n            required: true,\n            validation: {\n                minLength: 3,\n                maxLength: 30\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"tiktok-coins-100\"),\n            name: \"100 عملة\",\n            amount: \"100 Coins\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-500\"),\n            name: \"500 عملة\",\n            amount: \"500 Coins\",\n            price: 20,\n            originalPrice: 25,\n            discount: 20,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-1000\"),\n            name: \"1000 عملة\",\n            amount: \"1000 Coins\",\n            price: 35,\n            originalPrice: 45,\n            discount: 22,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💰 عملات أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎁 ادعم المبدعين المفضلين\"\n    ],\n    tags: [\n        \"tiktok\",\n        \"coins\",\n        \"عملات\",\n        \"شحن\",\n        \"وسائل التواصل\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * All default templates\n */ const defaultTemplates = [\n    pubgMobileTemplate,\n    freeFireTemplate,\n    googlePlayTemplate,\n    tiktokCoinsTemplate\n];\n/**\n * Initialize default templates in localStorage (client-side only)\n * ## TODO: Replace with Supabase initialization\n */ function initializeDefaultTemplates() {\n    // Check if we're in a browser environment\n    if (true) {\n        console.log('Skipping localStorage initialization on server-side');\n        return;\n    }\n    try {\n        const existingTemplates = localStorage.getItem('productTemplates');\n        if (!existingTemplates) {\n            console.log('Initializing default product templates...');\n            localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            console.log(`Initialized ${defaultTemplates.length} default templates`);\n        } else {\n            // Validate existing templates\n            try {\n                const parsed = JSON.parse(existingTemplates);\n                if (!Array.isArray(parsed) || parsed.length === 0) {\n                    console.log('Invalid templates found, reinitializing...');\n                    localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n                } else {\n                    console.log(`Found ${parsed.length} existing templates in localStorage`);\n                }\n            } catch (parseError) {\n                console.log('Corrupted templates found, reinitializing...');\n                localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default templates:', error);\n    }\n}\n/**\n * Force reinitialize templates (useful for debugging)\n */ function forceReinitializeTemplates() {\n    if (true) return;\n    try {\n        console.log('Force reinitializing product templates...');\n        localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n        console.log(`Reinitialized ${defaultTemplates.length} templates`);\n    } catch (error) {\n        console.error('Error force reinitializing templates:', error);\n    }\n}\n/**\n * Load product templates from localStorage\n * ## TODO: Replace with Supabase query\n */ function loadProductTemplates() {\n    try {\n        const savedTemplates = localStorage.getItem('productTemplates');\n        if (savedTemplates) {\n            return JSON.parse(savedTemplates);\n        }\n        return [];\n    } catch (error) {\n        console.error('Error loading product templates:', error);\n        return [];\n    }\n}\n/**\n * Save product template to localStorage\n * ## TODO: Replace with Supabase insert/update\n */ function saveProductTemplate(template) {\n    try {\n        const templates = loadProductTemplates();\n        const existingIndex = templates.findIndex((t)=>t.id === template.id);\n        if (existingIndex >= 0) {\n            templates[existingIndex] = template;\n        } else {\n            templates.push(template);\n        }\n        localStorage.setItem('productTemplates', JSON.stringify(templates));\n    } catch (error) {\n        console.error('Error saving product template:', error);\n        throw error;\n    }\n}\n/**\n * Delete product template from localStorage\n * ## TODO: Replace with Supabase delete\n */ function deleteProductTemplate(templateId) {\n    try {\n        const templates = loadProductTemplates();\n        const filteredTemplates = templates.filter((t)=>t.id !== templateId);\n        localStorage.setItem('productTemplates', JSON.stringify(filteredTemplates));\n    } catch (error) {\n        console.error('Error deleting product template:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/data/defaultProductTemplates.ts\n");

/***/ }),

/***/ "(ssr)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(ssr)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(ssr)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Check if user has sufficient wallet balance for purchase\n */ async function checkWalletBalance(userEmail, requiredAmount) {\n    try {\n        // Get initial balance from localStorage (default: $100 for demo)\n        const initialBalance = parseFloat(localStorage.getItem(`wallet_balance_${userEmail}`) || '100');\n        // Get all transactions for this user\n        const allTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        const userTransactions = allTransactions.filter((t)=>t.userId === userEmail);\n        // Calculate total spent from completed purchase transactions\n        const totalSpent = userTransactions.filter((t)=>t.status === 'completed' && t.type === 'purchase').reduce((sum, t)=>sum + t.amount, 0);\n        // Calculate total deposits from completed deposit transactions\n        const totalDeposits = userTransactions.filter((t)=>t.status === 'completed' && t.type === 'deposit').reduce((sum, t)=>sum + t.amount, 0);\n        // Current balance = Initial + Deposits - Spent\n        const currentBalance = initialBalance + totalDeposits - totalSpent;\n        console.log(`💰 Wallet Balance Check for ${userEmail}:`, {\n            currentBalance: currentBalance.toFixed(2),\n            requiredAmount: requiredAmount.toFixed(2),\n            sufficient: currentBalance >= requiredAmount\n        });\n        return currentBalance >= requiredAmount;\n    } catch (error) {\n        console.error('Error checking wallet balance:', error);\n        return false // Fail safe - deny purchase if balance check fails\n        ;\n    }\n}\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(`Only ${availableCodes.length} codes available, but ${formData.quantity} requested`);\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(`Only ${selectedPackage.quantityLimit} items available, but ${formData.quantity} requested`);\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Check wallet balance before creating order\n        const totalCost = selectedPackage.price * formData.quantity;\n        const hasSufficientBalance = await checkWalletBalance(formData.userDetails.email, totalCost);\n        if (!hasSufficientBalance) {\n            throw new Error(`Insufficient wallet balance. Required: $${totalCost.toFixed(2)}`);\n        }\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: `timeline_${Date.now()}`,\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            // Ensure the package has available digital codes\n            await ensureDigitalCodesAvailable(formData.templateId, formData.selectedPackage.id);\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        // Create corresponding wallet transaction for unified display\n        await createWalletTransactionFromOrder(newOrder);\n        console.log(`✅ Created order: ${newOrder.id} for product: ${product.name}`);\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Ensure digital codes are available for a package\n */ async function ensureDigitalCodesAvailable(productId, packageId) {\n    const availableCodes = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.getAvailableCodes)(productId, packageId);\n    if (availableCodes.length === 0) {\n        console.log(`⚠️ No digital codes available for package ${packageId}, adding sample codes...`);\n        // Add sample digital codes to the package\n        const { ProductStorage } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/storage/localStorage */ \"(ssr)/./lib/storage/localStorage.ts\"));\n        const product = ProductStorage.getById(productId);\n        if (product) {\n            const pkg = product.packages.find((p)=>p.id === packageId);\n            if (pkg) {\n                // Add sample digital codes\n                const sampleCodes = [\n                    {\n                        id: `code_${Date.now()}_1`,\n                        key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: `code_${Date.now()}_2`,\n                        key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: `code_${Date.now()}_3`,\n                        key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    }\n                ];\n                pkg.digitalCodes = [\n                    ...pkg.digitalCodes || [],\n                    ...sampleCodes\n                ];\n                ProductStorage.update(productId, product);\n                console.log(`✅ Added ${sampleCodes.length} sample digital codes to package ${packageId}`);\n            }\n        }\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(`Failed to assign digital code ${i + 1} of ${quantity}`);\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', `${assignedCodes.length} digital codes assigned`);\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', `Failed to assign digital codes: ${error}`);\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        const updatedOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n        if (updatedOrder) {\n            console.log(`✅ Updated order ${orderId} status to: ${status}`);\n            // Update corresponding wallet transaction\n            await updateWalletTransactionFromOrder(updatedOrder);\n        }\n        return updatedOrder;\n    } catch (error) {\n        console.error('Error updating order status:', error);\n        return null;\n    }\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(`Order ${order.id}: ${error}`);\n            await updateOrderStatus(order.id, 'failed', `Processing failed: ${error}`);\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n/**\n * Create a wallet transaction from an order for unified display in the wallet\n */ async function createWalletTransactionFromOrder(order) {\n    try {\n        const transaction = {\n            userId: order.userDetails.email,\n            walletId: `wallet_${order.userDetails.email}`,\n            type: \"purchase\",\n            amount: order.totalPrice,\n            currency: order.currency,\n            description: `🛒 ${order.productName} - ${order.packageName}`,\n            referenceNumber: order.id,\n            status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n            orderId: order.id,\n            hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n            digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                status: order.status === 'completed' ? 'ready' : 'pending',\n                contents: order.digitalCodes.map((code)=>({\n                        id: code.id,\n                        type: 'game_code',\n                        title: `${order.packageName} - كود رقمي`,\n                        content: code.key,\n                        instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                        isRevealed: false,\n                        deliveredAt: order.updatedAt || order.createdAt\n                    })),\n                deliveryMethod: 'instant',\n                lastUpdated: order.updatedAt || order.createdAt\n            } : undefined,\n            createdAt: order.createdAt,\n            updatedAt: order.updatedAt || order.createdAt\n        };\n        // Create full transaction with ID and date\n        const fullTransaction = {\n            ...transaction,\n            id: `txn_order_${order.id}`,\n            date: order.createdAt\n        };\n        // Save to localStorage for wallet display\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Check if transaction already exists to avoid duplicates\n        const existingIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (existingIndex >= 0) {\n            // Update existing transaction\n            existingTransactions[existingIndex] = fullTransaction;\n        } else {\n            // Add new transaction\n            existingTransactions.unshift(fullTransaction);\n        }\n        localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n        // Dispatch event to notify wallet page of transaction update\n        window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n            detail: {\n                transaction: fullTransaction,\n                order\n            }\n        }));\n        console.log(`✅ Created wallet transaction for order: ${order.id}`);\n    } catch (error) {\n        console.error('Error creating wallet transaction from order:', error);\n    }\n}\n/**\n * Update wallet transaction when order status changes\n */ async function updateWalletTransactionFromOrder(order) {\n    try {\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Find the transaction for this order\n        const transactionIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (transactionIndex >= 0) {\n            // Update the existing transaction\n            const updatedTransaction = {\n                ...existingTransactions[transactionIndex],\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : order.status === 'cancelled' ? 'cancelled' : 'pending',\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: `${order.packageName} - كود رقمي`,\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                updatedAt: order.updatedAt || order.createdAt\n            };\n            existingTransactions[transactionIndex] = updatedTransaction;\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Dispatch event to notify wallet page\n            window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n                detail: {\n                    transaction: updatedTransaction,\n                    order\n                }\n            }));\n            console.log(`✅ Updated wallet transaction for order: ${order.id}`);\n        } else {\n            // Transaction doesn't exist, create it\n            await createWalletTransactionFromOrder(order);\n        }\n    } catch (error) {\n        console.error('Error updating wallet transaction from order:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/services/orderService.ts\n");

/***/ }),

/***/ "(ssr)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   assignDigitalCode: () => (/* binding */ assignDigitalCode),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getAvailableCodes: () => (/* binding */ getAvailableCodes),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   hardDeleteProduct: () => (/* binding */ hardDeleteProduct),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(ssr)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(ssr)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (false) {}\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (true) {\n            console.log('Server-side: returning default templates');\n            return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n        }\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log(`🔍 Looking for product with ID: \"${id}\"`);\n        // On server-side, search in default templates\n        if (true) {\n            const product = _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates.find((p)=>p.id === id && p.isActive);\n            return product || null;\n        }\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log(`✅ Found product: \"${product.name}\" (Active: ${product.isActive})`);\n            return product;\n        } else {\n            console.log(`❌ Product with ID \"${id}\" not found or inactive`);\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log(`✅ Created product: \"${newProduct.name}\" with ID: ${newProduct.id}`);\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(`Product with id ${id} not found`);\n        }\n        console.log(`✅ Updated product: \"${updatedProduct.name}\"`);\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Soft delete by setting isActive to false\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (!product) return false;\n        const updated = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, {\n            isActive: false\n        });\n        console.log(`✅ Soft deleted product: \"${product.name}\"`);\n        return !!updated;\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return false;\n    }\n}\n/**\n * Hard delete product (completely remove from storage)\n */ async function hardDeleteProduct(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const success = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.delete(id);\n        console.log(`✅ Hard deleted product with ID: ${id}`);\n        return success;\n    } catch (error) {\n        console.error('Error hard deleting product:', error);\n        return false;\n    }\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    const product = await getProductById(productId);\n    return product?.packages.filter((pkg)=>pkg.isActive) || [];\n}\n/**\n * Get products by category\n */ async function getProductsByCategory(category) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (true) {\n        return _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates.filter((p)=>p.isActive && p.category.toLowerCase().includes(category.toLowerCase()));\n    }\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getByCategory(category);\n}\n/**\n * Search products\n */ async function searchProducts(query) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (true) {\n        const searchTerm = query.toLowerCase();\n        return _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates.filter((p)=>p.isActive && (p.name.toLowerCase().includes(searchTerm) || p.description?.toLowerCase().includes(searchTerm) || p.category.toLowerCase().includes(searchTerm) || p.tags?.some((tag)=>tag.toLowerCase().includes(searchTerm))));\n    }\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.search(query);\n}\n/**\n * Get available digital codes for a package\n */ async function getAvailableCodes(productId, packageId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAvailableCodes(productId, packageId);\n}\n/**\n * Assign digital code to order\n */ async function assignDigitalCode(productId, packageId, orderId) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.assignDigitalCode(productId, packageId, orderId);\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map(([category, count])=>({\n            category,\n            count\n        })).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/services/productService.ts\n");

/***/ }),

/***/ "(ssr)/./lib/storage/localStorage.ts":
/*!*************************************!*\
  !*** ./lib/storage/localStorage.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStorage: () => (/* binding */ OrderStorage),\n/* harmony export */   ProductStorage: () => (/* binding */ ProductStorage),\n/* harmony export */   SettingsStorage: () => (/* binding */ SettingsStorage),\n/* harmony export */   UserStorage: () => (/* binding */ UserStorage),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   exportDatabase: () => (/* binding */ exportDatabase),\n/* harmony export */   importDatabase: () => (/* binding */ importDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/**\n * Local Storage Database System\n * \n * Provides a complete database-like interface using localStorage\n * Handles products, orders, users, and digital codes\n */ // Storage keys\nconst STORAGE_KEYS = {\n    PRODUCTS: 'alraya_products',\n    ORDERS: 'alraya_orders',\n    USERS: 'alraya_users',\n    SETTINGS: 'alraya_settings',\n    COUNTERS: 'alraya_counters'\n};\n// =====================================================\n// CORE STORAGE UTILITIES\n// =====================================================\n/**\n * Safe localStorage operations with error handling\n */ class SafeStorage {\n    static get(key, defaultValue) {\n        try {\n            if (true) return defaultValue;\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : defaultValue;\n        } catch (error) {\n            console.error(`Error reading from localStorage key \"${key}\":`, error);\n            return defaultValue;\n        }\n    }\n    static set(key, value) {\n        try {\n            if (true) return false;\n            localStorage.setItem(key, JSON.stringify(value));\n            return true;\n        } catch (error) {\n            console.error(`Error writing to localStorage key \"${key}\":`, error);\n            return false;\n        }\n    }\n    static remove(key) {\n        try {\n            if (true) return false;\n            localStorage.removeItem(key);\n            return true;\n        } catch (error) {\n            console.error(`Error removing localStorage key \"${key}\":`, error);\n            return false;\n        }\n    }\n    static clear() {\n        try {\n            if (true) return false;\n            localStorage.clear();\n            return true;\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n            return false;\n        }\n    }\n}\n/**\n * Generate unique IDs\n */ function generateId(prefix = '') {\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return `${prefix}${timestamp}_${random}`;\n}\n/**\n * Get and increment counter\n */ function getNextId(type) {\n    const counters = SafeStorage.get(STORAGE_KEYS.COUNTERS, {\n        products: 1,\n        orders: 1,\n        users: 1\n    });\n    const nextId = counters[type];\n    counters[type] = nextId + 1;\n    SafeStorage.set(STORAGE_KEYS.COUNTERS, counters);\n    return nextId;\n}\n// =====================================================\n// PRODUCT STORAGE OPERATIONS\n// =====================================================\nclass ProductStorage {\n    /**\n   * Get all products\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.PRODUCTS, []);\n    }\n    /**\n   * Get product by ID\n   */ static getById(id) {\n        const products = this.getAll();\n        return products.find((p)=>p.id === id) || null;\n    }\n    /**\n   * Get active products only\n   */ static getActive() {\n        return this.getAll().filter((p)=>p.isActive);\n    }\n    /**\n   * Get products by category\n   */ static getByCategory(category) {\n        return this.getActive().filter((p)=>p.category.toLowerCase().includes(category.toLowerCase()));\n    }\n    /**\n   * Search products\n   */ static search(query) {\n        const searchTerm = query.toLowerCase();\n        return this.getActive().filter((p)=>p.name.toLowerCase().includes(searchTerm) || p.description?.toLowerCase().includes(searchTerm) || p.category.toLowerCase().includes(searchTerm) || p.tags?.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n    }\n    /**\n   * Create new product\n   */ static create(productData) {\n        const products = this.getAll();\n        const now = new Date();\n        const newProduct = {\n            ...productData,\n            id: generateId('prod_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        products.push(newProduct);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event for real-time updates\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'create',\n                product: newProduct\n            }\n        }));\n        return newProduct;\n    }\n    /**\n   * Update existing product\n   */ static update(id, updates) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        const updatedProduct = {\n            ...products[index],\n            ...updates,\n            id,\n            updatedAt: new Date()\n        };\n        products[index] = updatedProduct;\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'update',\n                product: updatedProduct\n            }\n        }));\n        return updatedProduct;\n    }\n    /**\n   * Delete product\n   */ static delete(id) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        const deletedProduct = products[index];\n        products.splice(index, 1);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'delete',\n                product: deletedProduct\n            }\n        }));\n        return true;\n    }\n    /**\n   * Get available digital codes for a package\n   */ static getAvailableCodes(productId, packageId) {\n        const product = this.getById(productId);\n        if (!product) return [];\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return [];\n        return pkg.digitalCodes.filter((code)=>!code.used);\n    }\n    /**\n   * Assign digital code to order\n   */ static assignDigitalCode(productId, packageId, orderId) {\n        const product = this.getById(productId);\n        if (!product) return null;\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return null;\n        const availableCode = pkg.digitalCodes.find((code)=>!code.used);\n        if (!availableCode) return null;\n        // Mark code as used and assign to order\n        availableCode.used = true;\n        availableCode.assignedToOrderId = orderId;\n        availableCode.usedAt = new Date();\n        // Update product in storage\n        this.update(productId, product);\n        return availableCode;\n    }\n    /**\n   * Initialize with sample data if empty\n   */ static initializeSampleData() {\n        const existingProducts = this.getAll();\n        if (existingProducts.length > 0) return;\n        console.log('🔄 Initializing sample products...');\n        // Sample product with enhanced features\n        const sampleProduct = {\n            name: \"PUBG Mobile UC - شحن يوسي\",\n            description: \"شحن يوسي لعبة ببجي موبايل - توصيل فوري مع ضمان الجودة\",\n            category: \"ألعاب الموبايل\",\n            image: \"https://images.unsplash.com/photo-1542751371-adc38448a05e?w=500\",\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\",\n            fields: [\n                {\n                    id: \"field_1\",\n                    type: \"universal_input\",\n                    name: \"player_id\",\n                    label: \"معرف اللاعب\",\n                    placeholder: \"أدخل معرف اللاعب\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 0,\n                    validation: {}\n                },\n                {\n                    id: \"field_2\",\n                    type: \"dropdown\",\n                    name: \"server_region\",\n                    label: \"منطقة الخادم\",\n                    placeholder: \"اختر منطقة الخادم\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    validation: {},\n                    options: [\n                        {\n                            id: \"opt_1\",\n                            value: \"global\",\n                            label: \"عالمي\",\n                            sortOrder: 0,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_2\",\n                            value: \"asia\",\n                            label: \"آسيا\",\n                            sortOrder: 1,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_3\",\n                            value: \"europe\",\n                            label: \"أوروبا\",\n                            sortOrder: 2,\n                            isActive: true\n                        }\n                    ]\n                }\n            ],\n            packages: [\n                {\n                    id: \"pkg_1\",\n                    name: \"60 UC\",\n                    amount: \"60 Unknown Cash\",\n                    price: 0.99,\n                    isActive: true,\n                    sortOrder: 0,\n                    digitalCodes: [\n                        {\n                            id: \"code_1\",\n                            key: \"UC60-ABCD-1234\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_2\",\n                            key: \"UC60-EFGH-5678\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_2\",\n                    name: \"300 UC\",\n                    amount: \"300 Unknown Cash\",\n                    price: 4.99,\n                    originalPrice: 5.99,\n                    popular: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    digitalCodes: [\n                        {\n                            id: \"code_3\",\n                            key: \"UC300-IJKL-9012\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_4\",\n                            key: \"UC300-MNOP-3456\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_3\",\n                    name: \"1800 UC\",\n                    amount: \"1800 Unknown Cash\",\n                    price: 24.99,\n                    originalPrice: 29.99,\n                    description: \"أفضل قيمة - وفر 17%\",\n                    isActive: true,\n                    sortOrder: 2,\n                    digitalCodes: [\n                        {\n                            id: \"code_5\",\n                            key: \"UC1800-QRST-7890\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                }\n            ],\n            features: [\n                \"توصيل فوري\",\n                \"ضمان الجودة\",\n                \"دعم فني 24/7\",\n                \"أسعار تنافسية\"\n            ],\n            tags: [\n                \"pubg\",\n                \"mobile\",\n                \"uc\",\n                \"gaming\",\n                \"instant\"\n            ],\n            isActive: true,\n            isFeatured: true\n        };\n        this.create(sampleProduct);\n        console.log('✅ Sample product initialized successfully');\n    }\n}\n// =====================================================\n// ORDER STORAGE OPERATIONS\n// =====================================================\nclass OrderStorage {\n    /**\n   * Get all orders\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.ORDERS, []);\n    }\n    /**\n   * Get order by ID\n   */ static getById(id) {\n        const orders = this.getAll();\n        return orders.find((o)=>o.id === id) || null;\n    }\n    /**\n   * Get orders by user (using email as identifier)\n   */ static getByUser(userEmail) {\n        return this.getAll().filter((o)=>o.userDetails.email === userEmail);\n    }\n    /**\n   * Get orders by status\n   */ static getByStatus(status) {\n        return this.getAll().filter((o)=>o.status === status);\n    }\n    /**\n   * Create new order\n   */ static create(orderData) {\n        const orders = this.getAll();\n        const now = new Date();\n        const newOrder = {\n            ...orderData,\n            id: generateId('order_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        orders.push(newOrder);\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'create',\n                order: newOrder\n            }\n        }));\n        return newOrder;\n    }\n    /**\n   * Update order status\n   */ static updateStatus(id, status, notes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === id);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            status,\n            updatedAt: new Date(),\n            ...notes && {\n                notes\n            }\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'update',\n                order: updatedOrder\n            }\n        }));\n        return updatedOrder;\n    }\n    /**\n   * Add digital codes to order\n   */ static addDigitalCodes(orderId, codes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === orderId);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            digitalCodes: [\n                ...orders[index].digitalCodes || [],\n                ...codes\n            ],\n            updatedAt: new Date()\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        return updatedOrder;\n    }\n}\n// =====================================================\n// USER STORAGE OPERATIONS\n// =====================================================\nclass UserStorage {\n    /**\n   * Get all users\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.USERS, []);\n    }\n    /**\n   * Get user by email\n   */ static getByEmail(email) {\n        const users = this.getAll();\n        return users.find((u)=>u.email === email) || null;\n    }\n    /**\n   * Create or update user\n   */ static upsert(userData) {\n        const users = this.getAll();\n        const existingIndex = users.findIndex((u)=>u.email === userData.email);\n        const now = new Date();\n        if (existingIndex >= 0) {\n            // Update existing user\n            const updatedUser = {\n                ...users[existingIndex],\n                ...userData,\n                updatedAt: now\n            };\n            users[existingIndex] = updatedUser;\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return updatedUser;\n        } else {\n            // Create new user\n            const newUser = {\n                ...userData,\n                id: generateId('user_'),\n                createdAt: now,\n                updatedAt: now\n            };\n            users.push(newUser);\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return newUser;\n        }\n    }\n}\n// =====================================================\n// SETTINGS STORAGE\n// =====================================================\nclass SettingsStorage {\n    /**\n   * Get application settings\n   */ static get() {\n        return SafeStorage.get(STORAGE_KEYS.SETTINGS, {});\n    }\n    /**\n   * Update settings\n   */ static update(settings) {\n        const currentSettings = this.get();\n        const updatedSettings = {\n            ...currentSettings,\n            ...settings\n        };\n        SafeStorage.set(STORAGE_KEYS.SETTINGS, updatedSettings);\n    }\n    /**\n   * Get specific setting\n   */ static getSetting(key, defaultValue) {\n        const settings = this.get();\n        return settings[key] !== undefined ? settings[key] : defaultValue;\n    }\n    /**\n   * Set specific setting\n   */ static setSetting(key, value) {\n        this.update({\n            [key]: value\n        });\n    }\n}\n// =====================================================\n// DATABASE INITIALIZATION\n// =====================================================\n/**\n * Initialize the entire localStorage database\n */ function initializeDatabase() {\n    console.log('🔄 Initializing localStorage database...');\n    // Initialize sample data if needed\n    ProductStorage.initializeSampleData();\n    console.log('✅ Database initialized successfully');\n}\n/**\n * Clear all data (for development/testing)\n */ function clearDatabase() {\n    console.log('🗑️ Clearing all localStorage data...');\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        SafeStorage.remove(key);\n    });\n    console.log('✅ Database cleared successfully');\n}\n/**\n * Export database (for backup/migration)\n */ function exportDatabase() {\n    const data = {};\n    Object.entries(STORAGE_KEYS).forEach(([name, key])=>{\n        data[name] = SafeStorage.get(key, null);\n    });\n    return data;\n}\n/**\n * Import database (for backup/migration)\n */ function importDatabase(data) {\n    Object.entries(STORAGE_KEYS).forEach(([name, key])=>{\n        if (data[name] !== undefined) {\n            SafeStorage.set(key, data[name]);\n        }\n    });\n    console.log('✅ Database imported successfully');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/storage/localStorage.ts\n");

/***/ })

};
;