"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./app/shop/page.tsx":
/*!***************************!*\
  !*** ./app/shop/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShopPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils/pricingUtils */ \"(app-pages-browser)/./lib/utils/pricingUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Enhanced categories with icons\nconst categories = [\n    {\n        id: \"all\",\n        label: \"جميع المنتجات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 35,\n            columnNumber: 46\n        }, undefined)\n    },\n    {\n        id: \"ألعاب الموبايل\",\n        label: \"ألعاب الموبايل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"منصات التواصل\",\n        label: \"منصات التواصل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 56\n        }, undefined)\n    },\n    {\n        id: \"بطاقات الألعاب\",\n        label: \"بطاقات الألعاب\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"digital\",\n        label: \"🎮 منتجات رقمية\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 52\n        }, undefined)\n    }\n];\nfunction ShopPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"shop\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use global currency context for price conversion\n    const { formatPrice } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter)();\n    // Load products from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShopPage.useEffect\": ()=>{\n            const loadProducts = {\n                \"ShopPage.useEffect.loadProducts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const productList = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_13__.getProducts)();\n                        setProducts(productList);\n                    } catch (error) {\n                        console.error('Error loading products:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ShopPage.useEffect.loadProducts\"];\n            loadProducts();\n            // Listen for real-time product updates\n            const handleProductsUpdated = {\n                \"ShopPage.useEffect.handleProductsUpdated\": (event)=>{\n                    console.log('Products updated:', event.detail);\n                    loadProducts() // Reload products when they change\n                    ;\n                }\n            }[\"ShopPage.useEffect.handleProductsUpdated\"];\n            window.addEventListener('productsUpdated', handleProductsUpdated);\n            return ({\n                \"ShopPage.useEffect\": ()=>{\n                    window.removeEventListener('productsUpdated', handleProductsUpdated);\n                }\n            })[\"ShopPage.useEffect\"];\n        }\n    }[\"ShopPage.useEffect\"], []);\n    // Navigation handler\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n        } else if (tab === \"support\") {\n            router.push(\"/contact\");\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    // Product click handler\n    const handleProductClick = (productId)=>{\n        router.push(\"/shop/\".concat(productId));\n    };\n    // Filter products based on search and category\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase());\n        // Handle category filtering including digital products\n        let matchesCategory = false;\n        if (selectedCategory === \"all\") {\n            matchesCategory = true;\n        } else if (selectedCategory === \"digital\") {\n            matchesCategory = product.productType === \"digital\";\n        } else {\n            matchesCategory = product.category === selectedCategory;\n        }\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"متجر الألعاب\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"اكتشف أفضل العروض لشحن ألعابك المفضلة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            placeholder: \"ابحث عن لعبتك المفضلة...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pr-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:border-yellow-400 focus:outline-none appearance-none\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category.id,\n                                                className: \"bg-slate-800\",\n                                                children: category.label\n                                            }, category.id, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-8\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"flex items-center gap-2 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-0\" : \"border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400\"),\n                                children: [\n                                    category.icon,\n                                    category.label\n                                ]\n                            }, category.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⏳\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-2\",\n                                children: \"جاري تحميل المنتجات...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400\",\n                                children: \"يرجى الانتظار\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 md:gap-6\",\n                                children: filteredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        onClick: ()=>handleProductClick(product.id),\n                                        className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 hover:border-yellow-400/30 hover:shadow-2xl hover:shadow-yellow-400/10 transition-all duration-500 cursor-pointer group overflow-hidden transform hover:scale-105 hover:-translate-y-2\",\n                                        style: {\n                                            animationDelay: \"\".concat(index * 100, \"ms\")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square overflow-hidden\",\n                                                    children: [\n                                                        product.previewImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.previewImage,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center relative overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 group-hover:from-yellow-400/10 group-hover:to-orange-500/10 transition-all duration-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-slate-400 group-hover:text-yellow-400 transition-colors duration-300 relative z-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"أكواد فورية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.isProductPopular)(product) && product.productType !== \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 animate-pulse\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg\",\n                                                                children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.isProductPopular)(product) && product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"أكواد فورية\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg block\",\n                                                                    children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs font-medium border-0 \".concat(product.processingType === \"instant\" ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400'),\n                                                                children: product.processingType === \"instant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"فوري\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"يدوي\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 w-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-white\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-yellow-400 fill-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium\",\n                                                                                    children: \"4.8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-slate-300\",\n                                                                                    children: \"(جديد)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs font-bold text-yellow-400\",\n                                                                            children: [\n                                                                                \"من \",\n                                                                                formatPrice((0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.getProductStartingPrice)(product))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-bold text-center group-hover:text-yellow-400 transition-colors duration-300 text-sm leading-tight\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-xs text-center mt-2 line-clamp-2 group-hover:text-slate-300 transition-colors duration-300\",\n                                                            children: product.description || \"منتج رائع من متجر الراية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center gap-2 mt-3 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: product.estimatedTime\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                product.packages.length,\n                                                                                \" حزمة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"لا توجد منتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400\",\n                                        children: products.length === 0 ? \"لم يتم إنشاء أي منتجات بعد. قم بإنشاء منتجات من لوحة الإدارة.\" : \"لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopPage, \"WUb9mGFUDanYkBwzB0RnMYO0Zr8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter\n    ];\n});\n_c = ShopPage;\nvar _c;\n$RefreshReg$(_c, \"ShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/shop/page.tsx\n"));

/***/ })

});