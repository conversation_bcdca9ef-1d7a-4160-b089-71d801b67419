"use client"

import { useState, useEffect } from "react"
import { AppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { WalletBalance } from "@/components/wallet/WalletBalance"
import { WalletTransactions } from "@/components/wallet/WalletTransactions"
import { WalletOrders } from "@/components/wallet/WalletOrders"
import { Currency, Order } from "@/lib/types"
import { Wallet } from "lucide-react"
import { useRouter } from "next/navigation"
import { useCurrency } from "@/contexts/CurrencyContext"
import { getDigitalContentNotificationCount } from "@/lib/utils/digitalContentUtils"
import { useChat } from "@/lib/hooks/useChat"
import { getOrdersByUser, getOrderStats } from "@/lib/services/orderService"


export function WalletPage() {
  const [activeTab, setActiveTab] = useState("wallet") // Set to wallet tab since this is wallet page
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [orders, setOrders] = useState<Order[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Use global currency context
  const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = useCurrency()

  // Mock user email (in real app, this would come from auth)
  const userEmail = "<EMAIL>"

  // Load user orders
  useEffect(() => {
    const loadOrders = async () => {
      try {
        setIsLoading(true)
        const userOrders = await getOrdersByUser(userEmail)
        setOrders(userOrders)
      } catch (error) {
        console.error('Error loading orders:', error)
        setError('فشل في تحميل الطلبات')
      } finally {
        setIsLoading(false)
      }
    }

    loadOrders()

    // Listen for real-time order updates
    const handleOrdersUpdated = (event: CustomEvent) => {
      console.log('Orders updated:', event.detail)
      loadOrders() // Reload orders when they change
    }

    window.addEventListener('ordersUpdated', handleOrdersUpdated as EventListener)

    return () => {
      window.removeEventListener('ordersUpdated', handleOrdersUpdated as EventListener)
    }
  }, [userEmail])

  // Get chat unread count for navigation badge
  const { unreadCount: chatUnreadCount } = useChat({
    userId: 'customer-demo', // TODO: Replace with actual user ID
    userType: 'customer'
  })

  // Enhanced handler for currency change - now updates global currency context
  const handleCurrencyChange = async (currency: Currency) => {
    try {
      // Update global currency context (affects entire app)
      await updateCurrency(currency)
    } catch (err) {
      console.error('Failed to update currency preference:', err)
    }
  }

  // Calculate wallet statistics from orders
  const walletStats = {
    totalSpent: orders
      .filter(order => order.status === 'completed')
      .reduce((sum, order) => sum + order.totalPrice, 0),
    totalOrders: orders.length,
    completedOrders: orders.filter(order => order.status === 'completed').length,
    pendingOrders: orders.filter(order => order.status === 'pending').length,
    digitalCodes: orders
      .filter(order => order.status === 'completed')
      .reduce((sum, order) => sum + (order.digitalCodes?.length || 0), 0)
  }

  // ## Handler for adding balance - navigates to checkout page
  const handleAddBalance = () => {
    router.push("/checkout")
  }

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <Wallet className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            محفظتي
          </h1>
          <p className="text-slate-300 text-lg">
            إدارة رصيدك ومعاملاتك المالية بسهولة وأمان
          </p>
        </div>

        {/* Wallet Balance Section */}
        <div className="mb-8">
          <WalletBalance
            walletData={{
              balances: [
                {
                  id: "balance_demo",
                  userId: userEmail,
                  currency: selectedCurrency,
                  amount: walletStats.totalSpent, // Show total spent as balance for demo
                  reservedBalance: 0,
                  totalDeposits: walletStats.totalSpent,
                  totalWithdrawals: 0,
                  totalPurchases: walletStats.totalSpent,
                  lastTransactionAt: orders.length > 0 ? orders[0].createdAt : undefined,
                  lastUpdated: new Date(),
                  createdAt: new Date()
                }
              ],
              selectedCurrency: selectedCurrency,
              availableCurrencies: availableCurrencies,
              totalPurchases: walletStats.totalSpent,
              transactions: [] // TODO: Implement transactions
            }}
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            onAddBalance={handleAddBalance}
            isLoading={isLoading}
          />
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg">
            <p className="text-red-100">{error}</p>
          </div>
        )}

        {/* Orders Section */}
        <div className="mb-8">
          <WalletOrders
            orders={orders}
            isLoading={isLoading}
          />
        </div>

        {/* Transactions Section */}
        <WalletTransactions
          transactions={[]} // TODO: Implement transactions
          selectedCurrency={selectedCurrency}
          isLoading={isLoading}
        />
      </main>

      <MobileNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        walletNotificationCount={getDigitalContentNotificationCount([])} // TODO: Implement transactions
        unreadChatCount={chatUnreadCount}
      />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
