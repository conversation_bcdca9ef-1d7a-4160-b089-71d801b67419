"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./components/layout/DesktopFooter.tsx":
/*!*********************************************!*\
  !*** ./components/layout/DesktopFooter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DesktopFooter: () => (/* binding */ DesktopFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ DesktopFooter auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DesktopFooter(param) {\n    let { activeTab, onTabChange } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat)();\n    // Standardized navigation items matching mobile\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, this),\n            label: \"الدعم الفني\",\n            action: ()=>onTabChange(\"support\") // Routes to contact page, consistent with mobile\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-8 py-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center gap-8\",\n                children: navItems.map((param)=>{\n                    let { id, icon, label, action } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: action,\n                        className: \"relative flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 min-w-[5rem] \".concat(activeTab === id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg shadow-yellow-500/25 ring-1 ring-yellow-400/30\" : \"text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    icon,\n                                    activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -inset-1 bg-white/20 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-medium transition-all duration-300 \".concat(activeTab === id ? \"text-slate-900\" : \"text-slate-400\"),\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 15\n                            }, this),\n                            activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-slate-900 rounded-full animate-pulse\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopFooter, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat\n    ];\n});\n_c = DesktopFooter;\nvar _c;\n$RefreshReg$(_c, \"DesktopFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/DesktopFooter.tsx\n"));

/***/ })

});