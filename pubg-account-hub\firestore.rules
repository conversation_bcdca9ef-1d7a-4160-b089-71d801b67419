rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if the user is an admin
    function isAdmin() {
      return request.auth != null && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Helper function to check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper function to check if user is accessing their own data
    function isOwner(userId) {
      return request.auth != null && request.auth.uid == userId;
    }
    
    // Data validation helpers
    function isValidAccount(data) {
      return data.title is string &&
             data.priceUSD is number && data.priceUSD >= 0 &&
             data.priceEGP is number && data.priceEGP >= 0 &&
             data.image is string &&
             data.category is string;
    }
    
    function isValidContactMessage(data) {
      return data.name is string &&
             data.email is string &&
             data.message is string &&
             data.createdAt is timestamp;
    }
    
    // Rate limiting for anonymous operations (Unix timestamp check)
    function notRateLimited() {
      return request.time.toMillis() - (request.resource.data.createdAt.toMillis() || 0) >= 10000;
    }
    
    // Public collections - allow read access to everyone
    match /blogPosts/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /accounts/{document=**} {
      allow read: if true;
      allow create, update: if isAdmin() && isValidAccount(request.resource.data);
      allow delete: if isAdmin();
    }
    
    match /ucPackages/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /categories/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /settings/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /ucStoreSettings/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /contactInfo/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    match /mods/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Page visibility settings
    match /pageVisibility/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Page content collections for public pages
    match /pageContent/{document=**} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // User-specific collections
    match /users/{userId} {
      allow read: if isOwner(userId) || isAdmin();
      allow create, update: if isOwner(userId) || isAdmin();
      allow delete: if isAdmin();
    }
    
    // User carts - only allow user to access their own cart
    match /userCarts/{userId} {
      allow read, write: if isOwner(userId);
      allow read: if isAdmin();
    }
    
    match /orders/{document} {
      allow create: if isAuthenticated();
      allow read, update, delete: if isAuthenticated() && 
        (request.auth.uid == resource.data.userId || isAdmin());
    }
    
    match /contactMessages/{document} {
      // Anyone can submit contact messages but with rate limiting and validation
      allow create: if isValidContactMessage(request.resource.data) && notRateLimited();
      allow read, delete: if isAdmin();
    }
    
    // Default deny
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
