"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(\"Only \".concat(availableCodes.length, \" codes available, but \").concat(formData.quantity, \" requested\"));\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(\"Only \".concat(selectedPackage.quantityLimit, \" items available, but \").concat(formData.quantity, \" requested\"));\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: \"timeline_\".concat(Date.now()),\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            // Ensure the package has available digital codes\n            await ensureDigitalCodesAvailable(formData.templateId, formData.selectedPackage.id);\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        // Create corresponding wallet transaction for unified display\n        await createWalletTransactionFromOrder(newOrder);\n        console.log(\"✅ Created order: \".concat(newOrder.id, \" for product: \").concat(product.name));\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Ensure digital codes are available for a package\n */ async function ensureDigitalCodesAvailable(productId, packageId) {\n    const availableCodes = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.getAvailableCodes)(productId, packageId);\n    if (availableCodes.length === 0) {\n        console.log(\"⚠️ No digital codes available for package \".concat(packageId, \", adding sample codes...\"));\n        // Add sample digital codes to the package\n        const { ProductStorage } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\"));\n        const product = ProductStorage.getById(productId);\n        if (product) {\n            const pkg = product.packages.find((p)=>p.id === packageId);\n            if (pkg) {\n                // Add sample digital codes\n                const sampleCodes = [\n                    {\n                        id: \"code_\".concat(Date.now(), \"_1\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_2\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    },\n                    {\n                        id: \"code_\".concat(Date.now(), \"_3\"),\n                        key: \"SAMPLE-\".concat(Math.random().toString(36).substr(2, 4).toUpperCase(), \"-\").concat(Math.random().toString(36).substr(2, 4).toUpperCase()),\n                        used: false,\n                        assignedToOrderId: null,\n                        createdAt: new Date()\n                    }\n                ];\n                pkg.digitalCodes = [\n                    ...pkg.digitalCodes || [],\n                    ...sampleCodes\n                ];\n                ProductStorage.update(productId, product);\n                console.log(\"✅ Added \".concat(sampleCodes.length, \" sample digital codes to package \").concat(packageId));\n            }\n        }\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(\"Failed to assign digital code \".concat(i + 1, \" of \").concat(quantity));\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', \"\".concat(assignedCodes.length, \" digital codes assigned\"));\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', \"Failed to assign digital codes: \".concat(error));\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        const updatedOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n        if (updatedOrder) {\n            console.log(\"✅ Updated order \".concat(orderId, \" status to: \").concat(status));\n            // Update corresponding wallet transaction\n            await updateWalletTransactionFromOrder(updatedOrder);\n        }\n        return updatedOrder;\n    } catch (error) {\n        console.error('Error updating order status:', error);\n        return null;\n    }\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(\"Order \".concat(order.id, \": \").concat(error));\n            await updateOrderStatus(order.id, 'failed', \"Processing failed: \".concat(error));\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n/**\n * Create a wallet transaction from an order for unified display in the wallet\n */ async function createWalletTransactionFromOrder(order) {\n    try {\n        const transaction = {\n            userId: order.userDetails.email,\n            walletId: \"wallet_\".concat(order.userDetails.email),\n            type: \"purchase\",\n            amount: order.totalPrice,\n            currency: order.currency,\n            description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n            referenceNumber: order.id,\n            status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n            orderId: order.id,\n            hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n            digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                status: order.status === 'completed' ? 'ready' : 'pending',\n                contents: order.digitalCodes.map((code)=>({\n                        id: code.id,\n                        type: 'game_code',\n                        title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                        content: code.key,\n                        instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                        isRevealed: false,\n                        deliveredAt: order.updatedAt || order.createdAt\n                    })),\n                deliveryMethod: 'instant',\n                lastUpdated: order.updatedAt || order.createdAt\n            } : undefined,\n            createdAt: order.createdAt,\n            updatedAt: order.updatedAt || order.createdAt\n        };\n        // Create full transaction with ID and date\n        const fullTransaction = {\n            ...transaction,\n            id: \"txn_order_\".concat(order.id),\n            date: order.createdAt\n        };\n        // Save to localStorage for wallet display\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Check if transaction already exists to avoid duplicates\n        const existingIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (existingIndex >= 0) {\n            // Update existing transaction\n            existingTransactions[existingIndex] = fullTransaction;\n        } else {\n            // Add new transaction\n            existingTransactions.unshift(fullTransaction);\n        }\n        localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n        // Dispatch event to notify wallet page of transaction update\n        window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n            detail: {\n                transaction: fullTransaction,\n                order\n            }\n        }));\n        console.log(\"✅ Created wallet transaction for order: \".concat(order.id));\n    } catch (error) {\n        console.error('Error creating wallet transaction from order:', error);\n    }\n}\n/**\n * Update wallet transaction when order status changes\n */ async function updateWalletTransactionFromOrder(order) {\n    try {\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Find the transaction for this order\n        const transactionIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (transactionIndex >= 0) {\n            // Update the existing transaction\n            const updatedTransaction = {\n                ...existingTransactions[transactionIndex],\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : order.status === 'cancelled' ? 'cancelled' : 'pending',\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                updatedAt: order.updatedAt || order.createdAt\n            };\n            existingTransactions[transactionIndex] = updatedTransaction;\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Dispatch event to notify wallet page\n            window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n                detail: {\n                    transaction: updatedTransaction,\n                    order\n                }\n            }));\n            console.log(\"✅ Updated wallet transaction for order: \".concat(order.id));\n        } else {\n            // Transaction doesn't exist, create it\n            await createWalletTransactionFromOrder(order);\n        }\n    } catch (error) {\n        console.error('Error updating wallet transaction from order:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/orderService.ts\n"));

/***/ })

});