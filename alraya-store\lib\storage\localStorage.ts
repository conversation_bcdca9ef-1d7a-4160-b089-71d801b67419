/**
 * Local Storage Database System
 * 
 * Provides a complete database-like interface using localStorage
 * Handles products, orders, users, and digital codes
 */

import { ProductTemplate, Order, User, DigitalCode } from '@/lib/types'

// Storage keys
const STORAGE_KEYS = {
  PRODUCTS: 'alraya_products',
  ORDERS: 'alraya_orders', 
  USERS: 'alraya_users',
  SETTINGS: 'alraya_settings',
  COUNTERS: 'alraya_counters'
} as const

// Counter management for auto-incrementing IDs
interface Counters {
  products: number
  orders: number
  users: number
}

// =====================================================
// CORE STORAGE UTILITIES
// =====================================================

/**
 * Safe localStorage operations with error handling
 */
class SafeStorage {
  static get<T>(key: string, defaultValue: T): T {
    try {
      if (typeof window === 'undefined') return defaultValue
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue
    } catch (error) {
      console.error(`Error reading from localStorage key "${key}":`, error)
      return defaultValue
    }
  }

  static set<T>(key: string, value: T): boolean {
    try {
      if (typeof window === 'undefined') return false
      localStorage.setItem(key, JSON.stringify(value))
      return true
    } catch (error) {
      console.error(`Error writing to localStorage key "${key}":`, error)
      return false
    }
  }

  static remove(key: string): boolean {
    try {
      if (typeof window === 'undefined') return false
      localStorage.removeItem(key)
      return true
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error)
      return false
    }
  }

  static clear(): boolean {
    try {
      if (typeof window === 'undefined') return false
      localStorage.clear()
      return true
    } catch (error) {
      console.error('Error clearing localStorage:', error)
      return false
    }
  }
}

/**
 * Generate unique IDs
 */
function generateId(prefix: string = ''): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substr(2, 9)
  return `${prefix}${timestamp}_${random}`
}

/**
 * Get and increment counter
 */
function getNextId(type: keyof Counters): number {
  const counters = SafeStorage.get<Counters>(STORAGE_KEYS.COUNTERS, {
    products: 1,
    orders: 1,
    users: 1
  })
  
  const nextId = counters[type]
  counters[type] = nextId + 1
  
  SafeStorage.set(STORAGE_KEYS.COUNTERS, counters)
  return nextId
}

// =====================================================
// PRODUCT STORAGE OPERATIONS
// =====================================================

export class ProductStorage {
  /**
   * Get all products
   */
  static getAll(): ProductTemplate[] {
    return SafeStorage.get<ProductTemplate[]>(STORAGE_KEYS.PRODUCTS, [])
  }

  /**
   * Get product by ID
   */
  static getById(id: string): ProductTemplate | null {
    const products = this.getAll()
    return products.find(p => p.id === id) || null
  }

  /**
   * Get active products only
   */
  static getActive(): ProductTemplate[] {
    return this.getAll().filter(p => p.isActive)
  }

  /**
   * Get products by category
   */
  static getByCategory(category: string): ProductTemplate[] {
    return this.getActive().filter(p => 
      p.category.toLowerCase().includes(category.toLowerCase())
    )
  }

  /**
   * Search products
   */
  static search(query: string): ProductTemplate[] {
    const searchTerm = query.toLowerCase()
    return this.getActive().filter(p => 
      p.name.toLowerCase().includes(searchTerm) ||
      p.description?.toLowerCase().includes(searchTerm) ||
      p.category.toLowerCase().includes(searchTerm) ||
      p.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  /**
   * Create new product
   */
  static create(productData: Omit<ProductTemplate, 'id' | 'createdAt' | 'updatedAt'>): ProductTemplate {
    const products = this.getAll()
    const now = new Date()
    
    const newProduct: ProductTemplate = {
      ...productData,
      id: generateId('prod_'),
      createdAt: now,
      updatedAt: now
    }

    products.push(newProduct)
    SafeStorage.set(STORAGE_KEYS.PRODUCTS, products)
    
    // Trigger storage event for real-time updates
    window.dispatchEvent(new CustomEvent('productsUpdated', { 
      detail: { action: 'create', product: newProduct } 
    }))
    
    return newProduct
  }

  /**
   * Update existing product
   */
  static update(id: string, updates: Partial<ProductTemplate>): ProductTemplate | null {
    const products = this.getAll()
    const index = products.findIndex(p => p.id === id)
    
    if (index === -1) return null
    
    const updatedProduct: ProductTemplate = {
      ...products[index],
      ...updates,
      id, // Ensure ID doesn't change
      updatedAt: new Date()
    }
    
    products[index] = updatedProduct
    SafeStorage.set(STORAGE_KEYS.PRODUCTS, products)
    
    // Trigger storage event
    window.dispatchEvent(new CustomEvent('productsUpdated', { 
      detail: { action: 'update', product: updatedProduct } 
    }))
    
    return updatedProduct
  }

  /**
   * Delete product
   */
  static delete(id: string): boolean {
    const products = this.getAll()
    const index = products.findIndex(p => p.id === id)
    
    if (index === -1) return false
    
    const deletedProduct = products[index]
    products.splice(index, 1)
    SafeStorage.set(STORAGE_KEYS.PRODUCTS, products)
    
    // Trigger storage event
    window.dispatchEvent(new CustomEvent('productsUpdated', { 
      detail: { action: 'delete', product: deletedProduct } 
    }))
    
    return true
  }

  /**
   * Get available digital codes for a package
   */
  static getAvailableCodes(productId: string, packageId: string): DigitalCode[] {
    const product = this.getById(productId)
    if (!product) return []
    
    const pkg = product.packages.find(p => p.id === packageId)
    if (!pkg || !pkg.digitalCodes) return []
    
    return pkg.digitalCodes.filter(code => !code.used)
  }

  /**
   * Assign digital code to order
   */
  static assignDigitalCode(productId: string, packageId: string, orderId: string): DigitalCode | null {
    const product = this.getById(productId)
    if (!product) return null
    
    const pkg = product.packages.find(p => p.id === packageId)
    if (!pkg || !pkg.digitalCodes) return null
    
    const availableCode = pkg.digitalCodes.find(code => !code.used)
    if (!availableCode) return null
    
    // Mark code as used and assign to order
    availableCode.used = true
    availableCode.assignedToOrderId = orderId
    availableCode.usedAt = new Date()
    
    // Update product in storage
    this.update(productId, product)
    
    return availableCode
  }

  /**
   * Initialize with sample data if empty
   */
  static initializeSampleData(): void {
    const existingProducts = this.getAll()
    if (existingProducts.length > 0) return

    console.log('🔄 Initializing sample products...')

    // Sample product with enhanced features
    const sampleProduct = {
      name: "PUBG Mobile UC - شحن يوسي",
      description: "شحن يوسي لعبة ببجي موبايل - توصيل فوري مع ضمان الجودة",
      category: "ألعاب الموبايل",
      image: "https://images.unsplash.com/photo-1542751371-adc38448a05e?w=500",
      deliveryType: "code_based" as const,
      productType: "digital" as const,
      processingType: "instant" as const,
      fields: [
        {
          id: "field_1",
          type: "universal_input" as const,
          name: "player_id",
          label: "معرف اللاعب",
          placeholder: "أدخل معرف اللاعب",
          required: true,
          isActive: true,
          sortOrder: 0,
          validation: {}
        },
        {
          id: "field_2",
          type: "dropdown" as const,
          name: "server_region",
          label: "منطقة الخادم",
          placeholder: "اختر منطقة الخادم",
          required: true,
          isActive: true,
          sortOrder: 1,
          validation: {},
          options: [
            { id: "opt_1", value: "global", label: "عالمي", sortOrder: 0, isActive: true },
            { id: "opt_2", value: "asia", label: "آسيا", sortOrder: 1, isActive: true },
            { id: "opt_3", value: "europe", label: "أوروبا", sortOrder: 2, isActive: true }
          ]
        }
      ],
      packages: [
        {
          id: "pkg_1",
          name: "60 UC",
          amount: "60 Unknown Cash",
          price: 0.99,
          isActive: true,
          sortOrder: 0,
          digitalCodes: [
            {
              id: "code_1",
              key: "UC60-ABCD-1234",
              used: false,
              assignedToOrderId: null,
              createdAt: new Date()
            },
            {
              id: "code_2",
              key: "UC60-EFGH-5678",
              used: false,
              assignedToOrderId: null,
              createdAt: new Date()
            }
          ]
        },
        {
          id: "pkg_2",
          name: "300 UC",
          amount: "300 Unknown Cash",
          price: 4.99,
          originalPrice: 5.99,
          popular: true,
          isActive: true,
          sortOrder: 1,
          digitalCodes: [
            {
              id: "code_3",
              key: "UC300-IJKL-9012",
              used: false,
              assignedToOrderId: null,
              createdAt: new Date()
            },
            {
              id: "code_4",
              key: "UC300-MNOP-3456",
              used: false,
              assignedToOrderId: null,
              createdAt: new Date()
            }
          ]
        },
        {
          id: "pkg_3",
          name: "1800 UC",
          amount: "1800 Unknown Cash",
          price: 24.99,
          originalPrice: 29.99,
          description: "أفضل قيمة - وفر 17%",
          isActive: true,
          sortOrder: 2,
          digitalCodes: [
            {
              id: "code_5",
              key: "UC1800-QRST-7890",
              used: false,
              assignedToOrderId: null,
              createdAt: new Date()
            }
          ]
        }
      ],
      features: ["توصيل فوري", "ضمان الجودة", "دعم فني 24/7", "أسعار تنافسية"],
      tags: ["pubg", "mobile", "uc", "gaming", "instant"],
      isActive: true,
      isFeatured: true
    }

    this.create(sampleProduct)
    console.log('✅ Sample product initialized successfully')
  }
}

// =====================================================
// ORDER STORAGE OPERATIONS
// =====================================================

export class OrderStorage {
  /**
   * Get all orders
   */
  static getAll(): Order[] {
    return SafeStorage.get<Order[]>(STORAGE_KEYS.ORDERS, [])
  }

  /**
   * Get order by ID
   */
  static getById(id: string): Order | null {
    const orders = this.getAll()
    return orders.find(o => o.id === id) || null
  }

  /**
   * Get orders by user (using email as identifier)
   */
  static getByUser(userEmail: string): Order[] {
    return this.getAll().filter(o => o.userDetails.email === userEmail)
  }

  /**
   * Get orders by status
   */
  static getByStatus(status: string): Order[] {
    return this.getAll().filter(o => o.status === status)
  }

  /**
   * Create new order
   */
  static create(orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'>): Order {
    const orders = this.getAll()
    const now = new Date()

    const newOrder: Order = {
      ...orderData,
      id: generateId('order_'),
      createdAt: now,
      updatedAt: now
    }

    orders.push(newOrder)
    SafeStorage.set(STORAGE_KEYS.ORDERS, orders)

    // Trigger storage event
    window.dispatchEvent(new CustomEvent('ordersUpdated', {
      detail: { action: 'create', order: newOrder }
    }))

    return newOrder
  }

  /**
   * Update order status
   */
  static updateStatus(id: string, status: string, notes?: string): Order | null {
    const orders = this.getAll()
    const index = orders.findIndex(o => o.id === id)

    if (index === -1) return null

    const updatedOrder: Order = {
      ...orders[index],
      status,
      updatedAt: new Date(),
      ...(notes && { notes })
    }

    orders[index] = updatedOrder
    SafeStorage.set(STORAGE_KEYS.ORDERS, orders)

    // Trigger storage event
    window.dispatchEvent(new CustomEvent('ordersUpdated', {
      detail: { action: 'update', order: updatedOrder }
    }))

    return updatedOrder
  }

  /**
   * Add digital codes to order
   */
  static addDigitalCodes(orderId: string, codes: DigitalCode[]): Order | null {
    const orders = this.getAll()
    const index = orders.findIndex(o => o.id === orderId)

    if (index === -1) return null

    const updatedOrder: Order = {
      ...orders[index],
      digitalCodes: [...(orders[index].digitalCodes || []), ...codes],
      updatedAt: new Date()
    }

    orders[index] = updatedOrder
    SafeStorage.set(STORAGE_KEYS.ORDERS, orders)

    return updatedOrder
  }
}

// =====================================================
// USER STORAGE OPERATIONS
// =====================================================

export class UserStorage {
  /**
   * Get all users
   */
  static getAll(): User[] {
    return SafeStorage.get<User[]>(STORAGE_KEYS.USERS, [])
  }

  /**
   * Get user by email
   */
  static getByEmail(email: string): User | null {
    const users = this.getAll()
    return users.find(u => u.email === email) || null
  }

  /**
   * Create or update user
   */
  static upsert(userData: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): User {
    const users = this.getAll()
    const existingIndex = users.findIndex(u => u.email === userData.email)
    const now = new Date()

    if (existingIndex >= 0) {
      // Update existing user
      const updatedUser: User = {
        ...users[existingIndex],
        ...userData,
        updatedAt: now
      }
      users[existingIndex] = updatedUser
      SafeStorage.set(STORAGE_KEYS.USERS, users)
      return updatedUser
    } else {
      // Create new user
      const newUser: User = {
        ...userData,
        id: generateId('user_'),
        createdAt: now,
        updatedAt: now
      }
      users.push(newUser)
      SafeStorage.set(STORAGE_KEYS.USERS, users)
      return newUser
    }
  }
}

// =====================================================
// SETTINGS STORAGE
// =====================================================

export class SettingsStorage {
  /**
   * Get application settings
   */
  static get(): Record<string, any> {
    return SafeStorage.get(STORAGE_KEYS.SETTINGS, {})
  }

  /**
   * Update settings
   */
  static update(settings: Record<string, any>): void {
    const currentSettings = this.get()
    const updatedSettings = { ...currentSettings, ...settings }
    SafeStorage.set(STORAGE_KEYS.SETTINGS, updatedSettings)
  }

  /**
   * Get specific setting
   */
  static getSetting<T>(key: string, defaultValue: T): T {
    const settings = this.get()
    return settings[key] !== undefined ? settings[key] : defaultValue
  }

  /**
   * Set specific setting
   */
  static setSetting(key: string, value: any): void {
    this.update({ [key]: value })
  }
}

// =====================================================
// DATABASE INITIALIZATION
// =====================================================

/**
 * Initialize the entire localStorage database
 */
export function initializeDatabase(): void {
  console.log('🔄 Initializing localStorage database...')

  // Initialize sample data if needed
  ProductStorage.initializeSampleData()

  console.log('✅ Database initialized successfully')
}

/**
 * Clear all data (for development/testing)
 */
export function clearDatabase(): void {
  console.log('🗑️ Clearing all localStorage data...')

  Object.values(STORAGE_KEYS).forEach(key => {
    SafeStorage.remove(key)
  })

  console.log('✅ Database cleared successfully')
}

/**
 * Export database (for backup/migration)
 */
export function exportDatabase(): Record<string, any> {
  const data: Record<string, any> = {}

  Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
    data[name] = SafeStorage.get(key, null)
  })

  return data
}

/**
 * Import database (for backup/migration)
 */
export function importDatabase(data: Record<string, any>): void {
  Object.entries(STORAGE_KEYS).forEach(([name, key]) => {
    if (data[name] !== undefined) {
      SafeStorage.set(key, data[name])
    }
  })

  console.log('✅ Database imported successfully')
}
