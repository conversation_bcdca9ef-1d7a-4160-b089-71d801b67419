import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	safelist: [
		"bg-pubg-orange/10",
		"bg-pubg-orange/20",
		"bg-pubg-orange/30",
		"bg-pubg-orange/40",
		"bg-pubg-orange/50",
		"bg-pubg-orange/60",
		"bg-pubg-orange/70",
		"bg-pubg-orange/80",
		"bg-pubg-orange/90",
		"text-pubg-orange",
		"border-pubg-orange",
		"hover:bg-pubg-orange/90",
		"bg-pubg-gray/10",
		"bg-pubg-gray/20",
		"bg-pubg-gray/30",
		"text-pubg-gray",
		"border-pubg-gray",
		"hover:bg-pubg-gray/90",
	],
	theme: {
		container: {
			center: true,
			padding: {
				DEFAULT: '1rem',
				sm: '1.5rem',
				lg: '2rem'
			},
			screens: {
				'sm': '640px',
				'md': '768px',
				'lg': '1024px',
				'xl': '1280px',
				'2xl': '1400px'
			}
		},
		screens: {
			'xs': '480px',
			'sm': '640px',
			'md': '768px',
			'lg': '1024px',
			'xl': '1280px',
			'2xl': '1536px',
		},
		extend: {
			fontFamily: {
				cairo: ['Cairo', 'sans-serif'],
			},
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				pubg: {
					orange: 'var(--pubg-orange-color)',
					black: 'var(--pubg-black-color)',
					darkGray: 'var(--pubg-darkGray-color)',
					gray: 'var(--pubg-gray-color)',
					lightGray: 'var(--pubg-lightGray-color)',
				},
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			spacing: {
				'navbar': '76px',
			},
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' },
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' },
				},
				'fade-in': {
					'0%': { opacity: '0' },
					'100%': { opacity: '1' },
				},
				'fade-in-up': {
					'0%': { 
						opacity: '0',
						transform: 'translateY(20px)'
					},
					'100%': {
						opacity: '1',
						transform: 'translateY(0)'
					},
				},
				'float': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-10px)' },
				},
				'float-slow': {
					'0%': { transform: 'translate(0, 0)' },
					'33%': { transform: 'translate(-5px, -5px)' },
					'66%': { transform: 'translate(5px, 5px)' },
					'100%': { transform: 'translate(0, 0)' },
				},
				'float-medium': {
					'0%': { transform: 'translate(0, 0)' },
					'25%': { transform: 'translate(5px, -5px)' },
					'50%': { transform: 'translate(10px, 0)' },
					'75%': { transform: 'translate(5px, 5px)' },
					'100%': { transform: 'translate(0, 0)' },
				},
				'float-fast': {
					'0%': { transform: 'translate(0, 0)' },
					'20%': { transform: 'translate(-7px, 7px)' },
					'40%': { transform: 'translate(7px, 0)' },
					'60%': { transform: 'translate(0, -7px)' },
					'80%': { transform: 'translate(-7px, 0)' },
					'100%': { transform: 'translate(0, 0)' },
				},
				'pulse-soft': {
					'0%, 100%': { opacity: '1' },
					'50%': { opacity: '0.8' },
				},
				'pulse-slow': {
					'0%, 100%': { 
						opacity: '0.8',
						transform: 'scale(1)'
					},
					'50%': { 
						opacity: '0.4',
						transform: 'scale(1.05)'
					},
				},
				'scale': {
					'0%': { transform: 'scale(0.95)' },
					'100%': { transform: 'scale(1)' },
				},
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'fade-in-up': 'fade-in-up 0.5s ease-out',
				'float': 'float 4s infinite ease-in-out',
				'float-slow': 'float-slow 8s infinite ease-in-out',
				'float-medium': 'float-medium 6s infinite ease-in-out',
				'float-fast': 'float-fast 4s infinite ease-in-out',
				'pulse-soft': 'pulse-soft 2s infinite ease-in-out',
				'pulse-slow': 'pulse-slow 3s infinite ease-in-out',
				'scale': 'scale 0.2s ease-out',
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
