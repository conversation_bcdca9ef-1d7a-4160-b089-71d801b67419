"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./components/products/SimpleProductForm.tsx":
/*!***************************************************!*\
  !*** ./components/products/SimpleProductForm.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleProductForm: () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_data_currencies__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/data/currencies */ \"(app-pages-browser)/./lib/data/currencies.ts\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ SimpleProductForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { template, onSubmit, currency, showPricing = true, disabled = false, className = \"\" } = param;\n    _s();\n    const [selectedPackage, setSelectedPackage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [customFieldValues, setCustomFieldValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [fieldErrors, setFieldErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { convertPrice } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrencyConverter)();\n    // Calculate total price\n    const calculateTotalPrice = ()=>{\n        if (!selectedPackage) return 0;\n        return selectedPackage.price * quantity;\n    };\n    // Format price with currency\n    const formatPrice = (price)=>{\n        const convertedPrice = convertPrice(price, \"USD\", currency);\n        return (0,_lib_data_currencies__WEBPACK_IMPORTED_MODULE_6__.formatCurrency)(convertedPrice, currency);\n    };\n    // Handle package selection\n    const handlePackageSelect = (pkg)=>{\n        setSelectedPackage(pkg);\n        setFieldErrors({}) // Clear errors when package changes\n        ;\n    };\n    // Handle custom field changes\n    const handleFieldChange = (fieldName, value)=>{\n        setCustomFieldValues((prev)=>({\n                ...prev,\n                [fieldName]: value\n            }));\n        // Clear error for this field when user starts typing\n        if (fieldErrors[fieldName]) {\n            setFieldErrors((prev)=>({\n                    ...prev,\n                    [fieldName]: \"\"\n                }));\n        }\n    };\n    // Validate form fields\n    const validateFields = ()=>{\n        const errors = {};\n        const activeFields = template.fields.filter((f)=>f.isActive);\n        activeFields.forEach((field)=>{\n            const value = customFieldValues[field.name];\n            if (field.required && (!value || value.toString().trim() === \"\")) {\n                errors[field.name] = \"\".concat(field.label, \" مطلوب\");\n            }\n            if (value && field.type === \"email\") {\n                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                if (!emailRegex.test(value)) {\n                    errors[field.name] = \"يرجى إدخال بريد إلكتروني صحيح\";\n                }\n            }\n            if (value && field.type === \"number\") {\n                if (isNaN(Number(value))) {\n                    errors[field.name] = \"يرجى إدخال رقم صحيح\";\n                }\n            }\n        });\n        return errors;\n    };\n    // Handle form submission\n    const handleSubmit = async ()=>{\n        if (!selectedPackage) {\n            alert(\"يرجى اختيار حزمة\");\n            return;\n        }\n        // Validate all custom fields\n        const errors = validateFields();\n        if (Object.keys(errors).length > 0) {\n            setFieldErrors(errors);\n            return;\n        }\n        try {\n            setIsLoading(true);\n            const formData = {\n                templateId: template.id,\n                selectedPackage,\n                quantity,\n                customFields: customFieldValues,\n                totalPrice: calculateTotalPrice(),\n                currency\n            };\n            await onSubmit(formData);\n        } catch (error) {\n            console.error(\"Error submitting form:\", error);\n            alert(\"حدث خطأ أثناء إرسال الطلب\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Render custom field\n    const renderCustomField = (field)=>{\n        var _field_options;\n        const value = customFieldValues[field.name] || \"\";\n        const error = fieldErrors[field.name];\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                    className: \"block text-sm font-medium text-slate-300\",\n                    children: [\n                        field.label,\n                        field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-400 ml-1\",\n                            children: \"*\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 30\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                field.type === \"universal_input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 11\n                }, this),\n                field.type === \"dropdown\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                            value: \"\",\n                            children: field.placeholder || \"اختر \".concat(field.label)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 13\n                        }, this),\n                        (_field_options = field.options) === null || _field_options === void 0 ? void 0 : _field_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: option.value,\n                                children: option.label\n                            }, option.id, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this))\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 162,\n                    columnNumber: 11\n                }, this),\n                field.type === \"text\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"text\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this),\n                field.type === \"email\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"email\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this),\n                field.type === \"number\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                    type: \"number\",\n                    value: value,\n                    onChange: (e)=>handleFieldChange(field.name, e.target.value),\n                    placeholder: field.placeholder,\n                    disabled: disabled,\n                    className: \"w-full bg-slate-700 border rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(error ? \"border-red-500\" : \"border-slate-600\")\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 209,\n                    columnNumber: 11\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-400 text-sm\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, field.id, true, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate values for rendering\n    const totalPrice = calculateTotalPrice();\n    const visibleFields = template.fields.filter((f)=>f.isActive);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                \"اختر الحزمة\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4\",\n                            children: template.packages.map((pkg)=>{\n                                var _pkg_digitalCodes;\n                                const enhancedPkg = (0,_lib_types__WEBPACK_IMPORTED_MODULE_5__.enhancePackageWithDiscountInfo)(pkg);\n                                const availableCodes = ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.filter((code)=>!code.used).length) || 0;\n                                // Enhanced availability logic for different product types\n                                const isAvailable = (()=>{\n                                    if (!pkg.isActive) return false;\n                                    // Digital products with codes\n                                    if (pkg.digitalCodes && pkg.digitalCodes.length > 0) {\n                                        return availableCodes > 0;\n                                    }\n                                    // Products with manual quantity limits\n                                    if (pkg.quantityLimit !== undefined && pkg.quantityLimit !== null) {\n                                        return pkg.quantityLimit > 0;\n                                    }\n                                    // Unlimited digital products/services (no codes, no limits)\n                                    return true;\n                                })();\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    onClick: ()=>!disabled && isAvailable && handlePackageSelect(pkg),\n                                    className: \"relative p-4 rounded-lg border-2 cursor-pointer transition-all \".concat((selectedPackage === null || selectedPackage === void 0 ? void 0 : selectedPackage.id) === pkg.id ? \"border-blue-500 bg-blue-500/10\" : isAvailable ? \"border-slate-600 bg-slate-700/30 hover:border-slate-500\" : \"border-red-600/50 bg-red-900/20\", \" \").concat(disabled || !isAvailable ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white\",\n                                                        children: pkg.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-300 text-sm\",\n                                                        children: pkg.amount\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm mt-1\",\n                                                        children: pkg.description\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    pkg.digitalCodes && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs mt-1 \".concat(availableCodes === 0 ? \"text-red-400\" : availableCodes < 5 ? \"text-yellow-400\" : \"text-green-400\"),\n                                                        children: availableCodes === 0 ? \"نفدت الكمية\" : availableCodes < 5 ? \"متبقي \".concat(availableCodes) : \"متوفر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    enhancedPkg.hasDiscount && pkg.originalPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-400 text-sm line-through\",\n                                                        children: formatPrice(pkg.originalPrice)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 302,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-bold\",\n                                                        children: formatPrice(pkg.price)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    enhancedPkg.hasDiscount && enhancedPkg.discountPercentage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        variant: \"destructive\",\n                                                        className: \"mt-1\",\n                                                        children: [\n                                                            \"خصم \",\n                                                            enhancedPkg.discountPercentage,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 310,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 19\n                                    }, this)\n                                }, pkg.id, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 243,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 235,\n                columnNumber: 7\n            }, this),\n            visibleFields.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-white\",\n                            children: \"معلومات إضافية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 327,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 326,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: visibleFields.map(renderCustomField)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                            lineNumber: 330,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 325,\n                columnNumber: 9\n            }, this),\n            selectedPackage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSubmit,\n                        disabled: disabled || isLoading,\n                        className: \"w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-bold py-4 text-lg\",\n                        size: \"lg\",\n                        children: isLoading ? \"جاري المعالجة...\" : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                template.processingType === \"instant\" ? \"اشتري الآن\" : \"أضف للسلة\",\n                                showPricing && \" - \".concat(formatPrice(totalPrice))\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                    lineNumber: 340,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\SimpleProductForm.tsx\",\n        lineNumber: 233,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"0g13qWIc+cKc3tL01+RBo0otGh8=\", false, function() {\n    return [\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_7__.useCurrencyConverter\n    ];\n});\n_c = SimpleProductForm;\nvar _c;\n$RefreshReg$(_c, \"SimpleProductForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/SimpleProductForm.tsx\n"));

/***/ })

});