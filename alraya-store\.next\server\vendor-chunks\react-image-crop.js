"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-image-crop";
exports.ids = ["vendor-chunks/react-image-crop"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-image-crop/dist/ReactCrop.css":
/*!**********************************************************!*\
  !*** ./node_modules/react-image-crop/dist/ReactCrop.css ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6579efb6794e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtaW1hZ2UtY3JvcC9kaXN0L1JlYWN0Q3JvcC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxWUy1wcm9qZWN0c1xcdHJ5XFxhbHJheWEtc3RvcmVcXG5vZGVfbW9kdWxlc1xccmVhY3QtaW1hZ2UtY3JvcFxcZGlzdFxcUmVhY3RDcm9wLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY1NzllZmI2Nzk0ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-image-crop/dist/ReactCrop.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-image-crop/dist/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/react-image-crop/dist/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Component: () => (/* binding */ S),\n/* harmony export */   ReactCrop: () => (/* binding */ S),\n/* harmony export */   areCropsEqual: () => (/* binding */ X),\n/* harmony export */   centerCrop: () => (/* binding */ L),\n/* harmony export */   clamp: () => (/* binding */ b),\n/* harmony export */   cls: () => (/* binding */ H),\n/* harmony export */   containCrop: () => (/* binding */ k),\n/* harmony export */   convertToPercentCrop: () => (/* binding */ v),\n/* harmony export */   convertToPixelCrop: () => (/* binding */ D),\n/* harmony export */   \"default\": () => (/* binding */ S),\n/* harmony export */   defaultCrop: () => (/* binding */ E),\n/* harmony export */   makeAspectCrop: () => (/* binding */ B),\n/* harmony export */   nudgeCrop: () => (/* binding */ I)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\nvar _ = Object.defineProperty;\nvar $ = (a, h, e) => h in a ? _(a, h, { enumerable: !0, configurable: !0, writable: !0, value: e }) : a[h] = e;\nvar m = (a, h, e) => $(a, typeof h != \"symbol\" ? h + \"\" : h, e);\n\nconst E = {\n  x: 0,\n  y: 0,\n  width: 0,\n  height: 0,\n  unit: \"px\"\n}, b = (a, h, e) => Math.min(Math.max(a, h), e), H = (...a) => a.filter((h) => h && typeof h == \"string\").join(\" \"), X = (a, h) => a === h || a.width === h.width && a.height === h.height && a.x === h.x && a.y === h.y && a.unit === h.unit;\nfunction B(a, h, e, n) {\n  const t = D(a, e, n);\n  return a.width && (t.height = t.width / h), a.height && (t.width = t.height * h), t.y + t.height > n && (t.height = n - t.y, t.width = t.height * h), t.x + t.width > e && (t.width = e - t.x, t.height = t.width / h), a.unit === \"%\" ? v(t, e, n) : t;\n}\nfunction L(a, h, e) {\n  const n = D(a, h, e);\n  return n.x = (h - n.width) / 2, n.y = (e - n.height) / 2, a.unit === \"%\" ? v(n, h, e) : n;\n}\nfunction v(a, h, e) {\n  return a.unit === \"%\" ? { ...E, ...a, unit: \"%\" } : {\n    unit: \"%\",\n    x: a.x ? a.x / h * 100 : 0,\n    y: a.y ? a.y / e * 100 : 0,\n    width: a.width ? a.width / h * 100 : 0,\n    height: a.height ? a.height / e * 100 : 0\n  };\n}\nfunction D(a, h, e) {\n  return a.unit ? a.unit === \"px\" ? { ...E, ...a, unit: \"px\" } : {\n    unit: \"px\",\n    x: a.x ? a.x * h / 100 : 0,\n    y: a.y ? a.y * e / 100 : 0,\n    width: a.width ? a.width * h / 100 : 0,\n    height: a.height ? a.height * e / 100 : 0\n  } : { ...E, ...a, unit: \"px\" };\n}\nfunction k(a, h, e, n, t, d = 0, r = 0, o = n, w = t) {\n  const i = { ...a };\n  let s = Math.min(d, n), c = Math.min(r, t), g = Math.min(o, n), p = Math.min(w, t);\n  h && (h > 1 ? (s = r ? r * h : s, c = s / h, g = o * h) : (c = d ? d / h : c, s = c * h, p = w / h)), i.y < 0 && (i.height = Math.max(i.height + i.y, c), i.y = 0), i.x < 0 && (i.width = Math.max(i.width + i.x, s), i.x = 0);\n  const l = n - (i.x + i.width);\n  l < 0 && (i.x = Math.min(i.x, n - s), i.width += l);\n  const C = t - (i.y + i.height);\n  if (C < 0 && (i.y = Math.min(i.y, t - c), i.height += C), i.width < s && ((e === \"sw\" || e == \"nw\") && (i.x -= s - i.width), i.width = s), i.height < c && ((e === \"nw\" || e == \"ne\") && (i.y -= c - i.height), i.height = c), i.width > g && ((e === \"sw\" || e == \"nw\") && (i.x -= g - i.width), i.width = g), i.height > p && ((e === \"nw\" || e == \"ne\") && (i.y -= p - i.height), i.height = p), h) {\n    const y = i.width / i.height;\n    if (y < h) {\n      const f = Math.max(i.width / h, c);\n      (e === \"nw\" || e == \"ne\") && (i.y -= f - i.height), i.height = f;\n    } else if (y > h) {\n      const f = Math.max(i.height * h, s);\n      (e === \"sw\" || e == \"nw\") && (i.x -= f - i.width), i.width = f;\n    }\n  }\n  return i;\n}\nfunction I(a, h, e, n) {\n  const t = { ...a };\n  return h === \"ArrowLeft\" ? n === \"nw\" ? (t.x -= e, t.y -= e, t.width += e, t.height += e) : n === \"w\" ? (t.x -= e, t.width += e) : n === \"sw\" ? (t.x -= e, t.width += e, t.height += e) : n === \"ne\" ? (t.y += e, t.width -= e, t.height -= e) : n === \"e\" ? t.width -= e : n === \"se\" && (t.width -= e, t.height -= e) : h === \"ArrowRight\" && (n === \"nw\" ? (t.x += e, t.y += e, t.width -= e, t.height -= e) : n === \"w\" ? (t.x += e, t.width -= e) : n === \"sw\" ? (t.x += e, t.width -= e, t.height -= e) : n === \"ne\" ? (t.y -= e, t.width += e, t.height += e) : n === \"e\" ? t.width += e : n === \"se\" && (t.width += e, t.height += e)), h === \"ArrowUp\" ? n === \"nw\" ? (t.x -= e, t.y -= e, t.width += e, t.height += e) : n === \"n\" ? (t.y -= e, t.height += e) : n === \"ne\" ? (t.y -= e, t.width += e, t.height += e) : n === \"sw\" ? (t.x += e, t.width -= e, t.height -= e) : n === \"s\" ? t.height -= e : n === \"se\" && (t.width -= e, t.height -= e) : h === \"ArrowDown\" && (n === \"nw\" ? (t.x += e, t.y += e, t.width -= e, t.height -= e) : n === \"n\" ? (t.y += e, t.height -= e) : n === \"ne\" ? (t.y += e, t.width -= e, t.height -= e) : n === \"sw\" ? (t.x -= e, t.width += e, t.height += e) : n === \"s\" ? t.height += e : n === \"se\" && (t.width += e, t.height += e)), t;\n}\nconst M = { capture: !0, passive: !1 };\nlet N = 0;\nconst x = class x extends react__WEBPACK_IMPORTED_MODULE_0__.PureComponent {\n  constructor() {\n    super(...arguments);\n    m(this, \"docMoveBound\", !1);\n    m(this, \"mouseDownOnCrop\", !1);\n    m(this, \"dragStarted\", !1);\n    m(this, \"evData\", {\n      startClientX: 0,\n      startClientY: 0,\n      startCropX: 0,\n      startCropY: 0,\n      clientX: 0,\n      clientY: 0,\n      isResize: !0\n    });\n    m(this, \"componentRef\", (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)());\n    m(this, \"mediaRef\", (0,react__WEBPACK_IMPORTED_MODULE_0__.createRef)());\n    m(this, \"resizeObserver\");\n    m(this, \"initChangeCalled\", !1);\n    m(this, \"instanceId\", `rc-${N++}`);\n    m(this, \"state\", {\n      cropIsActive: !1,\n      newCropIsBeingDrawn: !1\n    });\n    m(this, \"onCropPointerDown\", (e) => {\n      const { crop: n, disabled: t } = this.props, d = this.getBox();\n      if (!n)\n        return;\n      const r = D(n, d.width, d.height);\n      if (t)\n        return;\n      e.cancelable && e.preventDefault(), this.bindDocMove(), this.componentRef.current.focus({ preventScroll: !0 });\n      const o = e.target.dataset.ord, w = !!o;\n      let i = e.clientX, s = e.clientY, c = r.x, g = r.y;\n      if (o) {\n        const p = e.clientX - d.x, l = e.clientY - d.y;\n        let C = 0, y = 0;\n        o === \"ne\" || o == \"e\" ? (C = p - (r.x + r.width), y = l - r.y, c = r.x, g = r.y + r.height) : o === \"se\" || o === \"s\" ? (C = p - (r.x + r.width), y = l - (r.y + r.height), c = r.x, g = r.y) : o === \"sw\" || o == \"w\" ? (C = p - r.x, y = l - (r.y + r.height), c = r.x + r.width, g = r.y) : (o === \"nw\" || o == \"n\") && (C = p - r.x, y = l - r.y, c = r.x + r.width, g = r.y + r.height), i = c + d.x + C, s = g + d.y + y;\n      }\n      this.evData = {\n        startClientX: i,\n        startClientY: s,\n        startCropX: c,\n        startCropY: g,\n        clientX: e.clientX,\n        clientY: e.clientY,\n        isResize: w,\n        ord: o\n      }, this.mouseDownOnCrop = !0, this.setState({ cropIsActive: !0 });\n    });\n    m(this, \"onComponentPointerDown\", (e) => {\n      const { crop: n, disabled: t, locked: d, keepSelection: r, onChange: o } = this.props, w = this.getBox();\n      if (t || d || r && n)\n        return;\n      e.cancelable && e.preventDefault(), this.bindDocMove(), this.componentRef.current.focus({ preventScroll: !0 });\n      const i = e.clientX - w.x, s = e.clientY - w.y, c = {\n        unit: \"px\",\n        x: i,\n        y: s,\n        width: 0,\n        height: 0\n      };\n      this.evData = {\n        startClientX: e.clientX,\n        startClientY: e.clientY,\n        startCropX: i,\n        startCropY: s,\n        clientX: e.clientX,\n        clientY: e.clientY,\n        isResize: !0\n      }, this.mouseDownOnCrop = !0, o(D(c, w.width, w.height), v(c, w.width, w.height)), this.setState({ cropIsActive: !0, newCropIsBeingDrawn: !0 });\n    });\n    m(this, \"onDocPointerMove\", (e) => {\n      const { crop: n, disabled: t, onChange: d, onDragStart: r } = this.props, o = this.getBox();\n      if (t || !n || !this.mouseDownOnCrop)\n        return;\n      e.cancelable && e.preventDefault(), this.dragStarted || (this.dragStarted = !0, r && r(e));\n      const { evData: w } = this;\n      w.clientX = e.clientX, w.clientY = e.clientY;\n      let i;\n      w.isResize ? i = this.resizeCrop() : i = this.dragCrop(), X(n, i) || d(\n        D(i, o.width, o.height),\n        v(i, o.width, o.height)\n      );\n    });\n    m(this, \"onComponentKeyDown\", (e) => {\n      const { crop: n, disabled: t, onChange: d, onComplete: r } = this.props;\n      if (t)\n        return;\n      const o = e.key;\n      let w = !1;\n      if (!n)\n        return;\n      const i = this.getBox(), s = this.makePixelCrop(i), g = (navigator.platform.match(\"Mac\") ? e.metaKey : e.ctrlKey) ? x.nudgeStepLarge : e.shiftKey ? x.nudgeStepMedium : x.nudgeStep;\n      if (o === \"ArrowLeft\" ? (s.x -= g, w = !0) : o === \"ArrowRight\" ? (s.x += g, w = !0) : o === \"ArrowUp\" ? (s.y -= g, w = !0) : o === \"ArrowDown\" && (s.y += g, w = !0), w) {\n        e.cancelable && e.preventDefault(), s.x = b(s.x, 0, i.width - s.width), s.y = b(s.y, 0, i.height - s.height);\n        const p = D(s, i.width, i.height), l = v(s, i.width, i.height);\n        d(p, l), r && r(p, l);\n      }\n    });\n    m(this, \"onHandlerKeyDown\", (e, n) => {\n      const {\n        aspect: t = 0,\n        crop: d,\n        disabled: r,\n        minWidth: o = 0,\n        minHeight: w = 0,\n        maxWidth: i,\n        maxHeight: s,\n        onChange: c,\n        onComplete: g\n      } = this.props, p = this.getBox();\n      if (r || !d)\n        return;\n      if (e.key === \"ArrowUp\" || e.key === \"ArrowDown\" || e.key === \"ArrowLeft\" || e.key === \"ArrowRight\")\n        e.stopPropagation(), e.preventDefault();\n      else\n        return;\n      const C = (navigator.platform.match(\"Mac\") ? e.metaKey : e.ctrlKey) ? x.nudgeStepLarge : e.shiftKey ? x.nudgeStepMedium : x.nudgeStep, y = D(d, p.width, p.height), f = I(y, e.key, C, n), R = k(\n        f,\n        t,\n        n,\n        p.width,\n        p.height,\n        o,\n        w,\n        i,\n        s\n      );\n      if (!X(d, R)) {\n        const Y = v(R, p.width, p.height);\n        c(R, Y), g && g(R, Y);\n      }\n    });\n    m(this, \"onDocPointerDone\", (e) => {\n      const { crop: n, disabled: t, onComplete: d, onDragEnd: r } = this.props, o = this.getBox();\n      this.unbindDocMove(), !(t || !n) && this.mouseDownOnCrop && (this.mouseDownOnCrop = !1, this.dragStarted = !1, r && r(e), d && d(D(n, o.width, o.height), v(n, o.width, o.height)), this.setState({ cropIsActive: !1, newCropIsBeingDrawn: !1 }));\n    });\n    m(this, \"onDragFocus\", () => {\n      var e;\n      (e = this.componentRef.current) == null || e.scrollTo(0, 0);\n    });\n  }\n  get document() {\n    return document;\n  }\n  // We unfortunately get the bounding box every time as x+y changes\n  // due to scrolling.\n  getBox() {\n    const e = this.mediaRef.current;\n    if (!e)\n      return { x: 0, y: 0, width: 0, height: 0 };\n    const { x: n, y: t, width: d, height: r } = e.getBoundingClientRect();\n    return { x: n, y: t, width: d, height: r };\n  }\n  componentDidUpdate(e) {\n    const { crop: n, onComplete: t } = this.props;\n    if (t && !e.crop && n) {\n      const { width: d, height: r } = this.getBox();\n      d && r && t(D(n, d, r), v(n, d, r));\n    }\n  }\n  componentWillUnmount() {\n    this.resizeObserver && this.resizeObserver.disconnect(), this.unbindDocMove();\n  }\n  bindDocMove() {\n    this.docMoveBound || (this.document.addEventListener(\"pointermove\", this.onDocPointerMove, M), this.document.addEventListener(\"pointerup\", this.onDocPointerDone, M), this.document.addEventListener(\"pointercancel\", this.onDocPointerDone, M), this.docMoveBound = !0);\n  }\n  unbindDocMove() {\n    this.docMoveBound && (this.document.removeEventListener(\"pointermove\", this.onDocPointerMove, M), this.document.removeEventListener(\"pointerup\", this.onDocPointerDone, M), this.document.removeEventListener(\"pointercancel\", this.onDocPointerDone, M), this.docMoveBound = !1);\n  }\n  getCropStyle() {\n    const { crop: e } = this.props;\n    if (e)\n      return {\n        top: `${e.y}${e.unit}`,\n        left: `${e.x}${e.unit}`,\n        width: `${e.width}${e.unit}`,\n        height: `${e.height}${e.unit}`\n      };\n  }\n  dragCrop() {\n    const { evData: e } = this, n = this.getBox(), t = this.makePixelCrop(n), d = e.clientX - e.startClientX, r = e.clientY - e.startClientY;\n    return t.x = b(e.startCropX + d, 0, n.width - t.width), t.y = b(e.startCropY + r, 0, n.height - t.height), t;\n  }\n  getPointRegion(e, n, t, d) {\n    const { evData: r } = this, o = r.clientX - e.x, w = r.clientY - e.y;\n    let i;\n    d && n ? i = n === \"nw\" || n === \"n\" || n === \"ne\" : i = w < r.startCropY;\n    let s;\n    return t && n ? s = n === \"nw\" || n === \"w\" || n === \"sw\" : s = o < r.startCropX, s ? i ? \"nw\" : \"sw\" : i ? \"ne\" : \"se\";\n  }\n  resolveMinDimensions(e, n, t = 0, d = 0) {\n    const r = Math.min(t, e.width), o = Math.min(d, e.height);\n    return !n || !r && !o ? [r, o] : n > 1 ? r ? [r, r / n] : [o * n, o] : o ? [o * n, o] : [r, r / n];\n  }\n  resizeCrop() {\n    const { evData: e } = this, { aspect: n = 0, maxWidth: t, maxHeight: d } = this.props, r = this.getBox(), [o, w] = this.resolveMinDimensions(r, n, this.props.minWidth, this.props.minHeight);\n    let i = this.makePixelCrop(r);\n    const s = this.getPointRegion(r, e.ord, o, w), c = e.ord || s;\n    let g = e.clientX - e.startClientX, p = e.clientY - e.startClientY;\n    (o && c === \"nw\" || c === \"w\" || c === \"sw\") && (g = Math.min(g, -o)), (w && c === \"nw\" || c === \"n\" || c === \"ne\") && (p = Math.min(p, -w));\n    const l = {\n      unit: \"px\",\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n    s === \"ne\" ? (l.x = e.startCropX, l.width = g, n ? (l.height = l.width / n, l.y = e.startCropY - l.height) : (l.height = Math.abs(p), l.y = e.startCropY - l.height)) : s === \"se\" ? (l.x = e.startCropX, l.y = e.startCropY, l.width = g, n ? l.height = l.width / n : l.height = p) : s === \"sw\" ? (l.x = e.startCropX + g, l.y = e.startCropY, l.width = Math.abs(g), n ? l.height = l.width / n : l.height = p) : s === \"nw\" && (l.x = e.startCropX + g, l.width = Math.abs(g), n ? (l.height = l.width / n, l.y = e.startCropY - l.height) : (l.height = Math.abs(p), l.y = e.startCropY + p));\n    const C = k(\n      l,\n      n,\n      s,\n      r.width,\n      r.height,\n      o,\n      w,\n      t,\n      d\n    );\n    return n || x.xyOrds.indexOf(c) > -1 ? i = C : x.xOrds.indexOf(c) > -1 ? (i.x = C.x, i.width = C.width) : x.yOrds.indexOf(c) > -1 && (i.y = C.y, i.height = C.height), i.x = b(i.x, 0, r.width - i.width), i.y = b(i.y, 0, r.height - i.height), i;\n  }\n  renderCropSelection() {\n    const {\n      ariaLabels: e = x.defaultProps.ariaLabels,\n      disabled: n,\n      locked: t,\n      renderSelectionAddon: d,\n      ruleOfThirds: r,\n      crop: o\n    } = this.props, w = this.getCropStyle();\n    if (o)\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n        \"div\",\n        {\n          style: w,\n          className: \"ReactCrop__crop-selection\",\n          onPointerDown: this.onCropPointerDown,\n          \"aria-label\": e.cropArea,\n          tabIndex: 0,\n          onKeyDown: this.onComponentKeyDown,\n          role: \"group\"\n        },\n        !n && !t && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__drag-elements\", onFocus: this.onDragFocus }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-n\", \"data-ord\": \"n\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-e\", \"data-ord\": \"e\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-s\", \"data-ord\": \"s\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__drag-bar ord-w\", \"data-ord\": \"w\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-nw\",\n            \"data-ord\": \"nw\",\n            tabIndex: 0,\n            \"aria-label\": e.nwDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"nw\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-n\",\n            \"data-ord\": \"n\",\n            tabIndex: 0,\n            \"aria-label\": e.nDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"n\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-ne\",\n            \"data-ord\": \"ne\",\n            tabIndex: 0,\n            \"aria-label\": e.neDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"ne\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-e\",\n            \"data-ord\": \"e\",\n            tabIndex: 0,\n            \"aria-label\": e.eDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"e\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-se\",\n            \"data-ord\": \"se\",\n            tabIndex: 0,\n            \"aria-label\": e.seDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"se\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-s\",\n            \"data-ord\": \"s\",\n            tabIndex: 0,\n            \"aria-label\": e.sDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"s\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-sw\",\n            \"data-ord\": \"sw\",\n            tabIndex: 0,\n            \"aria-label\": e.swDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"sw\"),\n            role: \"button\"\n          }\n        ), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          \"div\",\n          {\n            className: \"ReactCrop__drag-handle ord-w\",\n            \"data-ord\": \"w\",\n            tabIndex: 0,\n            \"aria-label\": e.wDragHandle,\n            onKeyDown: (i) => this.onHandlerKeyDown(i, \"w\"),\n            role: \"button\"\n          }\n        )),\n        d && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__selection-addon\", onPointerDown: (i) => i.stopPropagation() }, d(this.state)),\n        r && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__rule-of-thirds-hz\" }), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { className: \"ReactCrop__rule-of-thirds-vt\" }))\n      );\n  }\n  makePixelCrop(e) {\n    const n = { ...E, ...this.props.crop || {} };\n    return D(n, e.width, e.height);\n  }\n  render() {\n    const { aspect: e, children: n, circularCrop: t, className: d, crop: r, disabled: o, locked: w, style: i, ruleOfThirds: s } = this.props, { cropIsActive: c, newCropIsBeingDrawn: g } = this.state, p = r ? this.renderCropSelection() : null, l = H(\n      \"ReactCrop\",\n      d,\n      c && \"ReactCrop--active\",\n      o && \"ReactCrop--disabled\",\n      w && \"ReactCrop--locked\",\n      g && \"ReactCrop--new-crop\",\n      r && e && \"ReactCrop--fixed-aspect\",\n      r && t && \"ReactCrop--circular-crop\",\n      r && s && \"ReactCrop--rule-of-thirds\",\n      !this.dragStarted && r && !r.width && !r.height && \"ReactCrop--invisible-crop\",\n      t && \"ReactCrop--no-animate\"\n    );\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: this.componentRef, className: l, style: i }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", { ref: this.mediaRef, className: \"ReactCrop__child-wrapper\", onPointerDown: this.onComponentPointerDown }, n), r ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", { className: \"ReactCrop__crop-mask\", width: \"100%\", height: \"100%\" }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"defs\", null, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"mask\", { id: `hole-${this.instanceId}` }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"rect\", { width: \"100%\", height: \"100%\", fill: \"white\" }), t ? /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"ellipse\",\n      {\n        cx: `${r.x + r.width / 2}${r.unit}`,\n        cy: `${r.y + r.height / 2}${r.unit}`,\n        rx: `${r.width / 2}${r.unit}`,\n        ry: `${r.height / 2}${r.unit}`,\n        fill: \"black\"\n      }\n    ) : /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      \"rect\",\n      {\n        x: `${r.x}${r.unit}`,\n        y: `${r.y}${r.unit}`,\n        width: `${r.width}${r.unit}`,\n        height: `${r.height}${r.unit}`,\n        fill: \"black\"\n      }\n    ))), /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"rect\", { fill: \"black\", fillOpacity: 0.5, width: \"100%\", height: \"100%\", mask: `url(#hole-${this.instanceId})` })) : void 0, p);\n  }\n};\nm(x, \"xOrds\", [\"e\", \"w\"]), m(x, \"yOrds\", [\"n\", \"s\"]), m(x, \"xyOrds\", [\"nw\", \"ne\", \"se\", \"sw\"]), m(x, \"nudgeStep\", 1), m(x, \"nudgeStepMedium\", 10), m(x, \"nudgeStepLarge\", 100), m(x, \"defaultProps\", {\n  ariaLabels: {\n    cropArea: \"Use the arrow keys to move the crop selection area\",\n    nwDragHandle: \"Use the arrow keys to move the north west drag handle to change the crop selection area\",\n    nDragHandle: \"Use the up and down arrow keys to move the north drag handle to change the crop selection area\",\n    neDragHandle: \"Use the arrow keys to move the north east drag handle to change the crop selection area\",\n    eDragHandle: \"Use the up and down arrow keys to move the east drag handle to change the crop selection area\",\n    seDragHandle: \"Use the arrow keys to move the south east drag handle to change the crop selection area\",\n    sDragHandle: \"Use the up and down arrow keys to move the south drag handle to change the crop selection area\",\n    swDragHandle: \"Use the arrow keys to move the south west drag handle to change the crop selection area\",\n    wDragHandle: \"Use the up and down arrow keys to move the west drag handle to change the crop selection area\"\n  }\n});\nlet S = x;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-image-crop/dist/index.js\n");

/***/ })

};
;