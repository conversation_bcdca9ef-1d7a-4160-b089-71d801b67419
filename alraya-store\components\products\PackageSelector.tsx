"use client"

import React from "react"
import { Badge } from "@/components/ui/badge"
import { ProductPackage, Currency, enhancePackageWithDiscountInfo } from "@/lib/types"
import { formatCurrency } from "@/lib/data/currencies"
import { useCurrencyConverter } from "@/contexts/CurrencyContext"

interface PackageSelectorProps {
  packages: ProductPackage[]
  selectedPackage?: ProductPackage
  onPackageSelect: (pkg: ProductPackage) => void
  currency: Currency
  showPricing?: boolean
  disabled?: boolean
  className?: string
}

export function PackageSelector({
  packages,
  selectedPackage,
  onPackageSelect,
  currency,
  showPricing = true,
  disabled = false,
  className = ""
}: PackageSelectorProps) {
  // ## TODO: Implement currency conversion with Supabase exchange rates
  const { convertPrice } = useCurrencyConverter()

  /**
   * Convert package price to selected currency
   */
  const getConvertedPrice = (priceUSD: number): number => {
    // ## TODO: Replace with real currency conversion
    return convertPrice(priceUSD, "USD", currency)
  }

  /**
   * Format price in selected currency
   */
  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = getConvertedPrice(priceUSD)
    return formatCurrency(convertedPrice, currency)
  }

  /**
   * Check if package is available (has digital codes if digital product)
   */
  const isPackageAvailable = (pkg: ProductPackage): boolean => {
    // ## TODO: Check digital code availability from Supabase
    if (pkg.digitalCodes) {
      return pkg.digitalCodes.length > 0
    }
    return pkg.isActive
  }

  /**
   * Get package availability status
   */
  const getAvailabilityStatus = (pkg: ProductPackage): string => {
    if (!pkg.isActive) return "غير متاح"
    
    // ## TODO: Check real availability from database
    if (pkg.digitalCodes) {
      const availableCount = pkg.digitalCodes.length
      if (availableCount === 0) return "نفدت الكمية"
      if (availableCount < 5) return `متبقي ${availableCount}`
    }
    
    return "متاح"
  }

  // Filter active packages
  const activePackages = packages.filter(pkg => pkg.isActive)

  if (activePackages.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-slate-400 mb-2">لا توجد حزم متاحة حالياً</div>
        <div className="text-slate-500 text-sm">
          ## TODO: تحميل الحزم من قاعدة البيانات
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
        {activePackages.map((pkg) => {
          const isSelected = selectedPackage?.id === pkg.id
          const isAvailable = isPackageAvailable(pkg)
          const availabilityStatus = getAvailabilityStatus(pkg)
          
          return (
            <button
              key={pkg.id}
              onClick={() => !disabled && isAvailable && onPackageSelect(pkg)}
              disabled={disabled || !isAvailable}
              className={`relative p-4 rounded-lg border-2 transition-all duration-300 text-center ${
                isSelected
                  ? 'border-yellow-400 bg-yellow-400/10 shadow-lg'
                  : isAvailable
                  ? 'border-slate-600 bg-slate-700/30 hover:border-slate-500 hover:bg-slate-700/50'
                  : 'border-slate-700 bg-slate-800/30 opacity-50 cursor-not-allowed'
              }`}
            >
              {/* Popular Badge */}
              {pkg.popular && isAvailable && (
                <Badge className="absolute -top-2 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs">
                  الأشهر
                </Badge>
              )}
              
              {/* Package Name */}
              <div className="text-white font-medium text-sm mb-1">
                {pkg.name}
              </div>
              
              {/* Package Amount */}
              <div className="text-slate-400 text-xs mb-2">
                {pkg.amount}
              </div>
              
              {/* Pricing */}
              {showPricing && (
                <div className="space-y-1">
                  <div className="text-yellow-400 font-bold text-sm">
                    {formatPrice(pkg.price)}
                  </div>
                  
                  {/* Original Price and Discount */}
                  {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                    <div className="flex items-center justify-center gap-1">
                      <span className="text-slate-500 line-through text-xs">
                        {formatPrice(pkg.originalPrice)}
                      </span>
                      {pkg.discount && (
                        <Badge className="bg-red-500/20 text-red-400 text-xs">
                          -{pkg.discount}%
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              )}
              
              {/* Package Description */}
              {pkg.description && (
                <div className="text-slate-400 text-xs mt-2">
                  {pkg.description}
                </div>
              )}
              
              {/* Availability Status */}
              {!isAvailable && (
                <div className="mt-2">
                  <Badge variant="secondary" className="text-xs bg-red-500/20 text-red-400">
                    {availabilityStatus}
                  </Badge>
                </div>
              )}
              
              {/* Low Stock Warning */}
              {isAvailable && pkg.digitalCodes && pkg.digitalCodes.length < 5 && pkg.digitalCodes.length > 0 && (
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs border-orange-500 text-orange-400">
                    {availabilityStatus}
                  </Badge>
                </div>
              )}
              
              {/* Selected Indicator */}
              {isSelected && (
                <div className="absolute top-2 right-2">
                  <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                </div>
              )}
            </button>
          )
        })}
      </div>
      
      {/* Package Selection Info */}
      {selectedPackage && (
        <div className="bg-slate-800/50 border border-slate-700 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-white font-medium">{selectedPackage.name}</h4>
              <p className="text-slate-400 text-sm">{selectedPackage.amount}</p>
            </div>
            {showPricing && (
              <div className="text-right">
                <div className="text-yellow-400 font-bold">
                  {formatPrice(selectedPackage.price)}
                </div>
                {selectedPackage.originalPrice && selectedPackage.originalPrice > selectedPackage.price && (
                  <div className="text-slate-500 text-sm line-through">
                    {formatPrice(selectedPackage.originalPrice)}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {selectedPackage.description && (
            <p className="text-slate-400 text-sm mt-2">{selectedPackage.description}</p>
          )}
        </div>
      )}
      
      {/* Currency Note */}
      {showPricing && currency !== "USD" && (
        <div className="text-xs text-slate-500 text-center">
          الأسعار محولة من الدولار الأمريكي • ## TODO: أسعار الصرف من Supabase
        </div>
      )}
    </div>
  )
}

/**
 * Compact package selector for smaller spaces
 */
export function CompactPackageSelector({
  packages,
  selectedPackage,
  onPackageSelect,
  currency,
  disabled = false
}: Omit<PackageSelectorProps, 'showPricing' | 'className'>) {
  const { convertPrice } = useCurrencyConverter()

  const getConvertedPrice = (priceUSD: number): number => {
    return convertPrice(priceUSD, "USD", currency)
  }

  const formatPrice = (priceUSD: number): string => {
    const convertedPrice = getConvertedPrice(priceUSD)
    return formatCurrency(convertedPrice, currency)
  }

  const activePackages = packages.filter(pkg => pkg.isActive)

  return (
    <div className="space-y-2">
      {activePackages.map((pkg) => {
        const isSelected = selectedPackage?.id === pkg.id
        
        return (
          <button
            key={pkg.id}
            onClick={() => !disabled && onPackageSelect(pkg)}
            disabled={disabled}
            className={`w-full p-3 rounded-lg border transition-all duration-200 text-left ${
              isSelected
                ? 'border-yellow-400 bg-yellow-400/10'
                : 'border-slate-600 bg-slate-700/30 hover:border-slate-500'
            }`}
          >
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium text-sm">{pkg.name}</div>
                <div className="text-slate-400 text-xs">{pkg.amount}</div>
              </div>
              <div className="text-right">
                <div className="text-yellow-400 font-bold text-sm">
                  {formatPrice(pkg.price)}
                </div>
                {pkg.originalPrice && pkg.originalPrice > pkg.price && (
                  <div className="text-slate-500 text-xs line-through">
                    {formatPrice(pkg.originalPrice)}
                  </div>
                )}
              </div>
            </div>
          </button>
        )
      })}
    </div>
  )
}
