"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_services_orderService_ts"],{

/***/ "(app-pages-browser)/./lib/data/defaultProductTemplates.ts":
/*!*********************************************!*\
  !*** ./lib/data/defaultProductTemplates.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTemplates: () => (/* binding */ defaultTemplates),\n/* harmony export */   deleteProductTemplate: () => (/* binding */ deleteProductTemplate),\n/* harmony export */   forceReinitializeTemplates: () => (/* binding */ forceReinitializeTemplates),\n/* harmony export */   freeFireTemplate: () => (/* binding */ freeFireTemplate),\n/* harmony export */   googlePlayTemplate: () => (/* binding */ googlePlayTemplate),\n/* harmony export */   initializeDefaultTemplates: () => (/* binding */ initializeDefaultTemplates),\n/* harmony export */   loadProductTemplates: () => (/* binding */ loadProductTemplates),\n/* harmony export */   pubgMobileTemplate: () => (/* binding */ pubgMobileTemplate),\n/* harmony export */   saveProductTemplate: () => (/* binding */ saveProductTemplate),\n/* harmony export */   tiktokCoinsTemplate: () => (/* binding */ tiktokCoinsTemplate)\n/* harmony export */ });\n// =====================================================\n// DEFAULT PRODUCT TEMPLATES\n// =====================================================\n// ## TODO: Replace with Supabase data loading\n// These templates will be used to initialize the system with sample products\n/**\n * Generate consistent ID for templates (fixed IDs for stability)\n */ function generateId() {\n    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '', suffix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n    // Use consistent IDs instead of random ones to avoid localStorage issues\n    return \"\".concat(prefix).concat(Date.now().toString(36)).concat(suffix).concat(Math.random().toString(36).substr(2, 4));\n}\n/**\n * Generate fixed ID for template components (for consistency)\n */ function fixedId(id) {\n    return id;\n}\n/**\n * PUBG Mobile UC Top-up Template\n */ const pubgMobileTemplate = {\n    id: \"pubg-mobile-uc\",\n    name: \"شحن يوسي PUBG Mobile\",\n    nameEnglish: \"PUBG Mobile UC Top-up\",\n    description: \"شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار\",\n    descriptionEnglish: \"Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 25,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"pubg-player-id\"),\n            type: \"text\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                minLength: 8,\n                maxLength: 12,\n                pattern: \"^[0-9]+$\"\n            },\n            sortOrder: 0,\n            isActive: true\n        },\n        {\n            id: fixedId(\"pubg-server\"),\n            type: \"dropdown\",\n            name: \"server\",\n            label: \"الخادم\",\n            placeholder: \"اختر الخادم...\",\n            required: true,\n            options: [\n                \"الشرق الأوسط\",\n                \"أوروبا\",\n                \"آسيا\",\n                \"أمريكا الشمالية\",\n                \"أمريكا الجنوبية\"\n            ],\n            sortOrder: 1,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"pubg-uc-60\"),\n            name: \"60 يوسي\",\n            amount: \"60 UC\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-325\"),\n            name: \"325 يوسي\",\n            amount: \"325 UC\",\n            price: 25,\n            originalPrice: 30,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-660\"),\n            name: \"660 يوسي\",\n            amount: \"660 UC\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-1800\"),\n            name: \"1800 يوسي\",\n            amount: \"1800 UC\",\n            price: 120,\n            originalPrice: 150,\n            discount: 20,\n            popular: false,\n            isActive: true,\n            sortOrder: 3,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        }\n    ],\n    features: [\n        \"🚀 تسليم فوري للأكواد\",\n        \"💯 ضمان الجودة والأمان\",\n        \"🔒 معاملات آمنة ومشفرة\",\n        \"📱 يعمل على جميع الأجهزة\",\n        \"🎮 دعم فني متخصص\",\n        \"💳 طرق دفع متعددة\"\n    ],\n    tags: [\n        \"pubg\",\n        \"mobile\",\n        \"uc\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Free Fire Diamonds Template\n */ const freeFireTemplate = {\n    id: \"free-fire-diamonds\",\n    name: \"شحن جواهر Free Fire\",\n    nameEnglish: \"Free Fire Diamonds Top-up\",\n    description: \"شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار\",\n    descriptionEnglish: \"Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.\",\n        expiryDays: 7\n    },\n    fields: [\n        {\n            id: fixedId(\"ff-player-id\"),\n            type: \"number\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            labelEnglish: \"Player ID\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                min: 100000000,\n                max: 9999999999\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"ff-diamonds-100\"),\n            name: \"100 جوهرة\",\n            amount: \"100 💎\",\n            price: 10,\n            originalPrice: 12,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-520\"),\n            name: \"520 جوهرة\",\n            amount: \"520 💎\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-1080\"),\n            name: \"1080 جوهرة\",\n            amount: \"1080 💎\",\n            price: 100,\n            originalPrice: 120,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💎 جواهر أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎮 دعم فني 24/7\"\n    ],\n    tags: [\n        \"free fire\",\n        \"diamonds\",\n        \"جواهر\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Google Play Gift Card Template\n */ const googlePlayTemplate = {\n    id: \"google-play-gift-card\",\n    name: \"بطاقة هدايا Google Play\",\n    nameEnglish: \"Google Play Gift Card\",\n    description: \"بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي\",\n    descriptionEnglish: \"Digital Google Play Gift Cards - Use them to buy apps, games, and digital content\",\n    category: \"بطاقات الهدايا\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"coupon\",\n        deliveryInstructions: \"استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.\",\n        expiryDays: 365\n    },\n    fields: [\n        {\n            id: fixedId(\"gp-email\"),\n            type: \"email\",\n            name: \"email\",\n            label: \"البريد الإلكتروني\",\n            labelEnglish: \"Email Address\",\n            placeholder: \"أدخل بريدك الإلكتروني...\",\n            required: true,\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"gp-usd-10\"),\n            name: \"$10 USD\",\n            nameArabic: \"10 دولار\",\n            amount: \"$10 USD\",\n            price: 10,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-25\"),\n            name: \"$25 USD\",\n            nameArabic: \"25 دولار\",\n            amount: \"$25 USD\",\n            price: 25,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-50\"),\n            name: \"$50 USD\",\n            nameArabic: \"50 دولار\",\n            amount: \"$50 USD\",\n            price: 50,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🎁 بطاقة هدايا رقمية\",\n        \"🚀 تسليم فوري\",\n        \"🌍 صالحة عالمياً\",\n        \"📱 لجميع أجهزة Android\",\n        \"🔒 آمنة ومضمونة\"\n    ],\n    tags: [\n        \"google play\",\n        \"gift card\",\n        \"بطاقة هدايا\",\n        \"تطبيقات\"\n    ],\n    isActive: true,\n    isFeatured: false,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * TikTok Coins Template\n */ const tiktokCoinsTemplate = {\n    id: \"tiktok-coins\",\n    name: \"شحن عملات TikTok\",\n    nameEnglish: \"TikTok Coins Top-up\",\n    description: \"شحن فوري لعملات TikTok - ادعم المبدعين المفضلين لديك واحصل على المزيد من المزايا\",\n    descriptionEnglish: \"Instant TikTok Coins top-up - Support your favorite creators and get more features\",\n    category: \"وسائل التواصل\",\n    basePrice: 5,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"gift_code\",\n        deliveryInstructions: \"سيتم إضافة العملات إلى حسابك في TikTok فوراً بعد الدفع.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"tiktok-username\"),\n            type: \"text\",\n            name: \"username\",\n            label: \"اسم المستخدم في TikTok\",\n            placeholder: \"أدخل اسم المستخدم...\",\n            required: true,\n            validation: {\n                minLength: 3,\n                maxLength: 30\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"tiktok-coins-100\"),\n            name: \"100 عملة\",\n            amount: \"100 Coins\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-500\"),\n            name: \"500 عملة\",\n            amount: \"500 Coins\",\n            price: 20,\n            originalPrice: 25,\n            discount: 20,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-1000\"),\n            name: \"1000 عملة\",\n            amount: \"1000 Coins\",\n            price: 35,\n            originalPrice: 45,\n            discount: 22,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💰 عملات أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎁 ادعم المبدعين المفضلين\"\n    ],\n    tags: [\n        \"tiktok\",\n        \"coins\",\n        \"عملات\",\n        \"شحن\",\n        \"وسائل التواصل\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * All default templates\n */ const defaultTemplates = [\n    pubgMobileTemplate,\n    freeFireTemplate,\n    googlePlayTemplate,\n    tiktokCoinsTemplate\n];\n/**\n * Initialize default templates in localStorage (client-side only)\n * ## TODO: Replace with Supabase initialization\n */ function initializeDefaultTemplates() {\n    // Check if we're in a browser environment\n    if (false) {}\n    try {\n        const existingTemplates = localStorage.getItem('productTemplates');\n        if (!existingTemplates) {\n            console.log('Initializing default product templates...');\n            localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            console.log(\"Initialized \".concat(defaultTemplates.length, \" default templates\"));\n        } else {\n            // Validate existing templates\n            try {\n                const parsed = JSON.parse(existingTemplates);\n                if (!Array.isArray(parsed) || parsed.length === 0) {\n                    console.log('Invalid templates found, reinitializing...');\n                    localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n                } else {\n                    console.log(\"Found \".concat(parsed.length, \" existing templates in localStorage\"));\n                }\n            } catch (parseError) {\n                console.log('Corrupted templates found, reinitializing...');\n                localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default templates:', error);\n    }\n}\n/**\n * Force reinitialize templates (useful for debugging)\n */ function forceReinitializeTemplates() {\n    if (false) {}\n    try {\n        console.log('Force reinitializing product templates...');\n        localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n        console.log(\"Reinitialized \".concat(defaultTemplates.length, \" templates\"));\n    } catch (error) {\n        console.error('Error force reinitializing templates:', error);\n    }\n}\n/**\n * Load product templates from localStorage\n * ## TODO: Replace with Supabase query\n */ function loadProductTemplates() {\n    try {\n        const savedTemplates = localStorage.getItem('productTemplates');\n        if (savedTemplates) {\n            return JSON.parse(savedTemplates);\n        }\n        return [];\n    } catch (error) {\n        console.error('Error loading product templates:', error);\n        return [];\n    }\n}\n/**\n * Save product template to localStorage\n * ## TODO: Replace with Supabase insert/update\n */ function saveProductTemplate(template) {\n    try {\n        const templates = loadProductTemplates();\n        const existingIndex = templates.findIndex((t)=>t.id === template.id);\n        if (existingIndex >= 0) {\n            templates[existingIndex] = template;\n        } else {\n            templates.push(template);\n        }\n        localStorage.setItem('productTemplates', JSON.stringify(templates));\n    } catch (error) {\n        console.error('Error saving product template:', error);\n        throw error;\n    }\n}\n/**\n * Delete product template from localStorage\n * ## TODO: Replace with Supabase delete\n */ function deleteProductTemplate(templateId) {\n    try {\n        const templates = loadProductTemplates();\n        const filteredTemplates = templates.filter((t)=>t.id !== templateId);\n        localStorage.setItem('productTemplates', JSON.stringify(filteredTemplates));\n    } catch (error) {\n        console.error('Error deleting product template:', error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/data/defaultProductTemplates.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(\"Only \".concat(availableCodes.length, \" codes available, but \").concat(formData.quantity, \" requested\"));\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(\"Only \".concat(selectedPackage.quantityLimit, \" items available, but \").concat(formData.quantity, \" requested\"));\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: \"timeline_\".concat(Date.now()),\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        // Create corresponding wallet transaction for unified display\n        await createWalletTransactionFromOrder(newOrder);\n        console.log(\"✅ Created order: \".concat(newOrder.id, \" for product: \").concat(product.name));\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(\"Failed to assign digital code \".concat(i + 1, \" of \").concat(quantity));\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', \"\".concat(assignedCodes.length, \" digital codes assigned\"));\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', \"Failed to assign digital codes: \".concat(error));\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        const updatedOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n        if (updatedOrder) {\n            console.log(\"✅ Updated order \".concat(orderId, \" status to: \").concat(status));\n            // Update corresponding wallet transaction\n            await updateWalletTransactionFromOrder(updatedOrder);\n        }\n        return updatedOrder;\n    } catch (error) {\n        console.error('Error updating order status:', error);\n        return null;\n    }\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(\"Order \".concat(order.id, \": \").concat(error));\n            await updateOrderStatus(order.id, 'failed', \"Processing failed: \".concat(error));\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n/**\n * Create a wallet transaction from an order for unified display in the wallet\n */ async function createWalletTransactionFromOrder(order) {\n    try {\n        const transaction = {\n            userId: order.userDetails.email,\n            walletId: \"wallet_\".concat(order.userDetails.email),\n            type: \"purchase\",\n            amount: order.totalPrice,\n            currency: order.currency,\n            description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n            referenceNumber: order.id,\n            status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n            orderId: order.id,\n            hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n            digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                status: order.status === 'completed' ? 'ready' : 'pending',\n                contents: order.digitalCodes.map((code)=>({\n                        id: code.id,\n                        type: 'game_code',\n                        title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                        content: code.key,\n                        instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                        isRevealed: false,\n                        deliveredAt: order.updatedAt || order.createdAt\n                    })),\n                deliveryMethod: 'instant',\n                lastUpdated: order.updatedAt || order.createdAt\n            } : undefined,\n            createdAt: order.createdAt,\n            updatedAt: order.updatedAt || order.createdAt\n        };\n        // Create full transaction with ID and date\n        const fullTransaction = {\n            ...transaction,\n            id: \"txn_order_\".concat(order.id),\n            date: order.createdAt\n        };\n        // Save to localStorage for wallet display\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Check if transaction already exists to avoid duplicates\n        const existingIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (existingIndex >= 0) {\n            // Update existing transaction\n            existingTransactions[existingIndex] = fullTransaction;\n        } else {\n            // Add new transaction\n            existingTransactions.unshift(fullTransaction);\n        }\n        localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n        // Dispatch event to notify wallet page of transaction update\n        window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n            detail: {\n                transaction: fullTransaction,\n                order\n            }\n        }));\n        console.log(\"✅ Created wallet transaction for order: \".concat(order.id));\n    } catch (error) {\n        console.error('Error creating wallet transaction from order:', error);\n    }\n}\n/**\n * Update wallet transaction when order status changes\n */ async function updateWalletTransactionFromOrder(order) {\n    try {\n        const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n        // Find the transaction for this order\n        const transactionIndex = existingTransactions.findIndex((t)=>t.orderId === order.id);\n        if (transactionIndex >= 0) {\n            // Update the existing transaction\n            const updatedTransaction = {\n                ...existingTransactions[transactionIndex],\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : order.status === 'cancelled' ? 'cancelled' : 'pending',\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                updatedAt: order.updatedAt || order.createdAt\n            };\n            existingTransactions[transactionIndex] = updatedTransaction;\n            localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions));\n            // Dispatch event to notify wallet page\n            window.dispatchEvent(new CustomEvent('transactionsUpdated', {\n                detail: {\n                    transaction: updatedTransaction,\n                    order\n                }\n            }));\n            console.log(\"✅ Updated wallet transaction for order: \".concat(order.id));\n        } else {\n            // Transaction doesn't exist, create it\n            await createWalletTransactionFromOrder(order);\n        }\n    } catch (error) {\n        console.error('Error updating wallet transaction from order:', error);\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/orderService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   assignDigitalCode: () => (/* binding */ assignDigitalCode),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getAvailableCodes: () => (/* binding */ getAvailableCodes),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   hardDeleteProduct: () => (/* binding */ hardDeleteProduct),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(\"Product with id \".concat(id, \" not found\"));\n        }\n        console.log('✅ Updated product: \"'.concat(updatedProduct.name, '\"'));\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Soft delete by setting isActive to false\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (!product) return false;\n        const updated = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, {\n            isActive: false\n        });\n        console.log('✅ Soft deleted product: \"'.concat(product.name, '\"'));\n        return !!updated;\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return false;\n    }\n}\n/**\n * Hard delete product (completely remove from storage)\n */ async function hardDeleteProduct(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const success = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.delete(id);\n        console.log(\"✅ Hard deleted product with ID: \".concat(id));\n        return success;\n    } catch (error) {\n        console.error('Error hard deleting product:', error);\n        return false;\n    }\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages.filter((pkg)=>pkg.isActive)) || [];\n}\n/**\n * Get products by category\n */ async function getProductsByCategory(category) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getByCategory(category);\n}\n/**\n * Search products\n */ async function searchProducts(query) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.search(query);\n}\n/**\n * Get available digital codes for a package\n */ async function getAvailableCodes(productId, packageId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAvailableCodes(productId, packageId);\n}\n/**\n * Assign digital code to order\n */ async function assignDigitalCode(productId, packageId, orderId) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.assignDigitalCode(productId, packageId, orderId);\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9zZXJ2aWNlcy9wcm9kdWN0U2VydmljZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSx3REFBd0Q7QUFDeEQsNkJBQTZCO0FBQzdCLHdEQUF3RDtBQUN4RCw0REFBNEQ7QUFDNUQseUVBQXlFO0FBR007QUFDa0I7QUFFakcsd0RBQXdEO0FBQ3hELDBCQUEwQjtBQUMxQix3REFBd0Q7QUFFeEQ7OztDQUdDLEdBQ00sZUFBZUksWUFBWUMsT0FBd0I7SUFDeEQsb0RBQW9EO0lBQ3BELElBQUksSUFBNkIsRUFBRTtRQUNqQ0osNkVBQWtCQTtRQUVsQixrREFBa0Q7UUFDbEQsTUFBTUssbUJBQW1CTixxRUFBY0EsQ0FBQ08sTUFBTTtRQUM5QyxJQUFJRCxpQkFBaUJFLE1BQU0sS0FBSyxHQUFHO1lBQ2pDQyxRQUFRQyxHQUFHLENBQUM7WUFDWlIsNkZBQTBCQTtZQUUxQix3Q0FBd0M7WUFDeEMsS0FBSyxNQUFNUyxZQUFZUiwrRUFBZ0JBLENBQUU7Z0JBQ3ZDSCxxRUFBY0EsQ0FBQ1ksTUFBTSxDQUFDRDtZQUN4QjtRQUNGO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFDdEMsTUFBTSxJQUFJRSxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBRWpELElBQUk7UUFDRiwyQ0FBMkM7UUFDM0MsSUFBSSxLQUE2QixFQUFFLEVBR2xDO1FBRUQseUNBQXlDO1FBQ3pDLE1BQU1HLFdBQVdqQixxRUFBY0EsQ0FBQ2tCLFNBQVM7UUFDekMsT0FBT0YsYUFBYUMsVUFBVVo7SUFDaEMsRUFBRSxPQUFPYyxPQUFPO1FBQ2RWLFFBQVFVLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLGdDQUFnQztRQUNoQyxPQUFPSCxhQUFhYiwrRUFBZ0JBLEVBQUVFO0lBQ3hDO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFlZSxlQUFlQyxFQUFVO0lBQzdDLHFCQUFxQjtJQUNyQixNQUFNLElBQUlSLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsSUFBSTtRQUNGTCxRQUFRQyxHQUFHLENBQUMsOENBQXVDLE9BQUhXLElBQUc7UUFFbkQsOENBQThDO1FBQzlDLElBQUksS0FBNkIsRUFBRSxFQUdsQztRQUVELHlDQUF5QztRQUN6QyxNQUFNQyxVQUFVdEIscUVBQWNBLENBQUMwQixPQUFPLENBQUNMO1FBRXZDLElBQUlDLFdBQVdBLFFBQVFHLFFBQVEsRUFBRTtZQUMvQmhCLFFBQVFDLEdBQUcsQ0FBQyxxQkFBK0NZLE9BQTFCQSxRQUFRSyxJQUFJLEVBQUMsZUFBOEIsT0FBakJMLFFBQVFHLFFBQVEsRUFBQztZQUM1RSxPQUFPSDtRQUNULE9BQU87WUFDTGIsUUFBUUMsR0FBRyxDQUFDLHNCQUF5QixPQUFIVyxJQUFHO1lBQ3JDLE9BQU87UUFDVDtJQUNGLEVBQUUsT0FBT0YsT0FBTztRQUNkVixRQUFRVSxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVTLGNBQWNOLE9BQWdFO0lBQ2xHLHFCQUFxQjtJQUNyQixNQUFNLElBQUlULFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsSUFBSTtRQUNGLGtDQUFrQztRQUNsQyxNQUFNZSxhQUFhN0IscUVBQWNBLENBQUNZLE1BQU0sQ0FBQ1U7UUFDekNiLFFBQVFDLEdBQUcsQ0FBQyx1QkFBb0RtQixPQUE3QkEsV0FBV0YsSUFBSSxFQUFDLGVBQTJCLE9BQWRFLFdBQVdSLEVBQUU7UUFDN0UsT0FBT1E7SUFDVCxFQUFFLE9BQU9WLE9BQU87UUFDZFYsUUFBUVUsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsTUFBTSxJQUFJVyxNQUFNO0lBQ2xCO0FBQ0Y7QUFFQTs7O0NBR0MsR0FDTSxlQUFlQyxjQUFjVixFQUFVLEVBQUVXLE9BQWlDO0lBQy9FLHFCQUFxQjtJQUNyQixNQUFNLElBQUluQixRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBRWpELElBQUk7UUFDRixNQUFNbUIsaUJBQWlCakMscUVBQWNBLENBQUNrQyxNQUFNLENBQUNiLElBQUlXO1FBQ2pELElBQUksQ0FBQ0MsZ0JBQWdCO1lBQ25CLE1BQU0sSUFBSUgsTUFBTSxtQkFBc0IsT0FBSFQsSUFBRztRQUN4QztRQUVBWixRQUFRQyxHQUFHLENBQUMsdUJBQTJDLE9BQXBCdUIsZUFBZU4sSUFBSSxFQUFDO1FBQ3ZELE9BQU9NO0lBQ1QsRUFBRSxPQUFPZCxPQUFPO1FBQ2RWLFFBQVFVLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVnQixjQUFjZCxFQUFVO0lBQzVDLHFCQUFxQjtJQUNyQixNQUFNLElBQUlSLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsSUFBSTtRQUNGLDJDQUEyQztRQUMzQyxNQUFNUSxVQUFVdEIscUVBQWNBLENBQUMwQixPQUFPLENBQUNMO1FBQ3ZDLElBQUksQ0FBQ0MsU0FBUyxPQUFPO1FBRXJCLE1BQU1jLFVBQVVwQyxxRUFBY0EsQ0FBQ2tDLE1BQU0sQ0FBQ2IsSUFBSTtZQUFFSSxVQUFVO1FBQU07UUFDNURoQixRQUFRQyxHQUFHLENBQUMsNEJBQXlDLE9BQWJZLFFBQVFLLElBQUksRUFBQztRQUNyRCxPQUFPLENBQUMsQ0FBQ1M7SUFDWCxFQUFFLE9BQU9qQixPQUFPO1FBQ2RWLFFBQVFVLEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDTSxlQUFla0Isa0JBQWtCaEIsRUFBVTtJQUNoRCxNQUFNLElBQUlSLFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFFakQsSUFBSTtRQUNGLE1BQU13QixVQUFVdEMscUVBQWNBLENBQUN1QyxNQUFNLENBQUNsQjtRQUN0Q1osUUFBUUMsR0FBRyxDQUFDLG1DQUFzQyxPQUFIVztRQUMvQyxPQUFPaUI7SUFDVCxFQUFFLE9BQU9uQixPQUFPO1FBQ2RWLFFBQVFVLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU87SUFDVDtBQUNGO0FBRUEsd0RBQXdEO0FBQ3hELHFCQUFxQjtBQUNyQix3REFBd0Q7QUFFeEQ7OztDQUdDLEdBQ00sZUFBZXFCLG1CQUFtQkMsU0FBaUI7SUFDeEQsTUFBTSxJQUFJNUIsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztJQUVqRCxNQUFNUSxVQUFVLE1BQU1GLGVBQWVxQjtJQUNyQyxPQUFPbkIsQ0FBQUEsb0JBQUFBLDhCQUFBQSxRQUFTb0IsUUFBUSxDQUFDQyxNQUFNLENBQUNDLENBQUFBLE1BQU9BLElBQUluQixRQUFRLE1BQUssRUFBRTtBQUM1RDtBQUVBOztDQUVDLEdBQ00sZUFBZW9CLHNCQUFzQkMsUUFBZ0I7SUFDMUQsTUFBTSxJQUFJakMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztJQUVqRCxJQUFJLEtBQTZCLEVBQUUsRUFJbEM7SUFFRCxPQUFPZCxxRUFBY0EsQ0FBQ2lELGFBQWEsQ0FBQ0g7QUFDdEM7QUFFQTs7Q0FFQyxHQUNNLGVBQWVJLGVBQWVDLEtBQWE7SUFDaEQsTUFBTSxJQUFJdEMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztJQUVqRCxJQUFJLEtBQTZCLEVBQUUsRUFVbEM7SUFFRCxPQUFPZCxxRUFBY0EsQ0FBQ3lELE1BQU0sQ0FBQ047QUFDL0I7QUFFQTs7Q0FFQyxHQUNNLGVBQWVPLGtCQUFrQmpCLFNBQWlCLEVBQUVrQixTQUFpQjtJQUMxRSxNQUFNLElBQUk5QyxRQUFRQyxDQUFBQSxVQUFXQyxXQUFXRCxTQUFTO0lBQ2pELE9BQU9kLHFFQUFjQSxDQUFDMEQsaUJBQWlCLENBQUNqQixXQUFXa0I7QUFDckQ7QUFFQTs7Q0FFQyxHQUNNLGVBQWVDLGtCQUFrQm5CLFNBQWlCLEVBQUVrQixTQUFpQixFQUFFRSxPQUFlO0lBQzNGLE1BQU0sSUFBSWhELFFBQVFDLENBQUFBLFVBQVdDLFdBQVdELFNBQVM7SUFDakQsT0FBT2QscUVBQWNBLENBQUM0RCxpQkFBaUIsQ0FBQ25CLFdBQVdrQixXQUFXRTtBQUNoRTtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVDLG9CQUFvQnJCLFNBQWlCLEVBQUVzQixXQUF1QztJQUNsRyx3Q0FBd0M7SUFDeEM7Ozs7Ozs7Ozs7Ozs7RUFhQSxHQUVBLE1BQU1DLGFBQTZCO1FBQ2pDLEdBQUdELFdBQVc7UUFDZDFDLElBQUk0QztJQUNOO0lBRUEsTUFBTTNDLFVBQVUsTUFBTUYsZUFBZXFCO0lBQ3JDLElBQUksQ0FBQ25CLFNBQVMsTUFBTSxJQUFJUSxNQUFNO0lBRTlCUixRQUFRb0IsUUFBUSxDQUFDd0IsSUFBSSxDQUFDRjtJQUN0QixNQUFNakMsY0FBY1UsV0FBVztRQUFFQyxVQUFVcEIsUUFBUW9CLFFBQVE7SUFBQztJQUU1RCxPQUFPc0I7QUFDVDtBQUVBLHdEQUF3RDtBQUN4RCwyQkFBMkI7QUFDM0Isd0RBQXdEO0FBRXhEOzs7Q0FHQyxHQUNNLGVBQWVHO0lBQ3BCLHFEQUFxRDtJQUNyRDs7Ozs7Ozs7Ozs7Ozs7OztFQWdCQSxHQUVBLHlDQUF5QztJQUN6QyxNQUFNbEQsV0FBVyxNQUFNYjtJQUV2QixzREFBc0Q7SUFDdEQsTUFBTWdFLGdCQUFnQkMsTUFBTUMsT0FBTyxDQUFDckQsWUFBWUEsU0FBUzBCLE1BQU0sQ0FBQ25CLENBQUFBLElBQUtBLEtBQUssT0FBT0EsTUFBTSxZQUFZLEVBQUU7SUFFckcsT0FBTztRQUNMK0MsZUFBZUgsY0FBYzVELE1BQU07UUFDbkNnRSxnQkFBZ0JKLGNBQWN6QixNQUFNLENBQUNuQixDQUFBQSxJQUFLQSxFQUFFQyxRQUFRLEtBQUssTUFBTWpCLE1BQU07UUFDckVpRSxpQkFBaUJMLGNBQWN6QixNQUFNLENBQUNuQixDQUFBQSxJQUFLQSxFQUFFa0QsV0FBVyxLQUFLLFdBQVdsRSxNQUFNO1FBQzlFbUUsa0JBQWtCUCxjQUFjekIsTUFBTSxDQUFDbkIsQ0FBQUEsSUFBS0EsRUFBRWtELFdBQVcsS0FBSyxZQUFZbEUsTUFBTTtRQUNoRm9FLGVBQWVSLGNBQWNTLE1BQU0sQ0FBQyxDQUFDQyxLQUFLdEQ7WUFDeEMsTUFBTWtCLFdBQVdsQixFQUFFa0IsUUFBUSxJQUFJLEVBQUU7WUFDakMsT0FBT29DLE1BQU9ULENBQUFBLE1BQU1DLE9BQU8sQ0FBQzVCLFlBQVlBLFNBQVNsQyxNQUFNLEdBQUc7UUFDNUQsR0FBRztRQUNIdUUsYUFBYTtRQUNiQyxtQkFBbUJDLHFCQUFxQmI7SUFDMUM7QUFDRjtBQUVBLHdEQUF3RDtBQUN4RCxtQkFBbUI7QUFDbkIsd0RBQXdEO0FBRXhEOztDQUVDLEdBQ0QsU0FBU3BELGFBQWFDLFFBQTJCLEVBQUVaLE9BQXdCO0lBQ3pFLG1DQUFtQztJQUNuQyxNQUFNK0QsZ0JBQWdCQyxNQUFNQyxPQUFPLENBQUNyRCxZQUFZQSxTQUFTMEIsTUFBTSxDQUFDbkIsQ0FBQUEsSUFBS0EsS0FBSyxPQUFPQSxNQUFNLFlBQVksRUFBRTtJQUVyRyxJQUFJLENBQUNuQixTQUFTLE9BQU8rRDtJQUVyQixPQUFPQSxjQUFjekIsTUFBTSxDQUFDckIsQ0FBQUE7UUFDMUIseUNBQXlDO1FBQ3pDLElBQUksQ0FBQ0EsUUFBUUssSUFBSSxJQUFJLENBQUNMLFFBQVF3QixRQUFRLEVBQUUsT0FBTztRQUUvQyxJQUFJekMsUUFBUXlDLFFBQVEsSUFBSXhCLFFBQVF3QixRQUFRLEtBQUt6QyxRQUFReUMsUUFBUSxFQUFFLE9BQU87UUFDdEUsSUFBSXpDLFFBQVFxRSxXQUFXLElBQUlwRCxRQUFRb0QsV0FBVyxLQUFLckUsUUFBUXFFLFdBQVcsRUFBRSxPQUFPO1FBQy9FLElBQUlyRSxRQUFRNkUsY0FBYyxJQUFJNUQsUUFBUTRELGNBQWMsS0FBSzdFLFFBQVE2RSxjQUFjLEVBQUUsT0FBTztRQUN4RixJQUFJN0UsUUFBUW9CLFFBQVEsS0FBSzBELGFBQWE3RCxRQUFRRyxRQUFRLEtBQUtwQixRQUFRb0IsUUFBUSxFQUFFLE9BQU87UUFDcEYsSUFBSXBCLFFBQVErRSxVQUFVLEtBQUtELGFBQWE3RCxRQUFROEQsVUFBVSxLQUFLL0UsUUFBUStFLFVBQVUsRUFBRSxPQUFPO1FBQzFGLElBQUkvRSxRQUFRb0QsTUFBTSxFQUFFO1lBQ2xCLE1BQU00QixjQUFjaEYsUUFBUW9ELE1BQU0sQ0FBQ1YsV0FBVztZQUM5QyxNQUFNdUMsWUFBWWhFLFFBQVFLLElBQUksSUFBSUwsUUFBUUssSUFBSSxDQUFDb0IsV0FBVyxHQUFHQyxRQUFRLENBQUNxQztZQUN0RSxNQUFNRSxZQUFZakUsUUFBUStCLFdBQVcsSUFBSS9CLFFBQVErQixXQUFXLENBQUNOLFdBQVcsR0FBR0MsUUFBUSxDQUFDcUM7WUFDcEYsSUFBSSxDQUFDQyxhQUFhLENBQUNDLFdBQVcsT0FBTztRQUN2QztRQUNBLE9BQU87SUFDVDtBQUNGO0FBRUE7O0NBRUMsR0FDRCxTQUFTTixxQkFBcUJoRSxRQUEyQjtJQUN2RCxNQUFNdUUsZ0JBQXdDLENBQUM7SUFFL0Msd0RBQXdEO0lBQ3hELE1BQU1wQixnQkFBZ0JDLE1BQU1DLE9BQU8sQ0FBQ3JELFlBQVlBLFNBQVMwQixNQUFNLENBQUNuQixDQUFBQSxJQUFLQSxLQUFLQSxFQUFFc0IsUUFBUSxJQUFJLEVBQUU7SUFFMUZzQixjQUFjcUIsT0FBTyxDQUFDbkUsQ0FBQUE7UUFDcEIsSUFBSUEsUUFBUXdCLFFBQVEsSUFBSSxPQUFPeEIsUUFBUXdCLFFBQVEsS0FBSyxVQUFVO1lBQzVEMEMsYUFBYSxDQUFDbEUsUUFBUXdCLFFBQVEsQ0FBQyxHQUFHLENBQUMwQyxhQUFhLENBQUNsRSxRQUFRd0IsUUFBUSxDQUFDLElBQUksS0FBSztRQUM3RTtJQUNGO0lBRUEsT0FBTzRDLE9BQU9DLE9BQU8sQ0FBQ0gsZUFDbkJJLEdBQUcsQ0FBQztZQUFDLENBQUM5QyxVQUFVK0MsTUFBTTtlQUFNO1lBQUUvQztZQUFVK0M7UUFBTTtPQUM5Q0MsSUFBSSxDQUFDLENBQUNDLEdBQUdDLElBQU1BLEVBQUVILEtBQUssR0FBR0UsRUFBRUYsS0FBSyxFQUNoQ0ksS0FBSyxDQUFDLEdBQUc7QUFDZDtBQUVBOztDQUVDLEdBQ0QsU0FBU2hDO0lBQ1AsT0FBT2lDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHO0FBQzlDO0FBRUEsd0RBQXdEO0FBQ3hELDhCQUE4QjtBQUM5Qix3REFBd0Q7QUFFeEQ7O0NBRUMsR0FDRCxTQUFTQyx1QkFBdUJDLFNBQWM7SUFDNUMseUVBQXlFO0lBQ3pFLE9BQU9BO0FBQ1Q7QUFFQTs7Q0FFQyxHQUNELFNBQVNDLHVCQUF1QkMsU0FBYztJQUM1Qyx3RUFBd0U7SUFDeEUsT0FBT0E7QUFDVCIsInNvdXJjZXMiOlsiRDpcXFZTLXByb2plY3RzXFx0cnlcXGFscmF5YS1zdG9yZVxcbGliXFxzZXJ2aWNlc1xccHJvZHVjdFNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIFBST0RVQ1QgTUFOQUdFTUVOVCBTRVJWSUNFXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gIyMgVE9ETzogSW1wbGVtZW50IFN1cGFiYXNlIGludGVncmF0aW9uIGZvciBhbGwgZnVuY3Rpb25zXG4vLyAjIyBEQVRBQkFTRSBMQVRFUjogQ29ubmVjdCB0byBwcm9kdWN0cywgcGFja2FnZXMsIGN1c3RvbV9maWVsZHMgdGFibGVzXG5cbmltcG9ydCB7IFByb2R1Y3RUZW1wbGF0ZSwgUHJvZHVjdFBhY2thZ2UsIER5bmFtaWNGaWVsZCwgUHJvZHVjdEZpbHRlcnMsIFByb2R1Y3RTdGF0cyB9IGZyb20gJ0AvbGliL3R5cGVzJ1xuaW1wb3J0IHsgUHJvZHVjdFN0b3JhZ2UsIGluaXRpYWxpemVEYXRhYmFzZSB9IGZyb20gJ0AvbGliL3N0b3JhZ2UvbG9jYWxTdG9yYWdlJ1xuaW1wb3J0IHsgaW5pdGlhbGl6ZURlZmF1bHRUZW1wbGF0ZXMsIGRlZmF1bHRUZW1wbGF0ZXMgfSBmcm9tICdAL2xpYi9kYXRhL2RlZmF1bHRQcm9kdWN0VGVtcGxhdGVzJ1xuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gUFJPRFVDVCBDUlVEIE9QRVJBVElPTlNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogIyMgVE9ETzogSW1wbGVtZW50IFN1cGFiYXNlIHByb2R1Y3QgZmV0Y2hpbmdcbiAqIEZldGNoIGFsbCBwcm9kdWN0cyB3aXRoIG9wdGlvbmFsIGZpbHRlcmluZ1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UHJvZHVjdHMoZmlsdGVycz86IFByb2R1Y3RGaWx0ZXJzKTogUHJvbWlzZTxQcm9kdWN0VGVtcGxhdGVbXT4ge1xuICAvLyBJbml0aWFsaXplIGRhdGFiYXNlIGFuZCBlbnN1cmUgc2FtcGxlIGRhdGEgZXhpc3RzXG4gIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJykge1xuICAgIGluaXRpYWxpemVEYXRhYmFzZSgpXG5cbiAgICAvLyBDaGVjayBpZiB3ZSBuZWVkIHRvIGluaXRpYWxpemUgd2l0aCBzYW1wbGUgZGF0YVxuICAgIGNvbnN0IGV4aXN0aW5nUHJvZHVjdHMgPSBQcm9kdWN0U3RvcmFnZS5nZXRBbGwoKVxuICAgIGlmIChleGlzdGluZ1Byb2R1Y3RzLmxlbmd0aCA9PT0gMCkge1xuICAgICAgY29uc29sZS5sb2coJ/CflIQgSW5pdGlhbGl6aW5nIHdpdGggc2FtcGxlIHByb2R1Y3RzLi4uJylcbiAgICAgIGluaXRpYWxpemVEZWZhdWx0VGVtcGxhdGVzKClcblxuICAgICAgLy8gQWRkIGRlZmF1bHQgdGVtcGxhdGVzIHRvIGxvY2FsU3RvcmFnZVxuICAgICAgZm9yIChjb25zdCB0ZW1wbGF0ZSBvZiBkZWZhdWx0VGVtcGxhdGVzKSB7XG4gICAgICAgIFByb2R1Y3RTdG9yYWdlLmNyZWF0ZSh0ZW1wbGF0ZSlcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvLyBTaW11bGF0ZSBBUEkgZGVsYXkgZm9yIHJlYWxpc3RpYyBVWFxuICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSlcblxuICB0cnkge1xuICAgIC8vIE9uIHNlcnZlci1zaWRlLCByZXR1cm4gZGVmYXVsdCB0ZW1wbGF0ZXNcbiAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgIGNvbnNvbGUubG9nKCdTZXJ2ZXItc2lkZTogcmV0dXJuaW5nIGRlZmF1bHQgdGVtcGxhdGVzJylcbiAgICAgIHJldHVybiBhcHBseUZpbHRlcnMoZGVmYXVsdFRlbXBsYXRlcywgZmlsdGVycylcbiAgICB9XG5cbiAgICAvLyBPbiBjbGllbnQtc2lkZSwgbG9hZCBmcm9tIGxvY2FsU3RvcmFnZVxuICAgIGNvbnN0IHByb2R1Y3RzID0gUHJvZHVjdFN0b3JhZ2UuZ2V0QWN0aXZlKClcbiAgICByZXR1cm4gYXBwbHlGaWx0ZXJzKHByb2R1Y3RzLCBmaWx0ZXJzKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgcHJvZHVjdHM6JywgZXJyb3IpXG4gICAgLy8gRmFsbGJhY2sgdG8gZGVmYXVsdCB0ZW1wbGF0ZXNcbiAgICByZXR1cm4gYXBwbHlGaWx0ZXJzKGRlZmF1bHRUZW1wbGF0ZXMsIGZpbHRlcnMpXG4gIH1cbn1cblxuLyoqXG4gKiAjIyBUT0RPOiBJbXBsZW1lbnQgU3VwYWJhc2UgcHJvZHVjdCBmZXRjaGluZyBieSBJRFxuICogRmV0Y2ggc2luZ2xlIHByb2R1Y3QgYnkgSURcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFByb2R1Y3RCeUlkKGlkOiBzdHJpbmcpOiBQcm9taXNlPFByb2R1Y3RUZW1wbGF0ZSB8IG51bGw+IHtcbiAgLy8gU2ltdWxhdGUgQVBJIGRlbGF5XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuXG4gIHRyeSB7XG4gICAgY29uc29sZS5sb2coYPCflI0gTG9va2luZyBmb3IgcHJvZHVjdCB3aXRoIElEOiBcIiR7aWR9XCJgKVxuXG4gICAgLy8gT24gc2VydmVyLXNpZGUsIHNlYXJjaCBpbiBkZWZhdWx0IHRlbXBsYXRlc1xuICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykge1xuICAgICAgY29uc3QgcHJvZHVjdCA9IGRlZmF1bHRUZW1wbGF0ZXMuZmluZChwID0+IHAuaWQgPT09IGlkICYmIHAuaXNBY3RpdmUpXG4gICAgICByZXR1cm4gcHJvZHVjdCB8fCBudWxsXG4gICAgfVxuXG4gICAgLy8gT24gY2xpZW50LXNpZGUsIHNlYXJjaCBpbiBsb2NhbFN0b3JhZ2VcbiAgICBjb25zdCBwcm9kdWN0ID0gUHJvZHVjdFN0b3JhZ2UuZ2V0QnlJZChpZClcblxuICAgIGlmIChwcm9kdWN0ICYmIHByb2R1Y3QuaXNBY3RpdmUpIHtcbiAgICAgIGNvbnNvbGUubG9nKGDinIUgRm91bmQgcHJvZHVjdDogXCIke3Byb2R1Y3QubmFtZX1cIiAoQWN0aXZlOiAke3Byb2R1Y3QuaXNBY3RpdmV9KWApXG4gICAgICByZXR1cm4gcHJvZHVjdFxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZyhg4p2MIFByb2R1Y3Qgd2l0aCBJRCBcIiR7aWR9XCIgbm90IGZvdW5kIG9yIGluYWN0aXZlYClcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGdldFByb2R1Y3RCeUlkOicsIGVycm9yKVxuICAgIHJldHVybiBudWxsXG4gIH1cbn1cblxuLyoqXG4gKiAjIyBUT0RPOiBJbXBsZW1lbnQgU3VwYWJhc2UgcHJvZHVjdCBjcmVhdGlvblxuICogQ3JlYXRlIG5ldyBwcm9kdWN0IHdpdGggcGFja2FnZXMgYW5kIGZpZWxkc1xuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gY3JlYXRlUHJvZHVjdChwcm9kdWN0OiBPbWl0PFByb2R1Y3RUZW1wbGF0ZSwgJ2lkJyB8ICdjcmVhdGVkQXQnIHwgJ3VwZGF0ZWRBdCc+KTogUHJvbWlzZTxQcm9kdWN0VGVtcGxhdGU+IHtcbiAgLy8gU2ltdWxhdGUgQVBJIGRlbGF5XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAyMDApKVxuXG4gIHRyeSB7XG4gICAgLy8gVXNlIG91ciBuZXcgbG9jYWxTdG9yYWdlIHN5c3RlbVxuICAgIGNvbnN0IG5ld1Byb2R1Y3QgPSBQcm9kdWN0U3RvcmFnZS5jcmVhdGUocHJvZHVjdClcbiAgICBjb25zb2xlLmxvZyhg4pyFIENyZWF0ZWQgcHJvZHVjdDogXCIke25ld1Byb2R1Y3QubmFtZX1cIiB3aXRoIElEOiAke25ld1Byb2R1Y3QuaWR9YClcbiAgICByZXR1cm4gbmV3UHJvZHVjdFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHByb2R1Y3Q6JywgZXJyb3IpXG4gICAgdGhyb3cgbmV3IEVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIHByb2R1Y3QnKVxuICB9XG59XG5cbi8qKlxuICogIyMgVE9ETzogSW1wbGVtZW50IFN1cGFiYXNlIHByb2R1Y3QgdXBkYXRlXG4gKiBVcGRhdGUgZXhpc3RpbmcgcHJvZHVjdFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gdXBkYXRlUHJvZHVjdChpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFByb2R1Y3RUZW1wbGF0ZT4pOiBQcm9taXNlPFByb2R1Y3RUZW1wbGF0ZT4ge1xuICAvLyBTaW11bGF0ZSBBUEkgZGVsYXlcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMCkpXG5cbiAgdHJ5IHtcbiAgICBjb25zdCB1cGRhdGVkUHJvZHVjdCA9IFByb2R1Y3RTdG9yYWdlLnVwZGF0ZShpZCwgdXBkYXRlcylcbiAgICBpZiAoIXVwZGF0ZWRQcm9kdWN0KSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYFByb2R1Y3Qgd2l0aCBpZCAke2lkfSBub3QgZm91bmRgKVxuICAgIH1cblxuICAgIGNvbnNvbGUubG9nKGDinIUgVXBkYXRlZCBwcm9kdWN0OiBcIiR7dXBkYXRlZFByb2R1Y3QubmFtZX1cImApXG4gICAgcmV0dXJuIHVwZGF0ZWRQcm9kdWN0XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgcHJvZHVjdDonLCBlcnJvcilcbiAgICB0aHJvdyBlcnJvclxuICB9XG59XG5cbi8qKlxuICogIyMgVE9ETzogSW1wbGVtZW50IFN1cGFiYXNlIHByb2R1Y3QgZGVsZXRpb25cbiAqIERlbGV0ZSBwcm9kdWN0IGFuZCByZWxhdGVkIGRhdGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRlbGV0ZVByb2R1Y3QoaWQ6IHN0cmluZyk6IFByb21pc2U8Ym9vbGVhbj4ge1xuICAvLyBTaW11bGF0ZSBBUEkgZGVsYXlcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDIwMCkpXG5cbiAgdHJ5IHtcbiAgICAvLyBTb2Z0IGRlbGV0ZSBieSBzZXR0aW5nIGlzQWN0aXZlIHRvIGZhbHNlXG4gICAgY29uc3QgcHJvZHVjdCA9IFByb2R1Y3RTdG9yYWdlLmdldEJ5SWQoaWQpXG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm4gZmFsc2VcblxuICAgIGNvbnN0IHVwZGF0ZWQgPSBQcm9kdWN0U3RvcmFnZS51cGRhdGUoaWQsIHsgaXNBY3RpdmU6IGZhbHNlIH0pXG4gICAgY29uc29sZS5sb2coYOKchSBTb2Z0IGRlbGV0ZWQgcHJvZHVjdDogXCIke3Byb2R1Y3QubmFtZX1cImApXG4gICAgcmV0dXJuICEhdXBkYXRlZFxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHByb2R1Y3Q6JywgZXJyb3IpXG4gICAgcmV0dXJuIGZhbHNlXG4gIH1cbn1cblxuLyoqXG4gKiBIYXJkIGRlbGV0ZSBwcm9kdWN0IChjb21wbGV0ZWx5IHJlbW92ZSBmcm9tIHN0b3JhZ2UpXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBoYXJkRGVsZXRlUHJvZHVjdChpZDogc3RyaW5nKTogUHJvbWlzZTxib29sZWFuPiB7XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAyMDApKVxuXG4gIHRyeSB7XG4gICAgY29uc3Qgc3VjY2VzcyA9IFByb2R1Y3RTdG9yYWdlLmRlbGV0ZShpZClcbiAgICBjb25zb2xlLmxvZyhg4pyFIEhhcmQgZGVsZXRlZCBwcm9kdWN0IHdpdGggSUQ6ICR7aWR9YClcbiAgICByZXR1cm4gc3VjY2Vzc1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGhhcmQgZGVsZXRpbmcgcHJvZHVjdDonLCBlcnJvcilcbiAgICByZXR1cm4gZmFsc2VcbiAgfVxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gUEFDS0FHRSBNQU5BR0VNRU5UXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vKipcbiAqICMjIFRPRE86IEltcGxlbWVudCBTdXBhYmFzZSBwYWNrYWdlIG9wZXJhdGlvbnNcbiAqIEdldCBwYWNrYWdlcyBmb3IgYSBzcGVjaWZpYyBwcm9kdWN0XG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRQcm9kdWN0UGFja2FnZXMocHJvZHVjdElkOiBzdHJpbmcpOiBQcm9taXNlPFByb2R1Y3RQYWNrYWdlW10+IHtcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwKSlcblxuICBjb25zdCBwcm9kdWN0ID0gYXdhaXQgZ2V0UHJvZHVjdEJ5SWQocHJvZHVjdElkKVxuICByZXR1cm4gcHJvZHVjdD8ucGFja2FnZXMuZmlsdGVyKHBrZyA9PiBwa2cuaXNBY3RpdmUpIHx8IFtdXG59XG5cbi8qKlxuICogR2V0IHByb2R1Y3RzIGJ5IGNhdGVnb3J5XG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRQcm9kdWN0c0J5Q2F0ZWdvcnkoY2F0ZWdvcnk6IHN0cmluZyk6IFByb21pc2U8UHJvZHVjdFRlbXBsYXRlW10+IHtcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDEwMCkpXG5cbiAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSB7XG4gICAgcmV0dXJuIGRlZmF1bHRUZW1wbGF0ZXMuZmlsdGVyKHAgPT5cbiAgICAgIHAuaXNBY3RpdmUgJiYgcC5jYXRlZ29yeS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKGNhdGVnb3J5LnRvTG93ZXJDYXNlKCkpXG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIFByb2R1Y3RTdG9yYWdlLmdldEJ5Q2F0ZWdvcnkoY2F0ZWdvcnkpXG59XG5cbi8qKlxuICogU2VhcmNoIHByb2R1Y3RzXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzZWFyY2hQcm9kdWN0cyhxdWVyeTogc3RyaW5nKTogUHJvbWlzZTxQcm9kdWN0VGVtcGxhdGVbXT4ge1xuICBhd2FpdCBuZXcgUHJvbWlzZShyZXNvbHZlID0+IHNldFRpbWVvdXQocmVzb2x2ZSwgMTAwKSlcblxuICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBjb25zdCBzZWFyY2hUZXJtID0gcXVlcnkudG9Mb3dlckNhc2UoKVxuICAgIHJldHVybiBkZWZhdWx0VGVtcGxhdGVzLmZpbHRlcihwID0+XG4gICAgICBwLmlzQWN0aXZlICYmIChcbiAgICAgICAgcC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgICAgcC5kZXNjcmlwdGlvbj8udG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtKSB8fFxuICAgICAgICBwLmNhdGVnb3J5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgICAgcC50YWdzPy5zb21lKHRhZyA9PiB0YWcudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtKSlcbiAgICAgIClcbiAgICApXG4gIH1cblxuICByZXR1cm4gUHJvZHVjdFN0b3JhZ2Uuc2VhcmNoKHF1ZXJ5KVxufVxuXG4vKipcbiAqIEdldCBhdmFpbGFibGUgZGlnaXRhbCBjb2RlcyBmb3IgYSBwYWNrYWdlXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRBdmFpbGFibGVDb2Rlcyhwcm9kdWN0SWQ6IHN0cmluZywgcGFja2FnZUlkOiBzdHJpbmcpIHtcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwKSlcbiAgcmV0dXJuIFByb2R1Y3RTdG9yYWdlLmdldEF2YWlsYWJsZUNvZGVzKHByb2R1Y3RJZCwgcGFja2FnZUlkKVxufVxuXG4vKipcbiAqIEFzc2lnbiBkaWdpdGFsIGNvZGUgdG8gb3JkZXJcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGFzc2lnbkRpZ2l0YWxDb2RlKHByb2R1Y3RJZDogc3RyaW5nLCBwYWNrYWdlSWQ6IHN0cmluZywgb3JkZXJJZDogc3RyaW5nKSB7XG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDApKVxuICByZXR1cm4gUHJvZHVjdFN0b3JhZ2UuYXNzaWduRGlnaXRhbENvZGUocHJvZHVjdElkLCBwYWNrYWdlSWQsIG9yZGVySWQpXG59XG5cbi8qKlxuICogIyMgVE9ETzogSW1wbGVtZW50IFN1cGFiYXNlIHBhY2thZ2UgY3JlYXRpb25cbiAqIEFkZCBwYWNrYWdlIHRvIHByb2R1Y3RcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGFkZFBhY2thZ2VUb1Byb2R1Y3QocHJvZHVjdElkOiBzdHJpbmcsIHBhY2thZ2VEYXRhOiBPbWl0PFByb2R1Y3RQYWNrYWdlLCAnaWQnPik6IFByb21pc2U8UHJvZHVjdFBhY2thZ2U+IHtcbiAgLy8gIyMgVE9ETzogUmVwbGFjZSB3aXRoIFN1cGFiYXNlIGluc2VydFxuICAvKlxuICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgIC5mcm9tKCdwcm9kdWN0X3BhY2thZ2VzJylcbiAgICAuaW5zZXJ0KHtcbiAgICAgIHByb2R1Y3RfaWQ6IHByb2R1Y3RJZCxcbiAgICAgIG5hbWU6IHBhY2thZ2VEYXRhLm5hbWUsXG4gICAgICAvLyAuLi4gb3RoZXIgZmllbGRzXG4gICAgfSlcbiAgICAuc2VsZWN0KClcbiAgICAuc2luZ2xlKClcbiAgXG4gIGlmIChlcnJvcikgdGhyb3cgZXJyb3JcbiAgcmV0dXJuIHRyYW5zZm9ybVBhY2thZ2VGcm9tREIoZGF0YSlcbiAgKi9cbiAgXG4gIGNvbnN0IG5ld1BhY2thZ2U6IFByb2R1Y3RQYWNrYWdlID0ge1xuICAgIC4uLnBhY2thZ2VEYXRhLFxuICAgIGlkOiBnZW5lcmF0ZUlkKClcbiAgfVxuICBcbiAgY29uc3QgcHJvZHVjdCA9IGF3YWl0IGdldFByb2R1Y3RCeUlkKHByb2R1Y3RJZClcbiAgaWYgKCFwcm9kdWN0KSB0aHJvdyBuZXcgRXJyb3IoJ1Byb2R1Y3Qgbm90IGZvdW5kJylcbiAgXG4gIHByb2R1Y3QucGFja2FnZXMucHVzaChuZXdQYWNrYWdlKVxuICBhd2FpdCB1cGRhdGVQcm9kdWN0KHByb2R1Y3RJZCwgeyBwYWNrYWdlczogcHJvZHVjdC5wYWNrYWdlcyB9KVxuICBcbiAgcmV0dXJuIG5ld1BhY2thZ2Vcbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIFNUQVRJU1RJQ1MgQU5EIEFOQUxZVElDU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLyoqXG4gKiAjIyBUT0RPOiBJbXBsZW1lbnQgU3VwYWJhc2UgYW5hbHl0aWNzIHF1ZXJpZXNcbiAqIEdldCBwcm9kdWN0IHN0YXRpc3RpY3MgZm9yIGFkbWluIGRhc2hib2FyZFxuICovXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gZ2V0UHJvZHVjdFN0YXRzKCk6IFByb21pc2U8UHJvZHVjdFN0YXRzPiB7XG4gIC8vICMjIFRPRE86IFJlcGxhY2Ugd2l0aCBTdXBhYmFzZSBhZ2dyZWdhdGlvbiBxdWVyaWVzXG4gIC8qXG4gIGNvbnN0IFtcbiAgICB0b3RhbFByb2R1Y3RzLFxuICAgIGFjdGl2ZVByb2R1Y3RzLFxuICAgIGRpZ2l0YWxQcm9kdWN0cyxcbiAgICB0b3RhbFBhY2thZ2VzLFxuICAgIHRvdGFsT3JkZXJzLFxuICAgIHBvcHVsYXJDYXRlZ29yaWVzXG4gIF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgc3VwYWJhc2UuZnJvbSgncHJvZHVjdHMnKS5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KSxcbiAgICBzdXBhYmFzZS5mcm9tKCdwcm9kdWN0cycpLnNlbGVjdCgnaWQnLCB7IGNvdW50OiAnZXhhY3QnIH0pLmVxKCdpc19hY3RpdmUnLCB0cnVlKSxcbiAgICBzdXBhYmFzZS5mcm9tKCdwcm9kdWN0cycpLnNlbGVjdCgnaWQnLCB7IGNvdW50OiAnZXhhY3QnIH0pLmVxKCdwcm9kdWN0X3R5cGUnLCAnZGlnaXRhbCcpLFxuICAgIHN1cGFiYXNlLmZyb20oJ3Byb2R1Y3RfcGFja2FnZXMnKS5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KSxcbiAgICBzdXBhYmFzZS5mcm9tKCdvcmRlcnMnKS5zZWxlY3QoJ2lkJywgeyBjb3VudDogJ2V4YWN0JyB9KSxcbiAgICBzdXBhYmFzZS5mcm9tKCdwcm9kdWN0cycpLnNlbGVjdCgnY2F0ZWdvcnknKS5ncm91cEJ5KCdjYXRlZ29yeScpXG4gIF0pXG4gICovXG4gIFxuICAvLyBUZW1wb3Jhcnk6IENhbGN1bGF0ZSBmcm9tIGxvY2FsU3RvcmFnZVxuICBjb25zdCBwcm9kdWN0cyA9IGF3YWl0IGdldFByb2R1Y3RzKClcblxuICAvLyBFbnN1cmUgcHJvZHVjdHMgaXMgYW4gYXJyYXkgYW5kIGhhcyB2YWxpZCBzdHJ1Y3R1cmVcbiAgY29uc3QgdmFsaWRQcm9kdWN0cyA9IEFycmF5LmlzQXJyYXkocHJvZHVjdHMpID8gcHJvZHVjdHMuZmlsdGVyKHAgPT4gcCAmJiB0eXBlb2YgcCA9PT0gJ29iamVjdCcpIDogW11cblxuICByZXR1cm4ge1xuICAgIHRvdGFsUHJvZHVjdHM6IHZhbGlkUHJvZHVjdHMubGVuZ3RoLFxuICAgIGFjdGl2ZVByb2R1Y3RzOiB2YWxpZFByb2R1Y3RzLmZpbHRlcihwID0+IHAuaXNBY3RpdmUgPT09IHRydWUpLmxlbmd0aCxcbiAgICBkaWdpdGFsUHJvZHVjdHM6IHZhbGlkUHJvZHVjdHMuZmlsdGVyKHAgPT4gcC5wcm9kdWN0VHlwZSA9PT0gJ2RpZ2l0YWwnKS5sZW5ndGgsXG4gICAgcGh5c2ljYWxQcm9kdWN0czogdmFsaWRQcm9kdWN0cy5maWx0ZXIocCA9PiBwLnByb2R1Y3RUeXBlID09PSAncGh5c2ljYWwnKS5sZW5ndGgsXG4gICAgdG90YWxQYWNrYWdlczogdmFsaWRQcm9kdWN0cy5yZWR1Y2UoKHN1bSwgcCkgPT4ge1xuICAgICAgY29uc3QgcGFja2FnZXMgPSBwLnBhY2thZ2VzIHx8IFtdXG4gICAgICByZXR1cm4gc3VtICsgKEFycmF5LmlzQXJyYXkocGFja2FnZXMpID8gcGFja2FnZXMubGVuZ3RoIDogMClcbiAgICB9LCAwKSxcbiAgICB0b3RhbE9yZGVyczogMCwgLy8gIyMgVE9ETzogR2V0IGZyb20gb3JkZXJzIHRhYmxlXG4gICAgcG9wdWxhckNhdGVnb3JpZXM6IGdldFBvcHVsYXJDYXRlZ29yaWVzKHZhbGlkUHJvZHVjdHMpXG4gIH1cbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIEhFTFBFUiBGVU5DVElPTlNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogQXBwbHkgZmlsdGVycyB0byBwcm9kdWN0cyBhcnJheSAodGVtcG9yYXJ5IGltcGxlbWVudGF0aW9uKVxuICovXG5mdW5jdGlvbiBhcHBseUZpbHRlcnMocHJvZHVjdHM6IFByb2R1Y3RUZW1wbGF0ZVtdLCBmaWx0ZXJzPzogUHJvZHVjdEZpbHRlcnMpOiBQcm9kdWN0VGVtcGxhdGVbXSB7XG4gIC8vIEVuc3VyZSBwcm9kdWN0cyBpcyBhIHZhbGlkIGFycmF5XG4gIGNvbnN0IHZhbGlkUHJvZHVjdHMgPSBBcnJheS5pc0FycmF5KHByb2R1Y3RzKSA/IHByb2R1Y3RzLmZpbHRlcihwID0+IHAgJiYgdHlwZW9mIHAgPT09ICdvYmplY3QnKSA6IFtdXG5cbiAgaWYgKCFmaWx0ZXJzKSByZXR1cm4gdmFsaWRQcm9kdWN0c1xuXG4gIHJldHVybiB2YWxpZFByb2R1Y3RzLmZpbHRlcihwcm9kdWN0ID0+IHtcbiAgICAvLyBFbnN1cmUgcHJvZHVjdCBoYXMgcmVxdWlyZWQgcHJvcGVydGllc1xuICAgIGlmICghcHJvZHVjdC5uYW1lIHx8ICFwcm9kdWN0LmNhdGVnb3J5KSByZXR1cm4gZmFsc2VcblxuICAgIGlmIChmaWx0ZXJzLmNhdGVnb3J5ICYmIHByb2R1Y3QuY2F0ZWdvcnkgIT09IGZpbHRlcnMuY2F0ZWdvcnkpIHJldHVybiBmYWxzZVxuICAgIGlmIChmaWx0ZXJzLnByb2R1Y3RUeXBlICYmIHByb2R1Y3QucHJvZHVjdFR5cGUgIT09IGZpbHRlcnMucHJvZHVjdFR5cGUpIHJldHVybiBmYWxzZVxuICAgIGlmIChmaWx0ZXJzLnByb2Nlc3NpbmdUeXBlICYmIHByb2R1Y3QucHJvY2Vzc2luZ1R5cGUgIT09IGZpbHRlcnMucHJvY2Vzc2luZ1R5cGUpIHJldHVybiBmYWxzZVxuICAgIGlmIChmaWx0ZXJzLmlzQWN0aXZlICE9PSB1bmRlZmluZWQgJiYgcHJvZHVjdC5pc0FjdGl2ZSAhPT0gZmlsdGVycy5pc0FjdGl2ZSkgcmV0dXJuIGZhbHNlXG4gICAgaWYgKGZpbHRlcnMuaXNGZWF0dXJlZCAhPT0gdW5kZWZpbmVkICYmIHByb2R1Y3QuaXNGZWF0dXJlZCAhPT0gZmlsdGVycy5pc0ZlYXR1cmVkKSByZXR1cm4gZmFsc2VcbiAgICBpZiAoZmlsdGVycy5zZWFyY2gpIHtcbiAgICAgIGNvbnN0IHNlYXJjaExvd2VyID0gZmlsdGVycy5zZWFyY2gudG9Mb3dlckNhc2UoKVxuICAgICAgY29uc3QgbmFtZU1hdGNoID0gcHJvZHVjdC5uYW1lICYmIHByb2R1Y3QubmFtZS50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaExvd2VyKVxuICAgICAgY29uc3QgZGVzY01hdGNoID0gcHJvZHVjdC5kZXNjcmlwdGlvbiAmJiBwcm9kdWN0LmRlc2NyaXB0aW9uLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoTG93ZXIpXG4gICAgICBpZiAoIW5hbWVNYXRjaCAmJiAhZGVzY01hdGNoKSByZXR1cm4gZmFsc2VcbiAgICB9XG4gICAgcmV0dXJuIHRydWVcbiAgfSlcbn1cblxuLyoqXG4gKiBHZXQgcG9wdWxhciBjYXRlZ29yaWVzIGZyb20gcHJvZHVjdHNcbiAqL1xuZnVuY3Rpb24gZ2V0UG9wdWxhckNhdGVnb3JpZXMocHJvZHVjdHM6IFByb2R1Y3RUZW1wbGF0ZVtdKSB7XG4gIGNvbnN0IGNhdGVnb3J5Q291bnQ6IFJlY29yZDxzdHJpbmcsIG51bWJlcj4gPSB7fVxuXG4gIC8vIEVuc3VyZSBwcm9kdWN0cyBpcyBhbiBhcnJheSBhbmQgZmlsdGVyIHZhbGlkIHByb2R1Y3RzXG4gIGNvbnN0IHZhbGlkUHJvZHVjdHMgPSBBcnJheS5pc0FycmF5KHByb2R1Y3RzKSA/IHByb2R1Y3RzLmZpbHRlcihwID0+IHAgJiYgcC5jYXRlZ29yeSkgOiBbXVxuXG4gIHZhbGlkUHJvZHVjdHMuZm9yRWFjaChwcm9kdWN0ID0+IHtcbiAgICBpZiAocHJvZHVjdC5jYXRlZ29yeSAmJiB0eXBlb2YgcHJvZHVjdC5jYXRlZ29yeSA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGNhdGVnb3J5Q291bnRbcHJvZHVjdC5jYXRlZ29yeV0gPSAoY2F0ZWdvcnlDb3VudFtwcm9kdWN0LmNhdGVnb3J5XSB8fCAwKSArIDFcbiAgICB9XG4gIH0pXG5cbiAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKGNhdGVnb3J5Q291bnQpXG4gICAgLm1hcCgoW2NhdGVnb3J5LCBjb3VudF0pID0+ICh7IGNhdGVnb3J5LCBjb3VudCB9KSlcbiAgICAuc29ydCgoYSwgYikgPT4gYi5jb3VudCAtIGEuY291bnQpXG4gICAgLnNsaWNlKDAsIDUpXG59XG5cbi8qKlxuICogR2VuZXJhdGUgdW5pcXVlIElEICh0ZW1wb3JhcnkgaW1wbGVtZW50YXRpb24pXG4gKi9cbmZ1bmN0aW9uIGdlbmVyYXRlSWQoKTogc3RyaW5nIHtcbiAgcmV0dXJuIE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KVxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gREFUQSBUUkFOU0ZPUk1BVElPTiBIRUxQRVJTXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG4vKipcbiAqICMjIFRPRE86IFRyYW5zZm9ybSBkYXRhYmFzZSBwcm9kdWN0IHRvIFByb2R1Y3RUZW1wbGF0ZSBpbnRlcmZhY2VcbiAqL1xuZnVuY3Rpb24gdHJhbnNmb3JtUHJvZHVjdEZyb21EQihkYlByb2R1Y3Q6IGFueSk6IFByb2R1Y3RUZW1wbGF0ZSB7XG4gIC8vICMjIFRPRE86IEltcGxlbWVudCB0cmFuc2Zvcm1hdGlvbiBmcm9tIFN1cGFiYXNlIHJvdyB0byBQcm9kdWN0VGVtcGxhdGVcbiAgcmV0dXJuIGRiUHJvZHVjdFxufVxuXG4vKipcbiAqICMjIFRPRE86IFRyYW5zZm9ybSBkYXRhYmFzZSBwYWNrYWdlIHRvIFByb2R1Y3RQYWNrYWdlIGludGVyZmFjZVxuICovXG5mdW5jdGlvbiB0cmFuc2Zvcm1QYWNrYWdlRnJvbURCKGRiUGFja2FnZTogYW55KTogUHJvZHVjdFBhY2thZ2Uge1xuICAvLyAjIyBUT0RPOiBJbXBsZW1lbnQgdHJhbnNmb3JtYXRpb24gZnJvbSBTdXBhYmFzZSByb3cgdG8gUHJvZHVjdFBhY2thZ2VcbiAgcmV0dXJuIGRiUGFja2FnZVxufVxuIl0sIm5hbWVzIjpbIlByb2R1Y3RTdG9yYWdlIiwiaW5pdGlhbGl6ZURhdGFiYXNlIiwiaW5pdGlhbGl6ZURlZmF1bHRUZW1wbGF0ZXMiLCJkZWZhdWx0VGVtcGxhdGVzIiwiZ2V0UHJvZHVjdHMiLCJmaWx0ZXJzIiwiZXhpc3RpbmdQcm9kdWN0cyIsImdldEFsbCIsImxlbmd0aCIsImNvbnNvbGUiLCJsb2ciLCJ0ZW1wbGF0ZSIsImNyZWF0ZSIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsImFwcGx5RmlsdGVycyIsInByb2R1Y3RzIiwiZ2V0QWN0aXZlIiwiZXJyb3IiLCJnZXRQcm9kdWN0QnlJZCIsImlkIiwicHJvZHVjdCIsImZpbmQiLCJwIiwiaXNBY3RpdmUiLCJnZXRCeUlkIiwibmFtZSIsImNyZWF0ZVByb2R1Y3QiLCJuZXdQcm9kdWN0IiwiRXJyb3IiLCJ1cGRhdGVQcm9kdWN0IiwidXBkYXRlcyIsInVwZGF0ZWRQcm9kdWN0IiwidXBkYXRlIiwiZGVsZXRlUHJvZHVjdCIsInVwZGF0ZWQiLCJoYXJkRGVsZXRlUHJvZHVjdCIsInN1Y2Nlc3MiLCJkZWxldGUiLCJnZXRQcm9kdWN0UGFja2FnZXMiLCJwcm9kdWN0SWQiLCJwYWNrYWdlcyIsImZpbHRlciIsInBrZyIsImdldFByb2R1Y3RzQnlDYXRlZ29yeSIsImNhdGVnb3J5IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImdldEJ5Q2F0ZWdvcnkiLCJzZWFyY2hQcm9kdWN0cyIsInF1ZXJ5Iiwic2VhcmNoVGVybSIsImRlc2NyaXB0aW9uIiwidGFncyIsInNvbWUiLCJ0YWciLCJzZWFyY2giLCJnZXRBdmFpbGFibGVDb2RlcyIsInBhY2thZ2VJZCIsImFzc2lnbkRpZ2l0YWxDb2RlIiwib3JkZXJJZCIsImFkZFBhY2thZ2VUb1Byb2R1Y3QiLCJwYWNrYWdlRGF0YSIsIm5ld1BhY2thZ2UiLCJnZW5lcmF0ZUlkIiwicHVzaCIsImdldFByb2R1Y3RTdGF0cyIsInZhbGlkUHJvZHVjdHMiLCJBcnJheSIsImlzQXJyYXkiLCJ0b3RhbFByb2R1Y3RzIiwiYWN0aXZlUHJvZHVjdHMiLCJkaWdpdGFsUHJvZHVjdHMiLCJwcm9kdWN0VHlwZSIsInBoeXNpY2FsUHJvZHVjdHMiLCJ0b3RhbFBhY2thZ2VzIiwicmVkdWNlIiwic3VtIiwidG90YWxPcmRlcnMiLCJwb3B1bGFyQ2F0ZWdvcmllcyIsImdldFBvcHVsYXJDYXRlZ29yaWVzIiwicHJvY2Vzc2luZ1R5cGUiLCJ1bmRlZmluZWQiLCJpc0ZlYXR1cmVkIiwic2VhcmNoTG93ZXIiLCJuYW1lTWF0Y2giLCJkZXNjTWF0Y2giLCJjYXRlZ29yeUNvdW50IiwiZm9yRWFjaCIsIk9iamVjdCIsImVudHJpZXMiLCJtYXAiLCJjb3VudCIsInNvcnQiLCJhIiwiYiIsInNsaWNlIiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwidHJhbnNmb3JtUHJvZHVjdEZyb21EQiIsImRiUHJvZHVjdCIsInRyYW5zZm9ybVBhY2thZ2VGcm9tREIiLCJkYlBhY2thZ2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/storage/localStorage.ts":
/*!*************************************!*\
  !*** ./lib/storage/localStorage.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStorage: () => (/* binding */ OrderStorage),\n/* harmony export */   ProductStorage: () => (/* binding */ ProductStorage),\n/* harmony export */   SettingsStorage: () => (/* binding */ SettingsStorage),\n/* harmony export */   UserStorage: () => (/* binding */ UserStorage),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   exportDatabase: () => (/* binding */ exportDatabase),\n/* harmony export */   importDatabase: () => (/* binding */ importDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/**\n * Local Storage Database System\n * \n * Provides a complete database-like interface using localStorage\n * Handles products, orders, users, and digital codes\n */ // Storage keys\nconst STORAGE_KEYS = {\n    PRODUCTS: 'alraya_products',\n    ORDERS: 'alraya_orders',\n    USERS: 'alraya_users',\n    SETTINGS: 'alraya_settings',\n    COUNTERS: 'alraya_counters'\n};\n// =====================================================\n// CORE STORAGE UTILITIES\n// =====================================================\n/**\n * Safe localStorage operations with error handling\n */ class SafeStorage {\n    static get(key, defaultValue) {\n        try {\n            if (false) {}\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : defaultValue;\n        } catch (error) {\n            console.error('Error reading from localStorage key \"'.concat(key, '\":'), error);\n            return defaultValue;\n        }\n    }\n    static set(key, value) {\n        try {\n            if (false) {}\n            localStorage.setItem(key, JSON.stringify(value));\n            return true;\n        } catch (error) {\n            console.error('Error writing to localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static remove(key) {\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            return true;\n        } catch (error) {\n            console.error('Error removing localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static clear() {\n        try {\n            if (false) {}\n            localStorage.clear();\n            return true;\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n            return false;\n        }\n    }\n}\n/**\n * Generate unique IDs\n */ function generateId() {\n    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '';\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp, \"_\").concat(random);\n}\n/**\n * Get and increment counter\n */ function getNextId(type) {\n    const counters = SafeStorage.get(STORAGE_KEYS.COUNTERS, {\n        products: 1,\n        orders: 1,\n        users: 1\n    });\n    const nextId = counters[type];\n    counters[type] = nextId + 1;\n    SafeStorage.set(STORAGE_KEYS.COUNTERS, counters);\n    return nextId;\n}\n// =====================================================\n// PRODUCT STORAGE OPERATIONS\n// =====================================================\nclass ProductStorage {\n    /**\n   * Get all products\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.PRODUCTS, []);\n    }\n    /**\n   * Get product by ID\n   */ static getById(id) {\n        const products = this.getAll();\n        return products.find((p)=>p.id === id) || null;\n    }\n    /**\n   * Get active products only\n   */ static getActive() {\n        return this.getAll().filter((p)=>p.isActive);\n    }\n    /**\n   * Get products by category\n   */ static getByCategory(category) {\n        return this.getActive().filter((p)=>p.category.toLowerCase().includes(category.toLowerCase()));\n    }\n    /**\n   * Search products\n   */ static search(query) {\n        const searchTerm = query.toLowerCase();\n        return this.getActive().filter((p)=>{\n            var _p_description, _p_tags;\n            return p.name.toLowerCase().includes(searchTerm) || ((_p_description = p.description) === null || _p_description === void 0 ? void 0 : _p_description.toLowerCase().includes(searchTerm)) || p.category.toLowerCase().includes(searchTerm) || ((_p_tags = p.tags) === null || _p_tags === void 0 ? void 0 : _p_tags.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n        });\n    }\n    /**\n   * Create new product\n   */ static create(productData) {\n        const products = this.getAll();\n        const now = new Date();\n        const newProduct = {\n            ...productData,\n            id: generateId('prod_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        products.push(newProduct);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event for real-time updates\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'create',\n                product: newProduct\n            }\n        }));\n        return newProduct;\n    }\n    /**\n   * Update existing product\n   */ static update(id, updates) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        const updatedProduct = {\n            ...products[index],\n            ...updates,\n            id,\n            updatedAt: new Date()\n        };\n        products[index] = updatedProduct;\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'update',\n                product: updatedProduct\n            }\n        }));\n        return updatedProduct;\n    }\n    /**\n   * Delete product\n   */ static delete(id) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        const deletedProduct = products[index];\n        products.splice(index, 1);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'delete',\n                product: deletedProduct\n            }\n        }));\n        return true;\n    }\n    /**\n   * Get available digital codes for a package\n   */ static getAvailableCodes(productId, packageId) {\n        const product = this.getById(productId);\n        if (!product) return [];\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return [];\n        return pkg.digitalCodes.filter((code)=>!code.used);\n    }\n    /**\n   * Assign digital code to order\n   */ static assignDigitalCode(productId, packageId, orderId) {\n        const product = this.getById(productId);\n        if (!product) return null;\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return null;\n        const availableCode = pkg.digitalCodes.find((code)=>!code.used);\n        if (!availableCode) return null;\n        // Mark code as used and assign to order\n        availableCode.used = true;\n        availableCode.assignedToOrderId = orderId;\n        availableCode.usedAt = new Date();\n        // Update product in storage\n        this.update(productId, product);\n        return availableCode;\n    }\n    /**\n   * Initialize with sample data if empty\n   */ static initializeSampleData() {\n        const existingProducts = this.getAll();\n        if (existingProducts.length > 0) return;\n        console.log('🔄 Initializing sample products...');\n        // Sample product with enhanced features\n        const sampleProduct = {\n            name: \"PUBG Mobile UC - شحن يوسي\",\n            description: \"شحن يوسي لعبة ببجي موبايل - توصيل فوري مع ضمان الجودة\",\n            category: \"ألعاب الموبايل\",\n            image: \"https://images.unsplash.com/photo-1542751371-adc38448a05e?w=500\",\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\",\n            fields: [\n                {\n                    id: \"field_1\",\n                    type: \"universal_input\",\n                    name: \"player_id\",\n                    label: \"معرف اللاعب\",\n                    placeholder: \"أدخل معرف اللاعب\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 0,\n                    validation: {}\n                },\n                {\n                    id: \"field_2\",\n                    type: \"dropdown\",\n                    name: \"server_region\",\n                    label: \"منطقة الخادم\",\n                    placeholder: \"اختر منطقة الخادم\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    validation: {},\n                    options: [\n                        {\n                            id: \"opt_1\",\n                            value: \"global\",\n                            label: \"عالمي\",\n                            sortOrder: 0,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_2\",\n                            value: \"asia\",\n                            label: \"آسيا\",\n                            sortOrder: 1,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_3\",\n                            value: \"europe\",\n                            label: \"أوروبا\",\n                            sortOrder: 2,\n                            isActive: true\n                        }\n                    ]\n                }\n            ],\n            packages: [\n                {\n                    id: \"pkg_1\",\n                    name: \"60 UC\",\n                    amount: \"60 Unknown Cash\",\n                    price: 0.99,\n                    isActive: true,\n                    sortOrder: 0,\n                    digitalCodes: [\n                        {\n                            id: \"code_1\",\n                            key: \"UC60-ABCD-1234\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_2\",\n                            key: \"UC60-EFGH-5678\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_2\",\n                    name: \"300 UC\",\n                    amount: \"300 Unknown Cash\",\n                    price: 4.99,\n                    originalPrice: 5.99,\n                    popular: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    digitalCodes: [\n                        {\n                            id: \"code_3\",\n                            key: \"UC300-IJKL-9012\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_4\",\n                            key: \"UC300-MNOP-3456\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_3\",\n                    name: \"1800 UC\",\n                    amount: \"1800 Unknown Cash\",\n                    price: 24.99,\n                    originalPrice: 29.99,\n                    description: \"أفضل قيمة - وفر 17%\",\n                    isActive: true,\n                    sortOrder: 2,\n                    digitalCodes: [\n                        {\n                            id: \"code_5\",\n                            key: \"UC1800-QRST-7890\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                }\n            ],\n            features: [\n                \"توصيل فوري\",\n                \"ضمان الجودة\",\n                \"دعم فني 24/7\",\n                \"أسعار تنافسية\"\n            ],\n            tags: [\n                \"pubg\",\n                \"mobile\",\n                \"uc\",\n                \"gaming\",\n                \"instant\"\n            ],\n            isActive: true,\n            isFeatured: true\n        };\n        this.create(sampleProduct);\n        console.log('✅ Sample product initialized successfully');\n    }\n}\n// =====================================================\n// ORDER STORAGE OPERATIONS\n// =====================================================\nclass OrderStorage {\n    /**\n   * Get all orders\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.ORDERS, []);\n    }\n    /**\n   * Get order by ID\n   */ static getById(id) {\n        const orders = this.getAll();\n        return orders.find((o)=>o.id === id) || null;\n    }\n    /**\n   * Get orders by user (using email as identifier)\n   */ static getByUser(userEmail) {\n        return this.getAll().filter((o)=>o.userDetails.email === userEmail);\n    }\n    /**\n   * Get orders by status\n   */ static getByStatus(status) {\n        return this.getAll().filter((o)=>o.status === status);\n    }\n    /**\n   * Create new order\n   */ static create(orderData) {\n        const orders = this.getAll();\n        const now = new Date();\n        const newOrder = {\n            ...orderData,\n            id: generateId('order_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        orders.push(newOrder);\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'create',\n                order: newOrder\n            }\n        }));\n        return newOrder;\n    }\n    /**\n   * Update order status\n   */ static updateStatus(id, status, notes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === id);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            status,\n            updatedAt: new Date(),\n            ...notes && {\n                notes\n            }\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'update',\n                order: updatedOrder\n            }\n        }));\n        return updatedOrder;\n    }\n    /**\n   * Add digital codes to order\n   */ static addDigitalCodes(orderId, codes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === orderId);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            digitalCodes: [\n                ...orders[index].digitalCodes || [],\n                ...codes\n            ],\n            updatedAt: new Date()\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        return updatedOrder;\n    }\n}\n// =====================================================\n// USER STORAGE OPERATIONS\n// =====================================================\nclass UserStorage {\n    /**\n   * Get all users\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.USERS, []);\n    }\n    /**\n   * Get user by email\n   */ static getByEmail(email) {\n        const users = this.getAll();\n        return users.find((u)=>u.email === email) || null;\n    }\n    /**\n   * Create or update user\n   */ static upsert(userData) {\n        const users = this.getAll();\n        const existingIndex = users.findIndex((u)=>u.email === userData.email);\n        const now = new Date();\n        if (existingIndex >= 0) {\n            // Update existing user\n            const updatedUser = {\n                ...users[existingIndex],\n                ...userData,\n                updatedAt: now\n            };\n            users[existingIndex] = updatedUser;\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return updatedUser;\n        } else {\n            // Create new user\n            const newUser = {\n                ...userData,\n                id: generateId('user_'),\n                createdAt: now,\n                updatedAt: now\n            };\n            users.push(newUser);\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return newUser;\n        }\n    }\n}\n// =====================================================\n// SETTINGS STORAGE\n// =====================================================\nclass SettingsStorage {\n    /**\n   * Get application settings\n   */ static get() {\n        return SafeStorage.get(STORAGE_KEYS.SETTINGS, {});\n    }\n    /**\n   * Update settings\n   */ static update(settings) {\n        const currentSettings = this.get();\n        const updatedSettings = {\n            ...currentSettings,\n            ...settings\n        };\n        SafeStorage.set(STORAGE_KEYS.SETTINGS, updatedSettings);\n    }\n    /**\n   * Get specific setting\n   */ static getSetting(key, defaultValue) {\n        const settings = this.get();\n        return settings[key] !== undefined ? settings[key] : defaultValue;\n    }\n    /**\n   * Set specific setting\n   */ static setSetting(key, value) {\n        this.update({\n            [key]: value\n        });\n    }\n}\n// =====================================================\n// DATABASE INITIALIZATION\n// =====================================================\n/**\n * Initialize the entire localStorage database\n */ function initializeDatabase() {\n    console.log('🔄 Initializing localStorage database...');\n    // Initialize sample data if needed\n    ProductStorage.initializeSampleData();\n    console.log('✅ Database initialized successfully');\n}\n/**\n * Clear all data (for development/testing)\n */ function clearDatabase() {\n    console.log('🗑️ Clearing all localStorage data...');\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        SafeStorage.remove(key);\n    });\n    console.log('✅ Database cleared successfully');\n}\n/**\n * Export database (for backup/migration)\n */ function exportDatabase() {\n    const data = {};\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        data[name] = SafeStorage.get(key, null);\n    });\n    return data;\n}\n/**\n * Import database (for backup/migration)\n */ function importDatabase(data) {\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        if (data[name] !== undefined) {\n            SafeStorage.set(key, data[name]);\n        }\n    });\n    console.log('✅ Database imported successfully');\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/storage/localStorage.ts\n"));

/***/ })

}]);