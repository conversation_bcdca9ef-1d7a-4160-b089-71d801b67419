"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Loader2,Package,Plus,RefreshCw,Trash2,Type,Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        discount: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"text\",\n        placeholder: \"\",\n        required: false\n    });\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleSave = async ()=>{\n        var _formData_name, _formData_category;\n        setIsLoading(true);\n        // Basic validation\n        if (!((_formData_name = formData.name) === null || _formData_name === void 0 ? void 0 : _formData_name.trim())) {\n            alert(\"يرجى إدخال اسم المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!((_formData_category = formData.category) === null || _formData_category === void 0 ? void 0 : _formData_category.trim())) {\n            alert(\"يرجى إدخال فئة المنتج\");\n            setIsLoading(false);\n            return;\n        }\n        if (!formData.packages || formData.packages.length === 0) {\n            alert(\"يرجى إضافة حزمة واحدة على الأقل\");\n            setIsLoading(false);\n            return;\n        }\n        try {\n            const productData = {\n                name: formData.name,\n                description: formData.description,\n                category: formData.category,\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: formData.fields,\n                packages: formData.packages,\n                features: formData.features,\n                tags: formData.tags,\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.updateProduct)(product.id, productData);\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_2__.createProduct)(productData);\n            }\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            alert(\"حدث خطأ أثناء حفظ المنتج\");\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 421,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 430,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 453,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 462,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    formData.image && isValidImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-lg border border-gray-600/50 bg-gray-700/30\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: formData.image,\n                                                                alt: \"صورة المنتج\",\n                                                                className: \"w-full h-auto object-cover rounded-lg\",\n                                                                style: {\n                                                                    aspectRatio: \"1\"\n                                                                },\n                                                                onError: handleImageError,\n                                                                onLoad: (e)=>{\n                                                                    const img = e.target;\n                                                                    setImageDimensions({\n                                                                        width: img.naturalWidth,\n                                                                        height: img.naturalHeight\n                                                                    });\n                                                                    setIsValidImage(true);\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/70 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-between p-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-white text-xs\",\n                                                                        children: isValidImage && imageDimensions.width > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                imageDimensions.width,\n                                                                                \" \\xd7 \",\n                                                                                imageDimensions.height,\n                                                                                \"px\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 506,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 504,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        type: \"button\",\n                                                                        variant: \"destructive\",\n                                                                        size: \"icon\",\n                                                                        className: \"h-8 w-8 rounded-full bg-red-500/80 hover:bg-red-500 shadow-md\",\n                                                                        onClick: ()=>{\n                                                                            setFormData((prev)=>({\n                                                                                    ...prev,\n                                                                                    image: \"\"\n                                                                                }));\n                                                                            setTempUrl(\"\");\n                                                                        },\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                className: \"h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 519,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"sr-only\",\n                                                                                children: \"حذف\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                                lineNumber: 520,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 509,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 503,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col space-y-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-2 space-x-reverse\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    value: tempUrl,\n                                                                    onChange: handleInputChange,\n                                                                    placeholder: \"أدخل رابط الصورة أو قم برفع صورة\",\n                                                                    className: \"flex-1 bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 529,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    type: \"button\",\n                                                                    variant: \"outline\",\n                                                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700 px-4\",\n                                                                    onClick: handleApplyUrl,\n                                                                    disabled: isTestingUrl,\n                                                                    children: [\n                                                                        isTestingUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            className: \"ml-2 h-4 w-4 animate-spin\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 543,\n                                                                            columnNumber: 25\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                            className: \"ml-2 h-4 w-4\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                            lineNumber: 545,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \"تطبيق\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        id: inputId.current,\n                                                        className: \"hidden\",\n                                                        accept: \"image/*\",\n                                                        onChange: onSelectFile\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        className: \"w-full border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                                        onClick: handleUploadButtonClick,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"ml-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 568,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \"رفع صورة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 562,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 576,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 592,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 418,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 602,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 604,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 610,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 606,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 625,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 623,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 639,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 654,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 633,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 661,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 660,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 618,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 616,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 672,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 673,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 671,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 599,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 692,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 698,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 688,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 712,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 714,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 718,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 713,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 711,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 710,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 704,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 752,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 751,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 776,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 777,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 775,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 782,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 768,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 786,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 767,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 803,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 802,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 813,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 823,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 824,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 822,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 837,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 838,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 836,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 849,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 850,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 848,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 861,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 862,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 860,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 835,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 874,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 873,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 887,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 888,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 889,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 886,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 895,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 897,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 901,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 885,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 913,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 808,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 926,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 933,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 800,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 799,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 948,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 947,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 976,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 983,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 981,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 994,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1001,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 993,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 953,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 945,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 944,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1037,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1028,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1027,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 388,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"oSWCy1Wg9EfPKrmYKV0bkkiPz3U=\");\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1149,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1158,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1167,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1148,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1182,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1227,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1226,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1232,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1234,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1235,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1210,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1181,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1241,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1246,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1247,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1248,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1249,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1245,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1243,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1254,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Loader2_Package_Plus_RefreshCw_Trash2_Type_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1266,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1261,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1253,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1146,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});