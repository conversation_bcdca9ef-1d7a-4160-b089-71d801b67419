"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/wallet/page",{

/***/ "(app-pages-browser)/./components/pages/WalletPage.tsx":
/*!*****************************************!*\
  !*** ./components/pages/WalletPage.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WalletPage: () => (/* binding */ WalletPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/wallet/WalletBalance */ \"(app-pages-browser)/./components/wallet/WalletBalance.tsx\");\n/* harmony import */ var _components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/wallet/WalletTransactions */ \"(app-pages-browser)/./components/wallet/WalletTransactions.tsx\");\n/* harmony import */ var _barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils/digitalContentUtils */ \"(app-pages-browser)/./lib/utils/digitalContentUtils.ts\");\n/* harmony import */ var _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/hooks/useChat */ \"(app-pages-browser)/./lib/hooks/useChat.ts\");\n/* __next_internal_client_entry_do_not_use__ WalletPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\r\n * Convert orders to transactions for unified display in the transactions component\r\n */ async function loadOrdersAsTransactions(userEmail) {\n    try {\n        // Import order service dynamically to avoid circular dependencies\n        const { getOrdersByUser } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_services_orderService_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/services/orderService */ \"(app-pages-browser)/./lib/services/orderService.ts\"));\n        const orders = await getOrdersByUser(userEmail);\n        return orders.map((order)=>({\n                id: \"order_\".concat(order.id),\n                userId: userEmail,\n                walletId: \"wallet_\".concat(userEmail),\n                type: \"purchase\",\n                amount: order.totalPrice,\n                currency: order.currency,\n                description: \"\\uD83D\\uDED2 \".concat(order.productName, \" - \").concat(order.packageName),\n                referenceNumber: order.id,\n                status: order.status === 'completed' ? 'completed' : order.status === 'pending' ? 'pending' : order.status === 'failed' ? 'failed' : 'pending',\n                orderId: order.id,\n                hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,\n                digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {\n                    status: order.status === 'completed' ? 'ready' : 'pending',\n                    contents: order.digitalCodes.map((code)=>({\n                            id: code.id,\n                            type: 'game_code',\n                            title: \"\".concat(order.packageName, \" - كود رقمي\"),\n                            content: code.key,\n                            instructions: \"استخدم هذا الكود في التطبيق المحدد\",\n                            isRevealed: false,\n                            deliveredAt: order.updatedAt || order.createdAt\n                        })),\n                    deliveryMethod: 'instant',\n                    lastUpdated: order.updatedAt || order.createdAt\n                } : undefined,\n                date: order.createdAt,\n                createdAt: order.createdAt,\n                updatedAt: order.updatedAt || order.createdAt\n            }));\n    } catch (error) {\n        console.error('Error converting orders to transactions:', error);\n        return [];\n    }\n}\nfunction WalletPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"wallet\") // Set to wallet tab since this is wallet page\n    ;\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter)();\n    // Use global currency context\n    const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency)();\n    // Mock user email (in real app, this would come from auth)\n    const userEmail = \"<EMAIL>\";\n    // Load user transactions (including orders converted to transactions)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WalletPage.useEffect\": ()=>{\n            const loadTransactions = {\n                \"WalletPage.useEffect.loadTransactions\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        // Load transactions from localStorage (created by order system)\n                        const localTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]');\n                        // Load orders and convert them to transactions for unified display\n                        const orderTransactions = await loadOrdersAsTransactions(userEmail);\n                        // Merge and sort by date (newest first)\n                        const allTransactions = [\n                            ...localTransactions,\n                            ...orderTransactions\n                        ].sort({\n                            \"WalletPage.useEffect.loadTransactions.allTransactions\": (a, b)=>new Date(b.date || b.createdAt).getTime() - new Date(a.date || a.createdAt).getTime()\n                        }[\"WalletPage.useEffect.loadTransactions.allTransactions\"]);\n                        setTransactions(allTransactions);\n                    } catch (error) {\n                        console.error('Error loading transactions:', error);\n                        setError('فشل في تحميل المعاملات');\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"WalletPage.useEffect.loadTransactions\"];\n            loadTransactions();\n            // Listen for real-time transaction updates (from order creation)\n            const handleTransactionsUpdated = {\n                \"WalletPage.useEffect.handleTransactionsUpdated\": (event)=>{\n                    console.log('Transactions updated:', event.detail);\n                    loadTransactions() // Reload transactions when they change\n                    ;\n                }\n            }[\"WalletPage.useEffect.handleTransactionsUpdated\"];\n            window.addEventListener('ordersUpdated', handleTransactionsUpdated);\n            window.addEventListener('transactionsUpdated', handleTransactionsUpdated);\n            return ({\n                \"WalletPage.useEffect\": ()=>{\n                    window.removeEventListener('ordersUpdated', handleTransactionsUpdated);\n                    window.removeEventListener('transactionsUpdated', handleTransactionsUpdated);\n                }\n            })[\"WalletPage.useEffect\"];\n        }\n    }[\"WalletPage.useEffect\"], [\n        userEmail\n    ]);\n    // Get chat unread count for navigation badge\n    const { unreadCount: chatUnreadCount } = (0,_lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__.useChat)({\n        userId: 'customer-demo',\n        userType: 'customer'\n    });\n    // Enhanced handler for currency change - now updates global currency context\n    const handleCurrencyChange = async (currency)=>{\n        try {\n            // Update global currency context (affects entire app)\n            await updateCurrency(currency);\n        } catch (err) {\n            console.error('Failed to update currency preference:', err);\n        }\n    };\n    // Calculate wallet statistics and balance from transactions\n    const calculateWalletBalance = ()=>{\n        // Get initial balance from localStorage (default: $100 for demo)\n        const initialBalance = parseFloat(localStorage.getItem(\"wallet_balance_\".concat(userEmail)) || '100');\n        // Calculate total spent from completed purchase transactions\n        const totalSpent = transactions.filter((transaction)=>transaction.status === 'completed' && transaction.type === 'purchase').reduce((sum, transaction)=>sum + transaction.amount, 0);\n        // Calculate total deposits from completed deposit transactions\n        const totalDeposits = transactions.filter((transaction)=>transaction.status === 'completed' && transaction.type === 'deposit').reduce((sum, transaction)=>sum + transaction.amount, 0);\n        // Current balance = Initial + Deposits - Spent\n        const currentBalance = initialBalance + totalDeposits - totalSpent;\n        return {\n            currentBalance,\n            initialBalance,\n            totalSpent,\n            totalDeposits\n        };\n    };\n    const balanceInfo = calculateWalletBalance();\n    const walletStats = {\n        currentBalance: balanceInfo.currentBalance,\n        totalSpent: balanceInfo.totalSpent,\n        totalDeposits: balanceInfo.totalDeposits,\n        totalTransactions: transactions.length,\n        completedTransactions: transactions.filter((transaction)=>transaction.status === 'completed').length,\n        pendingTransactions: transactions.filter((transaction)=>transaction.status === 'pending').length,\n        digitalCodes: transactions.filter((transaction)=>transaction.status === 'completed' && transaction.hasDigitalContent).reduce((sum, transaction)=>{\n            var _transaction_digitalContent_contents, _transaction_digitalContent;\n            return sum + (((_transaction_digitalContent = transaction.digitalContent) === null || _transaction_digitalContent === void 0 ? void 0 : (_transaction_digitalContent_contents = _transaction_digitalContent.contents) === null || _transaction_digitalContent_contents === void 0 ? void 0 : _transaction_digitalContent_contents.length) || 0);\n        }, 0)\n    };\n    // ## Handler for adding balance - navigates to checkout page\n    const handleAddBalance = ()=>{\n        router.push(\"/checkout\");\n    };\n    // Navigation handler for navbar\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n            router.refresh();\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 203,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_2__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 206,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_4__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 207,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_3__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center gap-4 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-slate-900\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"محفظتي\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"إدارة رصيدك ومعاملاتك المالية بسهولة وأمان\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletBalance__WEBPACK_IMPORTED_MODULE_7__.WalletBalance, {\n                            walletData: {\n                                balances: [\n                                    {\n                                        id: \"balance_demo\",\n                                        userId: userEmail,\n                                        currency: selectedCurrency,\n                                        amount: walletStats.currentBalance,\n                                        reservedBalance: 0,\n                                        totalDeposits: walletStats.totalSpent,\n                                        totalWithdrawals: 0,\n                                        totalPurchases: walletStats.totalSpent,\n                                        lastTransactionAt: transactions.length > 0 ? transactions[0].date || transactions[0].createdAt : undefined,\n                                        lastUpdated: new Date(),\n                                        createdAt: new Date()\n                                    }\n                                ],\n                                selectedCurrency: selectedCurrency,\n                                availableCurrencies: availableCurrencies,\n                                totalPurchases: walletStats.totalSpent,\n                                transactions: transactions\n                            },\n                            selectedCurrency: selectedCurrency,\n                            onCurrencyChange: handleCurrencyChange,\n                            onAddBalance: handleAddBalance,\n                            isLoading: isLoading\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-100\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_wallet_WalletTransactions__WEBPACK_IMPORTED_MODULE_8__.WalletTransactions, {\n                        transactions: transactions,\n                        selectedCurrency: selectedCurrency,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                        lineNumber: 266,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_5__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange,\n                walletNotificationCount: (0,_lib_utils_digitalContentUtils__WEBPACK_IMPORTED_MODULE_11__.getDigitalContentNotificationCount)(transactions),\n                unreadChatCount: chatUnreadCount\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_6__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n                lineNumber: 279,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\pages\\\\WalletPage.tsx\",\n        lineNumber: 200,\n        columnNumber: 5\n    }, this);\n}\n_s(WalletPage, \"7kane9a5Sq60rm/11PdYZorZhIM=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_9__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_10__.useCurrency,\n        _lib_hooks_useChat__WEBPACK_IMPORTED_MODULE_12__.useChat\n    ];\n});\n_c = WalletPage;\nvar _c;\n$RefreshReg$(_c, \"WalletPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/pages/WalletPage.tsx\n"));

/***/ })

});