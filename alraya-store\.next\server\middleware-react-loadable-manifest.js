self.__REACT_LOADABLE_MANIFEST="{\"components\\\\pages\\\\WalletPage.tsx -> @/lib/services/orderService\":{\"id\":\"components\\\\pages\\\\WalletPage.tsx -> @/lib/services/orderService\",\"files\":[\"static/chunks/_app-pages-browser_lib_services_orderService_ts.js\"]},\"lib\\\\services\\\\orderService.ts -> @/lib/storage/localStorage\":{\"id\":\"lib\\\\services\\\\orderService.ts -> @/lib/storage/localStorage\",\"files\":[]},\"node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\":{\"id\":\"node_modules\\\\next\\\\dist\\\\client\\\\components\\\\react-dev-overlay\\\\utils\\\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts\",\"files\":[\"static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js\"]}}"