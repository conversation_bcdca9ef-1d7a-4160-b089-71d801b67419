# Deploying to Netlify

This document provides step-by-step instructions for deploying the PUBG Account Hub website to Netlify.

## Prerequisites

- GitHub repository with your project
- Netlify account (you can sign up for free at [netlify.com](https://netlify.com))
- Node.js and npm installed

## Deployment Methods

### Method 1: Automatic Deployment via GitHub

This is the recommended approach for continuous deployment:

1. **Push your code to GitHub**:
   ```bash
   git add .
   git commit -m "Prepare for Netlify deployment"
   git push
   ```

2. **Connect to Netlify**:
   - Log in to [Netlify](https://app.netlify.com/)
   - Click "New site from Git"
   - Select GitHub and authorize Netlify
   - Choose your repository

3. **Configure build settings**:
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Advanced build settings: Add environment variables (if needed)
   - Select "Deploy site"

4. **Set up a custom domain** (optional):
   - Go to "Domain settings"
   - Click "Add custom domain"
   - Follow the instructions to configure your domain

### Method 2: Manual Deployment using Netlify CLI

If you prefer deploying from your local machine:

1. **Login to Netlify CLI**:
   ```bash
   npx netlify login
   ```

2. **Initialize your site** (first time only):
   ```bash
   npx netlify init
   ```
   - Choose "Create & configure a new site"
   - Select your team
   - Set a custom site name (optional)

3. **Deploy a preview version**:
   ```bash
   npm run netlify:deploy
   ```

4. **Deploy to production**:
   ```bash
   npm run netlify:deploy:prod
   ```

## Important Files for Netlify

- **netlify.toml**: Contains build settings, redirects, and headers
- **robots.txt**: SEO instructions for web crawlers
- **sitemap.xml**: Site structure for search engines 

## Environment Variables

Make sure to add any required environment variables in the Netlify UI:

1. Go to Site settings > Build & deploy > Environment
2. Add variables like:
   - `VITE_FIREBASE_API_KEY`
   - `VITE_OPENROUTER_API_KEY`
   - `VITE_IMGBB_API_KEY`

## Post-Deployment Checks

After deployment, verify that:

1. The site loads correctly
2. All routes work (test navigation)
3. API connections are working
4. Forms submit properly
5. SEO elements are present (use View Source)

## Troubleshooting

If you encounter issues:

- Check build logs in the Netlify dashboard
- Verify environment variables are set correctly
- Ensure the `postbuild` script ran properly
- Test locally with `npm run preview` before deploying

## Optimizing for Performance

Netlify automatically:
- Serves your site over HTTPS
- Applies cache headers (as configured in netlify.toml)
- Distributes content via CDN

## SEO Benefits of Netlify Hosting

- Fast global CDN improves page load speed
- Automatic HTTPS improves search rankings
- Proper headers and caching improve Core Web Vitals
- Sitemap.xml and robots.txt are properly served 