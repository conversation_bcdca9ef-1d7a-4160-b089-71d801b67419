"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .select(`\n      *,\n      product_packages(*),\n      custom_fields(*)\n    `)\n    .eq('is_active', filters?.isActive ?? true)\n    .order('created_at', { ascending: false })\n  \n  if (error) throw error\n  return data.map(transformProductFromDB)\n  */ // Temporary: Load from localStorage with default initialization (client-side only)\n    try {\n        (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.initializeDefaultTemplates)();\n        // On server-side, return default templates directly\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = loadProductTemplates();\n        const validProducts = Array.isArray(products) ? products : [];\n        return applyFilters(validProducts, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_0__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .select(`\n      *,\n      product_packages(*),\n      custom_fields(*)\n    `)\n    .eq('id', id)\n    .single()\n\n  if (error) throw error\n  return transformProductFromDB(data)\n  */ try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // Temporary: Load from localStorage\n        const products = await getProducts();\n        console.log(\"\\uD83D\\uDCE6 Total products available: \".concat(products.length));\n        console.log(\"\\uD83D\\uDCCB Available product IDs: \".concat(products.map((p)=>p.id).join(', ')));\n        const product = products.find((p)=>p.id === id);\n        if (product) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data: productData, error: productError } = await supabase\n    .from('products')\n    .insert({\n      name: product.name,\n      name_english: product.nameEnglish,\n      description: product.description,\n      category: product.category,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (productError) throw productError\n  \n  // Insert packages\n  if (product.packages.length > 0) {\n    const { error: packagesError } = await supabase\n      .from('product_packages')\n      .insert(product.packages.map(pkg => ({\n        product_id: productData.id,\n        name: pkg.name,\n        // ... other package fields\n      })))\n    \n    if (packagesError) throw packagesError\n  }\n  \n  // Insert custom fields\n  if (product.fields.length > 0) {\n    const { error: fieldsError } = await supabase\n      .from('custom_fields')\n      .insert(product.fields.map(field => ({\n        product_id: productData.id,\n        field_type: field.type,\n        // ... other field properties\n      })))\n    \n    if (fieldsError) throw fieldsError\n  }\n  \n  return getProductById(productData.id)\n  */ // Temporary: Save to localStorage\n    const newProduct = {\n        ...product,\n        id: generateId(),\n        createdAt: new Date(),\n        updatedAt: new Date()\n    };\n    saveProductTemplate(newProduct);\n    return newProduct;\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // ## TODO: Replace with Supabase transaction\n    /*\n  const { data, error } = await supabase\n    .from('products')\n    .update({\n      name: updates.name,\n      description: updates.description,\n      // ... other fields\n      updated_at: new Date().toISOString()\n    })\n    .eq('id', id)\n    .select()\n    .single()\n  \n  if (error) throw error\n  \n  // Update packages and fields if provided\n  // ... handle packages and fields updates\n  \n  return getProductById(id)\n  */ // Temporary: Update in localStorage\n    const products = loadProductTemplates();\n    const index = products.findIndex((p)=>p.id === id);\n    if (index === -1) throw new Error('Product not found');\n    const updatedProduct = {\n        ...products[index],\n        ...updates,\n        updatedAt: new Date()\n    };\n    saveProductTemplate(updatedProduct);\n    return updatedProduct;\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // ## TODO: Replace with Supabase cascade delete\n    /*\n  const { error } = await supabase\n    .from('products')\n    .delete()\n    .eq('id', id)\n  \n  if (error) throw error\n  */ // Temporary: Remove from localStorage\n    deleteProductTemplate(id);\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    // ## TODO: Replace with Supabase query\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .select('*')\n    .eq('product_id', productId)\n    .eq('is_active', true)\n    .order('sort_order')\n  \n  if (error) throw error\n  return data.map(transformPackageFromDB)\n  */ const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages) || [];\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ })

});