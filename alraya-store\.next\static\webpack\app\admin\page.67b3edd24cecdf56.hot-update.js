"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            discount: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"text\",\n            placeholder: \"\",\n            required: false\n        });\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            discount: pkg.discount || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        if (!packageForm.name.trim()) {\n            alert(\"يرجى إدخال اسم الحزمة\");\n            return;\n        }\n        if (packageForm.price <= 0) {\n            alert(\"يرجى إدخال سعر صحيح\");\n            return;\n        }\n        // Process digital codes\n        const digitalCodes = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key,\n                used: false,\n                assignedToOrderId: null\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: packageForm.name,\n            amount: packageForm.amount,\n            price: packageForm.price,\n            originalPrice: packageForm.originalPrice || undefined,\n            discount: packageForm.discount || undefined,\n            description: packageForm.description || undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            digitalCodes\n        };\n        setFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذه الحزمة؟\")) {\n            setFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 504,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 512,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 497,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 537,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 538,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 536,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 548,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 559,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 590,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 592,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 589,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 606,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 616,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 622,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 605,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 534,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 633,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 634,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 640,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 636,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 654,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 655,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 660,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 653,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 665,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 675,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 684,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 678,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 652,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 692,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 690,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 648,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 646,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 702,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 703,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 709,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 704,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 720,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 722,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 719,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 748,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 743,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 754,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 741,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 764,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 773,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 767,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 757,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 736,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 734,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 782,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 783,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 789,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 781,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 806,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 807,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 805,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 811,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 812,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 810,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 853,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 854,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 852,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 867,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 868,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 866,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 892,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 890,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 865,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 904,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 903,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 917,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 916,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 923,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 925,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 926,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 927,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 924,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 922,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 931,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 915,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 949,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 941,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 838,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 956,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 963,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 955,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 830,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 829,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 977,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 976,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 986,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 987,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 998,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1005,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1006,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1012,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1013,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1011,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1024,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1023,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1039,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1046,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1038,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 975,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 974,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1061,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1060,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1059,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1067,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1058,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1057,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"8Y1hvcddtm4Q8zWI4nTeUx6y9nU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1197,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1209,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1212,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1257,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1255,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1262,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1263,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1265,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1240,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1211,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1208,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1271,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1274,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1276,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1277,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1278,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1279,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1275,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1273,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1291,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1176,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/types/index.ts":
/*!****************************!*\
  !*** ./lib/types/index.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateDiscountPercentage: () => (/* binding */ calculateDiscountPercentage),\n/* harmony export */   enhancePackageWithDiscountInfo: () => (/* binding */ enhancePackageWithDiscountInfo),\n/* harmony export */   validateDiscountPricing: () => (/* binding */ validateDiscountPricing)\n/* harmony export */ });\n// Common types used across the application\n// =====================================================\n// UTILITY FUNCTIONS FOR PACKAGE CALCULATIONS\n// =====================================================\n/**\r\n * Calculate discount percentage from original and current price\r\n */ const calculateDiscountPercentage = (originalPrice, currentPrice)=>{\n    if (!originalPrice || originalPrice <= 0 || !currentPrice || currentPrice <= 0) {\n        return 0;\n    }\n    if (originalPrice <= currentPrice) {\n        return 0;\n    }\n    return Math.round((originalPrice - currentPrice) / originalPrice * 100);\n};\n/**\r\n * Validate discount pricing logic\r\n */ const validateDiscountPricing = (originalPrice, currentPrice)=>{\n    // If no original price, no discount validation needed\n    if (!originalPrice) {\n        return {\n            isValid: true\n        };\n    }\n    // If original price is provided, current price must be provided and valid\n    if (!currentPrice || currentPrice <= 0) {\n        return {\n            isValid: false,\n            error: \"يرجى إدخال السعر الحالي\"\n        };\n    }\n    // Original price must be greater than current price for discount to make sense\n    if (originalPrice <= currentPrice) {\n        return {\n            isValid: false,\n            error: \"السعر الأصلي يجب أن يكون أكبر من السعر الحالي\"\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\r\n * Enhanced ProductPackage with computed properties\r\n */ const enhancePackageWithDiscountInfo = (pkg)=>{\n    const discountPercentage = pkg.originalPrice ? calculateDiscountPercentage(pkg.originalPrice, pkg.price) : 0;\n    return {\n        ...pkg,\n        discountPercentage,\n        hasDiscount: discountPercentage > 0\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/types/index.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils/validation.ts":
/*!*********************************!*\
  !*** ./lib/utils/validation.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitizeDigitalCode: () => (/* binding */ sanitizeDigitalCode),\n/* harmony export */   sanitizeNumber: () => (/* binding */ sanitizeNumber),\n/* harmony export */   sanitizeText: () => (/* binding */ sanitizeText),\n/* harmony export */   validateDigitalCodes: () => (/* binding */ validateDigitalCodes),\n/* harmony export */   validateDropdownOptions: () => (/* binding */ validateDropdownOptions),\n/* harmony export */   validateFieldLabel: () => (/* binding */ validateFieldLabel),\n/* harmony export */   validatePackageName: () => (/* binding */ validatePackageName),\n/* harmony export */   validatePackagePrice: () => (/* binding */ validatePackagePrice),\n/* harmony export */   validatePriceDistribution: () => (/* binding */ validatePriceDistribution),\n/* harmony export */   validateProductCategory: () => (/* binding */ validateProductCategory),\n/* harmony export */   validateProductData: () => (/* binding */ validateProductData),\n/* harmony export */   validateProductName: () => (/* binding */ validateProductName)\n/* harmony export */ });\n/**\n * Comprehensive validation utilities for product creation form\n * Provides input sanitization, business rule validation, and error handling\n */ // =====================================================\n// INPUT SANITIZATION\n// =====================================================\n/**\n * Sanitize text input to prevent XSS and clean up whitespace\n */ const sanitizeText = (input)=>{\n    if (!input) return '';\n    return input.trim().replace(/\\s+/g, ' ') // Replace multiple spaces with single space\n    .replace(/[<>]/g, '') // Remove potential HTML tags\n    .substring(0, 1000) // Limit length to prevent abuse\n    ;\n};\n/**\n * Sanitize and validate digital code\n */ const sanitizeDigitalCode = (code)=>{\n    if (!code) return '';\n    return code.trim().toUpperCase().replace(/[^A-Z0-9\\-_]/g, '') // Only allow alphanumeric, dash, underscore\n    .substring(0, 50) // Reasonable code length limit\n    ;\n};\n/**\n * Sanitize numeric input\n */ const sanitizeNumber = (input)=>{\n    const num = typeof input === 'string' ? parseFloat(input) : input;\n    return isNaN(num) ? 0 : Math.max(0, num);\n};\n// =====================================================\n// VALIDATION FUNCTIONS\n// =====================================================\n/**\n * Validate product name\n */ const validateProductName = (name)=>{\n    const sanitized = sanitizeText(name);\n    if (!sanitized) {\n        return {\n            isValid: false,\n            error: 'يرجى إدخال اسم المنتج'\n        };\n    }\n    if (sanitized.length < 3) {\n        return {\n            isValid: false,\n            error: 'اسم المنتج يجب أن يكون 3 أحرف على الأقل'\n        };\n    }\n    if (sanitized.length > 100) {\n        return {\n            isValid: false,\n            error: 'اسم المنتج يجب أن يكون أقل من 100 حرف'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate product category\n */ const validateProductCategory = (category)=>{\n    const sanitized = sanitizeText(category);\n    if (!sanitized) {\n        return {\n            isValid: false,\n            error: 'يرجى إدخال فئة المنتج'\n        };\n    }\n    if (sanitized.length < 2) {\n        return {\n            isValid: false,\n            error: 'فئة المنتج يجب أن تكون حرفين على الأقل'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate package name\n */ const validatePackageName = (name)=>{\n    const sanitized = sanitizeText(name);\n    if (!sanitized) {\n        return {\n            isValid: false,\n            error: 'يرجى إدخال اسم الحزمة'\n        };\n    }\n    if (sanitized.length < 2) {\n        return {\n            isValid: false,\n            error: 'اسم الحزمة يجب أن يكون حرفين على الأقل'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate package price\n */ const validatePackagePrice = (price)=>{\n    if (!price || price <= 0) {\n        return {\n            isValid: false,\n            error: 'يرجى إدخال سعر صحيح أكبر من صفر'\n        };\n    }\n    if (price > 10000) {\n        return {\n            isValid: false,\n            error: 'السعر يجب أن يكون أقل من 10,000'\n        };\n    }\n    if (price < 0.01) {\n        return {\n            isValid: false,\n            error: 'السعر يجب أن يكون 0.01 على الأقل'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate digital codes array\n */ const validateDigitalCodes = (codes)=>{\n    if (!codes || codes.length === 0) {\n        return {\n            isValid: true,\n            sanitizedCodes: []\n        } // Digital codes are optional\n        ;\n    }\n    const sanitizedCodes = codes.map(sanitizeDigitalCode).filter(Boolean) // Remove empty codes\n    ;\n    if (sanitizedCodes.length === 0) {\n        return {\n            isValid: true,\n            sanitizedCodes: []\n        };\n    }\n    // Check for duplicates\n    const duplicates = sanitizedCodes.filter((code, index)=>sanitizedCodes.indexOf(code) !== index);\n    if (duplicates.length > 0) {\n        return {\n            isValid: false,\n            error: \"أكواد مكررة: \".concat(duplicates.join(', ')),\n            duplicates: [\n                ...new Set(duplicates)\n            ]\n        };\n    }\n    // Check code format\n    const invalidCodes = sanitizedCodes.filter((code)=>code.length < 3 || code.length > 50);\n    if (invalidCodes.length > 0) {\n        return {\n            isValid: false,\n            error: \"أكواد غير صالحة (يجب أن تكون بين 3-50 حرف): \".concat(invalidCodes.join(', '))\n        };\n    }\n    return {\n        isValid: true,\n        sanitizedCodes\n    };\n};\n/**\n * Validate field label\n */ const validateFieldLabel = (label)=>{\n    const sanitized = sanitizeText(label);\n    if (!sanitized) {\n        return {\n            isValid: false,\n            error: 'يرجى إدخال تسمية الحقل'\n        };\n    }\n    if (sanitized.length < 2) {\n        return {\n            isValid: false,\n            error: 'تسمية الحقل يجب أن تكون حرفين على الأقل'\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate dropdown options\n */ const validateDropdownOptions = (options)=>{\n    if (!options || options.length === 0) {\n        return {\n            isValid: false,\n            error: 'يرجى إضافة خيار واحد على الأقل للقائمة المنسدلة'\n        };\n    }\n    // Check for empty labels\n    const emptyOptions = options.filter((opt)=>!sanitizeText(opt.label));\n    if (emptyOptions.length > 0) {\n        return {\n            isValid: false,\n            error: 'جميع خيارات القائمة يجب أن تحتوي على نص'\n        };\n    }\n    // Check for duplicates\n    const labels = options.map((opt)=>sanitizeText(opt.label));\n    const duplicates = labels.filter((label, index)=>labels.indexOf(label) !== index);\n    if (duplicates.length > 0) {\n        return {\n            isValid: false,\n            error: \"خيارات مكررة: \".concat([\n                ...new Set(duplicates)\n            ].join(', ')),\n            duplicates: [\n                ...new Set(duplicates)\n            ]\n        };\n    }\n    return {\n        isValid: true\n    };\n};\n/**\n * Validate complete product data\n */ const validateProductData = (data)=>{\n    const errors = [];\n    const warnings = [];\n    // Validate basic info\n    const nameValidation = validateProductName(data.name || '');\n    if (!nameValidation.isValid) {\n        errors.push(nameValidation.error);\n    }\n    const categoryValidation = validateProductCategory(data.category || '');\n    if (!categoryValidation.isValid) {\n        errors.push(categoryValidation.error);\n    }\n    // Validate packages\n    if (!data.packages || data.packages.length === 0) {\n        errors.push('يرجى إضافة حزمة واحدة على الأقل');\n    } else {\n        data.packages.forEach((pkg, index)=>{\n            const nameValidation = validatePackageName(pkg.name);\n            if (!nameValidation.isValid) {\n                errors.push(\"الحزمة \".concat(index + 1, \": \").concat(nameValidation.error));\n            }\n            const priceValidation = validatePackagePrice(pkg.price);\n            if (!priceValidation.isValid) {\n                errors.push(\"الحزمة \".concat(index + 1, \": \").concat(priceValidation.error));\n            }\n        });\n        // Check for duplicate package names\n        const packageNames = data.packages.map((pkg)=>sanitizeText(pkg.name));\n        const duplicateNames = packageNames.filter((name, index)=>packageNames.indexOf(name) !== index);\n        if (duplicateNames.length > 0) {\n            errors.push(\"أسماء حزم مكررة: \".concat([\n                ...new Set(duplicateNames)\n            ].join(', ')));\n        }\n    }\n    // Validate fields\n    if (data.fields && data.fields.length > 0) {\n        data.fields.forEach((field, index)=>{\n            const labelValidation = validateFieldLabel(field.label);\n            if (!labelValidation.isValid) {\n                errors.push(\"الحقل \".concat(index + 1, \": \").concat(labelValidation.error));\n            }\n            // Validate dropdown options if it's a dropdown field\n            if (field.type === 'dropdown' && field.options) {\n                const optionsValidation = validateDropdownOptions(field.options);\n                if (!optionsValidation.isValid) {\n                    errors.push(\"الحقل \".concat(index + 1, \": \").concat(optionsValidation.error));\n                }\n            }\n        });\n    }\n    // Add warnings\n    if (!data.description || data.description.trim().length < 10) {\n        warnings.push('يُنصح بإضافة وصف مفصل للمنتج');\n    }\n    if (!data.image) {\n        warnings.push('يُنصح بإضافة صورة للمنتج');\n    }\n    return {\n        isValid: errors.length === 0,\n        errors,\n        warnings\n    };\n};\n// =====================================================\n// BUSINESS RULE VALIDATION\n// =====================================================\n/**\n * Check if packages have reasonable price distribution\n */ const validatePriceDistribution = (packages)=>{\n    const warnings = [];\n    if (packages.length < 2) {\n        return {\n            isValid: true,\n            warnings\n        };\n    }\n    const prices = packages.map((pkg)=>pkg.price).sort((a, b)=>a - b);\n    const minPrice = prices[0];\n    const maxPrice = prices[prices.length - 1];\n    // Check if price range is too narrow or too wide\n    if (maxPrice / minPrice > 50) {\n        warnings.push('فرق الأسعار بين الحزم كبير جداً - قد يسبب ارتباك للعملاء');\n    }\n    if (maxPrice / minPrice < 1.5 && packages.length > 3) {\n        warnings.push('الأسعار متقاربة جداً - قد لا يكون هناك حافز لشراء الحزم الأكبر');\n    }\n    return {\n        isValid: true,\n        warnings\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/validation.ts\n"));

/***/ })

});