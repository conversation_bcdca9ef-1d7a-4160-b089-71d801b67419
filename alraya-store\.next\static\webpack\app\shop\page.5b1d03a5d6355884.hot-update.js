"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./components/layout/DesktopFooter.tsx":
/*!*********************************************!*\
  !*** ./components/layout/DesktopFooter.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DesktopFooter: () => (/* binding */ DesktopFooter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ DesktopFooter auto */ \nvar _s = $RefreshSig$();\n\n\nfunction DesktopFooter(param) {\n    let { activeTab, onTabChange } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat)();\n    // Standardized navigation items matching mobile\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 24,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 30,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                lineNumber: 48,\n                columnNumber: 13\n            }, this),\n            label: \"الدعم الفني\",\n            action: ()=>onTabChange(\"support\") // Routes to contact page, consistent with mobile\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"hidden lg:block relative z-10 bg-slate-800/50 backdrop-blur-xl border-t border-slate-700/50 mt-12 transition-all duration-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-8 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-8\",\n                    children: navItems.map((param, index)=>{\n                        let { id, icon, label, action } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: action,\n                            className: \"relative flex flex-col items-center gap-2 p-4 rounded-2xl transition-all duration-300 transform hover:scale-105 active:scale-95 min-w-[5rem] animate-in fade-in-50 slide-in-from-bottom-2 \".concat(activeTab === id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 shadow-lg shadow-yellow-500/25 ring-2 ring-yellow-400/20 scale-105\" : \"text-slate-400 hover:text-white hover:bg-white/10 hover:shadow-md hover:ring-1 hover:ring-white/10\"),\n                            style: {\n                                animationDelay: \"\".concat(index * 100, \"ms\")\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        icon,\n                                        activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -inset-1 bg-white/20 rounded-full animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium transition-all duration-300 whitespace-nowrap \".concat(activeTab === id ? \"text-slate-900 font-semibold\" : \"text-slate-400\"),\n                                    children: label\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                activeTab === id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-slate-900 rounded-full animate-pulse shadow-lg\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-2xl bg-white/5 opacity-0 transition-opacity duration-300 hover:opacity-100\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 rounded-2xl overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-white/10 rounded-2xl transform scale-0 transition-transform duration-300 active:scale-100\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, id, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-0.5 bg-gradient-to-r from-transparent via-yellow-400/30 to-transparent\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\DesktopFooter.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(DesktopFooter, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_1__.useGlobalChat\n    ];\n});\n_c = DesktopFooter;\nvar _c;\n$RefreshReg$(_c, \"DesktopFooter\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/DesktopFooter.tsx\n"));

/***/ })

});