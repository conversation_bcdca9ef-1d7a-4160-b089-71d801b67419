"use client"

import React, { useState, useEffect, use } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AppHeader } from "@/components/layout/AppHeader"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import {
  CheckCircle,
  Clock,
  AlertCircle,
  Copy,
  Download,
  ArrowLeft,
  Package,
  CreditCard,
  User,
  Calendar,
  Key
} from "lucide-react"
import { Order } from "@/lib/types"
import { getOrderById } from "@/lib/services/orderService"
import { useToast } from "@/hooks/use-toast"

interface OrderPageProps {
  params: Promise<{
    orderId: string
  }>
}

export default function OrderPage({ params }: OrderPageProps) {
  // Unwrap params using React.use()
  const { orderId } = use(params)
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("wallet")
  const router = useRouter()
  const { toast } = useToast()

  // Load order details
  useEffect(() => {
    const loadOrder = async () => {
      try {
        setLoading(true)
        const orderData = await getOrderById(orderId)

        if (!orderData) {
          setError("الطلب غير موجود")
          return
        }

        setOrder(orderData)
      } catch (error) {
        console.error("Error loading order:", error)
        setError("حدث خطأ أثناء تحميل الطلب")
      } finally {
        setLoading(false)
      }
    }

    loadOrder()
  }, [orderId])

  // Navigation handler
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
    } else if (tab === "support") {
      router.push("/contact")
    } else {
      setActiveTab(tab)
    }
  }

  // Copy digital code to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "تم النسخ",
        description: "تم نسخ الكود إلى الحافظة",
      })
    } catch (error) {
      toast({
        title: "خطأ في النسخ",
        description: "فشل في نسخ الكود",
        variant: "destructive",
      })
    }
  }

  // Get status color and icon
  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'completed':
        return {
          color: 'bg-green-500',
          icon: <CheckCircle className="h-4 w-4" />,
          text: 'مكتمل',
          bgColor: 'bg-green-500/10 border-green-500/20'
        }
      case 'pending':
        return {
          color: 'bg-yellow-500',
          icon: <Clock className="h-4 w-4" />,
          text: 'قيد المعالجة',
          bgColor: 'bg-yellow-500/10 border-yellow-500/20'
        }
      case 'failed':
        return {
          color: 'bg-red-500',
          icon: <AlertCircle className="h-4 w-4" />,
          text: 'فشل',
          bgColor: 'bg-red-500/10 border-red-500/20'
        }
      case 'cancelled':
        return {
          color: 'bg-gray-500',
          icon: <AlertCircle className="h-4 w-4" />,
          text: 'ملغي',
          bgColor: 'bg-gray-500/10 border-gray-500/20'
        }
      default:
        return {
          color: 'bg-gray-500',
          icon: <Clock className="h-4 w-4" />,
          text: status,
          bgColor: 'bg-gray-500/10 border-gray-500/20'
        }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
        <AppHeader />
        <main className="container mx-auto px-4 py-8 pb-32 lg:pb-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto mb-4"></div>
              <p className="text-slate-300">جاري تحميل تفاصيل الطلب...</p>
            </div>
          </div>
        </main>
        <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
        <AppHeader />
        <main className="container mx-auto px-4 py-8 pb-32 lg:pb-8">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <AlertCircle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h2 className="text-xl font-bold mb-2">خطأ في تحميل الطلب</h2>
              <p className="text-slate-300 mb-4">{error || "الطلب غير موجود"}</p>
              <Button onClick={() => router.push("/wallet")} className="bg-yellow-500 hover:bg-yellow-600">
                <ArrowLeft className="h-4 w-4 mr-2" />
                العودة إلى المحفظة
              </Button>
            </div>
          </div>
        </main>
        <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
        <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
      </div>
    )
  }

  const statusDisplay = getStatusDisplay(order.status)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      <AppHeader />
      
      <main className="container mx-auto px-4 py-8 pb-32 lg:pb-8 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push("/wallet")}
            className="text-slate-300 hover:text-white"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            العودة إلى المحفظة
          </Button>
        </div>

        {/* Order Status */}
        <Card className={`${statusDisplay.bgColor} border backdrop-blur-sm`}>
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className={`p-2 rounded-full ${statusDisplay.color} text-white`}>
                {statusDisplay.icon}
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">طلب #{order.id.slice(-8)}</h1>
                <p className="text-slate-300">الحالة: {statusDisplay.text}</p>
              </div>
            </div>
            
            {order.status === 'completed' && (
              <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-4">
                <div className="flex items-center gap-2 text-green-300">
                  <CheckCircle className="h-5 w-5" />
                  <span className="font-medium">تم إكمال طلبك بنجاح!</span>
                </div>
                <p className="text-green-200 text-sm mt-1">
                  يمكنك الآن استخدام الأكواد الرقمية المرفقة أدناه
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Order Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Product Information */}
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Package className="h-5 w-5" />
                تفاصيل المنتج
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-slate-400 text-sm">المنتج</p>
                <p className="text-white font-medium">{order.productName}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">الحزمة</p>
                <p className="text-white font-medium">{order.packageName}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">الكمية</p>
                <p className="text-white font-medium">{order.quantity}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">السعر الإجمالي</p>
                <p className="text-white font-bold text-lg">{order.totalPrice} {order.currency}</p>
              </div>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <User className="h-5 w-5" />
                معلومات العميل
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-slate-400 text-sm">الاسم</p>
                <p className="text-white font-medium">{order.userDetails.name}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">البريد الإلكتروني</p>
                <p className="text-white font-medium">{order.userDetails.email}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">رقم الهاتف</p>
                <p className="text-white font-medium">{order.userDetails.phone}</p>
              </div>
              <div>
                <p className="text-slate-400 text-sm">تاريخ الطلب</p>
                <p className="text-white font-medium">
                  {new Date(order.createdAt).toLocaleDateString('ar-SA')}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Digital Codes */}
        {order.digitalCodes && order.digitalCodes.length > 0 && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-white">
                <Key className="h-5 w-5" />
                الأكواد الرقمية ({order.digitalCodes.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {order.digitalCodes.map((code, index) => (
                  <div
                    key={code.id}
                    className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg border border-slate-600/50"
                  >
                    <div className="flex items-center gap-3">
                      <div className="bg-yellow-500/20 p-2 rounded-lg">
                        <Key className="h-4 w-4 text-yellow-400" />
                      </div>
                      <div>
                        <p className="text-white font-mono text-lg">{code.key}</p>
                        <p className="text-slate-400 text-sm">كود رقم {index + 1}</p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(code.key)}
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              
              <div className="mt-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <p className="text-blue-300 text-sm">
                  💡 <strong>تعليمات الاستخدام:</strong> انسخ الكود واستخدمه في اللعبة أو التطبيق المطلوب. 
                  كل كود يُستخدم مرة واحدة فقط.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Custom Fields */}
        {order.customFields && Object.keys(order.customFields).length > 0 && (
          <Card className="bg-slate-800/50 border-slate-700/50 backdrop-blur-sm">
            <CardHeader>
              <CardTitle className="text-white">معلومات إضافية</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3">
                {Object.entries(order.customFields).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center p-3 bg-slate-700/30 rounded-lg">
                    <span className="text-slate-400">{key}:</span>
                    <span className="text-white font-medium">{String(value)}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </main>

      <MobileNavigation activeTab={activeTab} onTabChange={handleTabChange} />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
