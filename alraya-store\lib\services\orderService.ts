/**
 * Order Service
 * 
 * Handles all order-related operations using localStorage
 */

import { Order, ProductFormData, ProductTemplate, DigitalCode, CheckoutUserDetails, Transaction } from '@/lib/types'
import { OrderStorage, ProductStorage } from '@/lib/storage/localStorage'
import { assignDigitalCode } from './productService'

/**
 * Create order from product form data
 */
export async function createOrderFromProduct(
  formData: ProductFormData,
  userDetails: CheckoutUserDetails
): Promise<Order> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 300))

  try {
    // Validate that the selected package is still available
    const product = ProductStorage.getById(formData.templateId)
    if (!product) {
      throw new Error('Product not found')
    }

    const selectedPackage = product.packages.find(pkg => pkg.id === formData.selectedPackage.id)
    if (!selectedPackage || !selectedPackage.isActive) {
      throw new Error('Selected package is no longer available')
    }

    // Enhanced availability check for different product types
    if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {
      // Digital products with codes - check code availability
      const availableCodes = selectedPackage.digitalCodes.filter(code => !code.used)
      if (availableCodes.length < formData.quantity) {
        throw new Error(`Only ${availableCodes.length} codes available, but ${formData.quantity} requested`)
      }
    } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {
      // Products with manual quantity limits
      if (selectedPackage.quantityLimit < formData.quantity) {
        throw new Error(`Only ${selectedPackage.quantityLimit} items available, but ${formData.quantity} requested`)
      }
    }
    // For unlimited digital products/services (no codes, no limits), no availability check needed

    // Create the order
    const orderData: Omit<Order, 'id' | 'createdAt' | 'updatedAt'> = {
      productId: formData.templateId,
      productName: product.name,
      packageId: formData.selectedPackage.id,
      packageName: formData.selectedPackage.name,
      quantity: formData.quantity,
      unitPrice: formData.selectedPackage.price,
      totalPrice: formData.totalPrice,
      currency: formData.currency,
      status: 'pending',
      userDetails,
      customFields: formData.customFields,
      digitalCodes: [],
      processingType: product.processingType,
      deliveryType: product.deliveryType,
      timeline: [
        {
          id: `timeline_${Date.now()}`,
          status: 'pending',
          timestamp: new Date(),
          message: 'Order created and awaiting processing',
          isVisible: true
        }
      ]
    }

    const newOrder = OrderStorage.create(orderData)

    // Auto-assign digital codes if it's an instant processing product
    if (product.processingType === 'instant' && selectedPackage.digitalCodes) {
      // Ensure the package has available digital codes
      await ensureDigitalCodesAvailable(formData.templateId, formData.selectedPackage.id)
      await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity)
    }

    // Create corresponding wallet transaction for unified display
    await createWalletTransactionFromOrder(newOrder)

    console.log(`✅ Created order: ${newOrder.id} for product: ${product.name}`)
    return newOrder

  } catch (error) {
    console.error('Error creating order:', error)
    throw error
  }
}

/**
 * Ensure digital codes are available for a package
 */
async function ensureDigitalCodesAvailable(productId: string, packageId: string): Promise<void> {
  const availableCodes = await getAvailableCodes(productId, packageId)

  if (availableCodes.length === 0) {
    console.log(`⚠️ No digital codes available for package ${packageId}, adding sample codes...`)

    // Add sample digital codes to the package
    const { ProductStorage } = await import('@/lib/storage/localStorage')
    const product = ProductStorage.getById(productId)

    if (product) {
      const pkg = product.packages.find(p => p.id === packageId)
      if (pkg) {
        // Add sample digital codes
        const sampleCodes = [
          {
            id: `code_${Date.now()}_1`,
            key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
            used: false,
            assignedToOrderId: null,
            createdAt: new Date()
          },
          {
            id: `code_${Date.now()}_2`,
            key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
            used: false,
            assignedToOrderId: null,
            createdAt: new Date()
          },
          {
            id: `code_${Date.now()}_3`,
            key: `SAMPLE-${Math.random().toString(36).substr(2, 4).toUpperCase()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`,
            used: false,
            assignedToOrderId: null,
            createdAt: new Date()
          }
        ]

        pkg.digitalCodes = [...(pkg.digitalCodes || []), ...sampleCodes]
        ProductStorage.update(productId, product)

        console.log(`✅ Added ${sampleCodes.length} sample digital codes to package ${packageId}`)
      }
    }
  }
}

/**
 * Assign digital codes to an order
 */
export async function assignDigitalCodesToOrder(
  orderId: string,
  productId: string,
  packageId: string,
  quantity: number
): Promise<DigitalCode[]> {
  const assignedCodes: DigitalCode[] = []

  try {
    for (let i = 0; i < quantity; i++) {
      const code = await assignDigitalCode(productId, packageId, orderId)
      if (code) {
        assignedCodes.push(code)
      } else {
        throw new Error(`Failed to assign digital code ${i + 1} of ${quantity}`)
      }
    }

    // Add codes to order
    if (assignedCodes.length > 0) {
      OrderStorage.addDigitalCodes(orderId, assignedCodes)
      
      // Update order status to completed if all codes assigned
      OrderStorage.updateStatus(orderId, 'completed', `${assignedCodes.length} digital codes assigned`)
    }

    return assignedCodes

  } catch (error) {
    console.error('Error assigning digital codes:', error)
    
    // Update order status to failed
    OrderStorage.updateStatus(orderId, 'failed', `Failed to assign digital codes: ${error}`)
    
    throw error
  }
}

/**
 * Get all orders
 */
export async function getOrders(): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getAll()
}

/**
 * Get order by ID
 */
export async function getOrderById(id: string): Promise<Order | null> {
  await new Promise(resolve => setTimeout(resolve, 50))
  return OrderStorage.getById(id)
}

/**
 * Get orders by user email
 */
export async function getOrdersByUser(userEmail: string): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getByUser(userEmail)
}

/**
 * Get orders by status
 */
export async function getOrdersByStatus(status: string): Promise<Order[]> {
  await new Promise(resolve => setTimeout(resolve, 100))
  return OrderStorage.getByStatus(status)
}

/**
 * Update order status
 */
export async function updateOrderStatus(
  orderId: string,
  status: string,
  notes?: string
): Promise<Order | null> {
  await new Promise(resolve => setTimeout(resolve, 100))

  try {
    const updatedOrder = OrderStorage.updateStatus(orderId, status, notes)
    if (updatedOrder) {
      console.log(`✅ Updated order ${orderId} status to: ${status}`)

      // Update corresponding wallet transaction
      await updateWalletTransactionFromOrder(updatedOrder)
    }
    return updatedOrder
  } catch (error) {
    console.error('Error updating order status:', error)
    return null
  }
}

/**
 * Process pending orders (for admin use)
 */
export async function processPendingOrders(): Promise<{
  processed: number
  failed: number
  errors: string[]
}> {
  const pendingOrders = await getOrdersByStatus('pending')
  let processed = 0
  let failed = 0
  const errors: string[] = []

  for (const order of pendingOrders) {
    try {
      // Try to assign digital codes if not already assigned
      if (order.digitalCodes.length === 0 && order.packageId) {
        await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity)
        processed++
      } else {
        // Just mark as completed if codes already assigned
        await updateOrderStatus(order.id, 'completed', 'Order processed successfully')
        processed++
      }
    } catch (error) {
      failed++
      errors.push(`Order ${order.id}: ${error}`)
      await updateOrderStatus(order.id, 'failed', `Processing failed: ${error}`)
    }
  }

  return { processed, failed, errors }
}

/**
 * Get order statistics
 */
export async function getOrderStats(): Promise<{
  total: number
  pending: number
  completed: number
  failed: number
  totalRevenue: number
}> {
  const orders = await getOrders()
  
  const stats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    completed: orders.filter(o => o.status === 'completed').length,
    failed: orders.filter(o => o.status === 'failed').length,
    totalRevenue: orders
      .filter(o => o.status === 'completed')
      .reduce((sum, o) => sum + o.totalPrice, 0)
  }

  return stats
}

/**
 * Cancel order (if still pending)
 */
export async function cancelOrder(orderId: string, reason?: string): Promise<boolean> {
  const order = await getOrderById(orderId)
  if (!order || order.status !== 'pending') {
    return false
  }

  const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user')
  return !!updated
}

/**
 * Refund order (release digital codes back to pool)
 */
export async function refundOrder(orderId: string, reason?: string): Promise<boolean> {
  const order = await getOrderById(orderId)
  if (!order) return false

  try {
    // Release digital codes back to the product
    if (order.digitalCodes.length > 0) {
      const product = ProductStorage.getById(order.productId)
      if (product) {
        const pkg = product.packages.find(p => p.id === order.packageId)
        if (pkg && pkg.digitalCodes) {
          // Mark codes as unused and remove order assignment
          order.digitalCodes.forEach(orderCode => {
            const productCode = pkg.digitalCodes!.find(pc => pc.id === orderCode.id)
            if (productCode) {
              productCode.used = false
              productCode.assignedToOrderId = null
              productCode.usedAt = undefined
            }
          })
          
          // Update product in storage
          ProductStorage.update(order.productId, product)
        }
      }
    }

    // Update order status
    await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded')
    
    return true
  } catch (error) {
    console.error('Error refunding order:', error)
    return false
  }
}

/**
 * Create a wallet transaction from an order for unified display in the wallet
 */
async function createWalletTransactionFromOrder(order: Order): Promise<void> {
  try {
    const transaction: Omit<Transaction, 'id' | 'date'> = {
      userId: order.userDetails.email,
      walletId: `wallet_${order.userDetails.email}`,
      type: "purchase",
      amount: order.totalPrice,
      currency: order.currency,
      description: `🛒 ${order.productName} - ${order.packageName}`,
      referenceNumber: order.id,
      status: order.status === 'completed' ? 'completed' :
              order.status === 'pending' ? 'pending' :
              order.status === 'failed' ? 'failed' : 'pending',
      orderId: order.id,
      hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,
      digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {
        status: order.status === 'completed' ? 'ready' : 'pending',
        contents: order.digitalCodes.map(code => ({
          id: code.id,
          type: 'game_code',
          title: `${order.packageName} - كود رقمي`,
          content: code.key,
          instructions: "استخدم هذا الكود في التطبيق المحدد",
          isRevealed: false,
          deliveredAt: order.updatedAt || order.createdAt
        })),
        deliveryMethod: 'instant',
        lastUpdated: order.updatedAt || order.createdAt
      } : undefined,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt || order.createdAt
    }

    // Create full transaction with ID and date
    const fullTransaction: Transaction = {
      ...transaction,
      id: `txn_order_${order.id}`,
      date: order.createdAt
    }

    // Save to localStorage for wallet display
    const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]')

    // Check if transaction already exists to avoid duplicates
    const existingIndex = existingTransactions.findIndex((t: Transaction) => t.orderId === order.id)
    if (existingIndex >= 0) {
      // Update existing transaction
      existingTransactions[existingIndex] = fullTransaction
    } else {
      // Add new transaction
      existingTransactions.unshift(fullTransaction)
    }

    localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions))

    // Dispatch event to notify wallet page of transaction update
    window.dispatchEvent(new CustomEvent('transactionsUpdated', {
      detail: { transaction: fullTransaction, order }
    }))

    console.log(`✅ Created wallet transaction for order: ${order.id}`)
  } catch (error) {
    console.error('Error creating wallet transaction from order:', error)
  }
}

/**
 * Update wallet transaction when order status changes
 */
async function updateWalletTransactionFromOrder(order: Order): Promise<void> {
  try {
    const existingTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]')

    // Find the transaction for this order
    const transactionIndex = existingTransactions.findIndex((t: Transaction) => t.orderId === order.id)

    if (transactionIndex >= 0) {
      // Update the existing transaction
      const updatedTransaction: Transaction = {
        ...existingTransactions[transactionIndex],
        status: order.status === 'completed' ? 'completed' :
                order.status === 'pending' ? 'pending' :
                order.status === 'failed' ? 'failed' :
                order.status === 'cancelled' ? 'cancelled' : 'pending',
        hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,
        digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {
          status: order.status === 'completed' ? 'ready' : 'pending',
          contents: order.digitalCodes.map(code => ({
            id: code.id,
            type: 'game_code',
            title: `${order.packageName} - كود رقمي`,
            content: code.key,
            instructions: "استخدم هذا الكود في التطبيق المحدد",
            isRevealed: false,
            deliveredAt: order.updatedAt || order.createdAt
          })),
          deliveryMethod: 'instant',
          lastUpdated: order.updatedAt || order.createdAt
        } : undefined,
        updatedAt: order.updatedAt || order.createdAt
      }

      existingTransactions[transactionIndex] = updatedTransaction
      localStorage.setItem('walletTransactions', JSON.stringify(existingTransactions))

      // Dispatch event to notify wallet page
      window.dispatchEvent(new CustomEvent('transactionsUpdated', {
        detail: { transaction: updatedTransaction, order }
      }))

      console.log(`✅ Updated wallet transaction for order: ${order.id}`)
    } else {
      // Transaction doesn't exist, create it
      await createWalletTransactionFromOrder(order)
    }
  } catch (error) {
    console.error('Error updating wallet transaction from order:', error)
  }
}
