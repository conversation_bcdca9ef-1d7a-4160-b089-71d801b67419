/**
 * Comprehensive validation utilities for product creation form
 * Provides input sanitization, business rule validation, and error handling
 */

import { ProductTemplate, ProductPackage, DynamicField, DigitalCode, DropdownOption } from '@/lib/types'

// =====================================================
// INPUT SANITIZATION
// =====================================================

/**
 * Sanitize text input to prevent XSS and clean up whitespace
 */
export const sanitizeText = (input: string): string => {
  if (!input) return ''
  
  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .substring(0, 1000) // Limit length to prevent abuse
}

/**
 * Sanitize and validate digital code
 */
export const sanitizeDigitalCode = (code: string): string => {
  if (!code) return ''
  
  return code
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9\-_]/g, '') // Only allow alphanumeric, dash, underscore
    .substring(0, 50) // Reasonable code length limit
}

/**
 * Sanitize numeric input
 */
export const sanitizeNumber = (input: string | number): number => {
  const num = typeof input === 'string' ? parseFloat(input) : input
  return isNaN(num) ? 0 : Math.max(0, num)
}

// =====================================================
// VALIDATION FUNCTIONS
// =====================================================

/**
 * Validate product name
 */
export const validateProductName = (name: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(name)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال اسم المنتج' }
  }
  
  if (sanitized.length < 3) {
    return { isValid: false, error: 'اسم المنتج يجب أن يكون 3 أحرف على الأقل' }
  }
  
  if (sanitized.length > 100) {
    return { isValid: false, error: 'اسم المنتج يجب أن يكون أقل من 100 حرف' }
  }
  
  return { isValid: true }
}

/**
 * Validate product category
 */
export const validateProductCategory = (category: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(category)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال فئة المنتج' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'فئة المنتج يجب أن تكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate package name
 */
export const validatePackageName = (name: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(name)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال اسم الحزمة' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'اسم الحزمة يجب أن يكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate package price
 */
export const validatePackagePrice = (price: number): { isValid: boolean; error?: string } => {
  if (!price || price <= 0) {
    return { isValid: false, error: 'يرجى إدخال سعر صحيح أكبر من صفر' }
  }
  
  if (price > 10000) {
    return { isValid: false, error: 'السعر يجب أن يكون أقل من 10,000' }
  }
  
  if (price < 0.01) {
    return { isValid: false, error: 'السعر يجب أن يكون 0.01 على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate digital codes array
 */
export const validateDigitalCodes = (codes: string[]): { 
  isValid: boolean; 
  error?: string; 
  sanitizedCodes?: string[];
  duplicates?: string[];
} => {
  if (!codes || codes.length === 0) {
    return { isValid: true, sanitizedCodes: [] } // Digital codes are optional
  }
  
  const sanitizedCodes = codes
    .map(sanitizeDigitalCode)
    .filter(Boolean) // Remove empty codes
  
  if (sanitizedCodes.length === 0) {
    return { isValid: true, sanitizedCodes: [] }
  }
  
  // Check for duplicates
  const duplicates = sanitizedCodes.filter((code, index) => 
    sanitizedCodes.indexOf(code) !== index
  )
  
  if (duplicates.length > 0) {
    return { 
      isValid: false, 
      error: `أكواد مكررة: ${duplicates.join(', ')}`,
      duplicates: [...new Set(duplicates)]
    }
  }
  
  // Check code format
  const invalidCodes = sanitizedCodes.filter(code => 
    code.length < 3 || code.length > 50
  )
  
  if (invalidCodes.length > 0) {
    return { 
      isValid: false, 
      error: `أكواد غير صالحة (يجب أن تكون بين 3-50 حرف): ${invalidCodes.join(', ')}`
    }
  }
  
  return { isValid: true, sanitizedCodes }
}

/**
 * Validate field label
 */
export const validateFieldLabel = (label: string): { isValid: boolean; error?: string } => {
  const sanitized = sanitizeText(label)
  
  if (!sanitized) {
    return { isValid: false, error: 'يرجى إدخال تسمية الحقل' }
  }
  
  if (sanitized.length < 2) {
    return { isValid: false, error: 'تسمية الحقل يجب أن تكون حرفين على الأقل' }
  }
  
  return { isValid: true }
}

/**
 * Validate dropdown options
 */
export const validateDropdownOptions = (options: DropdownOption[]): { 
  isValid: boolean; 
  error?: string;
  duplicates?: string[];
} => {
  if (!options || options.length === 0) {
    return { isValid: false, error: 'يرجى إضافة خيار واحد على الأقل للقائمة المنسدلة' }
  }
  
  // Check for empty labels
  const emptyOptions = options.filter(opt => !sanitizeText(opt.label))
  if (emptyOptions.length > 0) {
    return { isValid: false, error: 'جميع خيارات القائمة يجب أن تحتوي على نص' }
  }
  
  // Check for duplicates
  const labels = options.map(opt => sanitizeText(opt.label))
  const duplicates = labels.filter((label, index) => labels.indexOf(label) !== index)
  
  if (duplicates.length > 0) {
    return { 
      isValid: false, 
      error: `خيارات مكررة: ${[...new Set(duplicates)].join(', ')}`,
      duplicates: [...new Set(duplicates)]
    }
  }
  
  return { isValid: true }
}

/**
 * Validate complete product data
 */
export const validateProductData = (data: Partial<ProductTemplate>): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} => {
  const errors: string[] = []
  const warnings: string[] = []
  
  // Validate basic info
  const nameValidation = validateProductName(data.name || '')
  if (!nameValidation.isValid) {
    errors.push(nameValidation.error!)
  }
  
  const categoryValidation = validateProductCategory(data.category || '')
  if (!categoryValidation.isValid) {
    errors.push(categoryValidation.error!)
  }
  
  // Validate packages
  if (!data.packages || data.packages.length === 0) {
    errors.push('يرجى إضافة حزمة واحدة على الأقل')
  } else {
    data.packages.forEach((pkg, index) => {
      const nameValidation = validatePackageName(pkg.name)
      if (!nameValidation.isValid) {
        errors.push(`الحزمة ${index + 1}: ${nameValidation.error}`)
      }
      
      const priceValidation = validatePackagePrice(pkg.price)
      if (!priceValidation.isValid) {
        errors.push(`الحزمة ${index + 1}: ${priceValidation.error}`)
      }
    })
    
    // Check for duplicate package names
    const packageNames = data.packages.map(pkg => sanitizeText(pkg.name))
    const duplicateNames = packageNames.filter((name, index) => packageNames.indexOf(name) !== index)
    if (duplicateNames.length > 0) {
      errors.push(`أسماء حزم مكررة: ${[...new Set(duplicateNames)].join(', ')}`)
    }
  }
  
  // Validate fields
  if (data.fields && data.fields.length > 0) {
    data.fields.forEach((field, index) => {
      const labelValidation = validateFieldLabel(field.label)
      if (!labelValidation.isValid) {
        errors.push(`الحقل ${index + 1}: ${labelValidation.error}`)
      }
      
      // Validate dropdown options if it's a dropdown field
      if (field.type === 'dropdown' && field.options) {
        const optionsValidation = validateDropdownOptions(field.options)
        if (!optionsValidation.isValid) {
          errors.push(`الحقل ${index + 1}: ${optionsValidation.error}`)
        }
      }
    })
  }
  
  // Add warnings
  if (!data.description || data.description.trim().length < 10) {
    warnings.push('يُنصح بإضافة وصف مفصل للمنتج')
  }
  
  if (!data.image) {
    warnings.push('يُنصح بإضافة صورة للمنتج')
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// =====================================================
// BUSINESS RULE VALIDATION
// =====================================================

/**
 * Check if packages have reasonable price distribution
 */
export const validatePriceDistribution = (packages: ProductPackage[]): {
  isValid: boolean;
  warnings: string[];
} => {
  const warnings: string[] = []
  
  if (packages.length < 2) {
    return { isValid: true, warnings }
  }
  
  const prices = packages.map(pkg => pkg.price).sort((a, b) => a - b)
  const minPrice = prices[0]
  const maxPrice = prices[prices.length - 1]
  
  // Check if price range is too narrow or too wide
  if (maxPrice / minPrice > 50) {
    warnings.push('فرق الأسعار بين الحزم كبير جداً - قد يسبب ارتباك للعملاء')
  }
  
  if (maxPrice / minPrice < 1.5 && packages.length > 3) {
    warnings.push('الأسعار متقاربة جداً - قد لا يكون هناك حافز لشراء الحزم الأكبر')
  }
  
  return { isValid: true, warnings }
}
