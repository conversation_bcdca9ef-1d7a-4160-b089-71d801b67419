"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/page",{

/***/ "(app-pages-browser)/./components/admin/SimpleProductForm.tsx":
/*!************************************************!*\
  !*** ./components/admin/SimpleProductForm.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SimpleProductForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/type.js\");\n/* harmony import */ var _barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Crop,Edit,Key,Package,Plus,Trash2,Type,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crop.js\");\n/* harmony import */ var _lib_types__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/types */ \"(app-pages-browser)/./lib/types/index.ts\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/use-toast */ \"(app-pages-browser)/./components/ui/use-toast.ts\");\n/* harmony import */ var _ImageUploader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./ImageUploader */ \"(app-pages-browser)/./components/admin/ImageUploader.tsx\");\n/* harmony import */ var _lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils/validation */ \"(app-pages-browser)/./lib/utils/validation.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction SimpleProductForm(param) {\n    let { product, onSave, onCancel, isEditing = false } = param;\n    var _formData_tags, _formData_packages, _formData_fields;\n    _s();\n    const { toast } = (0,_components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    // Core form state\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasUnsavedChanges, setHasUnsavedChanges] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [validationErrors, setValidationErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [validationWarnings, setValidationWarnings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Dialog states\n    const [isPackageDialogOpen, setIsPackageDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isFieldDialogOpen, setIsFieldDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingPackageIndex, setEditingPackageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingFieldIndex, setEditingFieldIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    var _product_isActive;\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: (product === null || product === void 0 ? void 0 : product.name) || \"\",\n        description: (product === null || product === void 0 ? void 0 : product.description) || \"\",\n        category: (product === null || product === void 0 ? void 0 : product.category) || \"\",\n        tags: (product === null || product === void 0 ? void 0 : product.tags) || [],\n        image: (product === null || product === void 0 ? void 0 : product.image) || \"\",\n        packages: (product === null || product === void 0 ? void 0 : product.packages) || [],\n        fields: (product === null || product === void 0 ? void 0 : product.fields) || [],\n        features: (product === null || product === void 0 ? void 0 : product.features) || [],\n        isActive: (_product_isActive = product === null || product === void 0 ? void 0 : product.isActive) !== null && _product_isActive !== void 0 ? _product_isActive : true,\n        isFeatured: (product === null || product === void 0 ? void 0 : product.isFeatured) || false,\n        deliveryType: (product === null || product === void 0 ? void 0 : product.deliveryType) || \"code_based\",\n        productType: (product === null || product === void 0 ? void 0 : product.productType) || \"digital\",\n        processingType: (product === null || product === void 0 ? void 0 : product.processingType) || \"instant\"\n    });\n    // Package dialog form state (updated for new discount system)\n    const [packageForm, setPackageForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        amount: \"\",\n        price: 0,\n        originalPrice: 0,\n        description: \"\",\n        popular: false,\n        digitalCodes: \"\"\n    });\n    // Field dialog form state (updated to support dropdown fields)\n    const [fieldForm, setFieldForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        label: \"\",\n        type: \"universal_input\",\n        placeholder: \"\",\n        required: false,\n        options: []\n    });\n    // Dropdown options management\n    const [newOptionText, setNewOptionText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Image upload state (temporary fix for the error)\n    const [tempUrl, setTempUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isValidImage, setIsValidImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isTestingUrl, setIsTestingUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageDimensions, setImageDimensions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isImageCropDialogOpen, setIsImageCropDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSrc, setImageSrc] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imagePreview, setImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0,\n        width: 0,\n        height: 0\n    });\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const inputId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"image-upload-\".concat(Math.random().toString(36).substr(2, 9)));\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Track unsaved changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SimpleProductForm.useEffect\": ()=>{\n            const handleBeforeUnload = {\n                \"SimpleProductForm.useEffect.handleBeforeUnload\": (e)=>{\n                    if (hasUnsavedChanges) {\n                        e.preventDefault();\n                        e.returnValue = '';\n                    }\n                }\n            }[\"SimpleProductForm.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"SimpleProductForm.useEffect\": ()=>window.removeEventListener('beforeunload', handleBeforeUnload)\n            })[\"SimpleProductForm.useEffect\"];\n        }\n    }[\"SimpleProductForm.useEffect\"], [\n        hasUnsavedChanges\n    ]);\n    // Mark form as changed when data updates\n    const updateFormData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"SimpleProductForm.useCallback[updateFormData]\": (updater)=>{\n            setFormData(updater);\n            setHasUnsavedChanges(true);\n        }\n    }[\"SimpleProductForm.useCallback[updateFormData]\"], []);\n    const resetForm = ()=>{\n        setFormData({\n            name: \"\",\n            description: \"\",\n            category: \"\",\n            tags: [],\n            image: \"\",\n            packages: [],\n            fields: [],\n            features: [],\n            isActive: true,\n            isFeatured: false,\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\"\n        });\n        setTempUrl(\"\");\n        setHasUnsavedChanges(false);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n    };\n    // Temporary handlers for the old image upload (will be replaced)\n    const handleInputChange = (e)=>{\n        setTempUrl(e.target.value);\n        setIsValidImage(true);\n    };\n    const handleApplyUrl = async ()=>{\n        // Temporary implementation\n        setFormData((prev)=>({\n                ...prev,\n                image: tempUrl\n            }));\n    };\n    const handleImageError = ()=>{\n        setIsValidImage(false);\n    };\n    const handleUploadButtonClick = ()=>{\n    // Temporary implementation\n    };\n    const onSelectFile = ()=>{\n    // Temporary implementation\n    };\n    // Image cropping handlers\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleMove = (e)=>{\n        if (!isDragging) return;\n        e.preventDefault();\n        const clientX = 'touches' in e ? e.touches[0].clientX : e.clientX;\n        const clientY = 'touches' in e ? e.touches[0].clientY : e.clientY;\n        const deltaX = clientX - dragStart.x;\n        const deltaY = clientY - dragStart.y;\n        setCropArea((prev)=>({\n                ...prev,\n                x: Math.max(0, Math.min(imageSize.width - prev.width, prev.x + deltaX)),\n                y: Math.max(0, Math.min(imageSize.height - prev.height, prev.y + deltaY))\n            }));\n        setDragStart({\n            x: clientX,\n            y: clientY\n        });\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    // Handle image crop completion\n    const handleImageCrop = (croppedImageUrl)=>{\n        setFormData((prev)=>({\n                ...prev,\n                image: croppedImageUrl\n            }));\n        setIsImageCropDialogOpen(false);\n        setImagePreview(null);\n    };\n    const handleSave = async ()=>{\n        setIsLoading(true);\n        setValidationErrors([]);\n        setValidationWarnings([]);\n        try {\n            var _formData_packages, _formData_fields, _formData_features, _formData_tags;\n            // Comprehensive validation\n            const validation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateProductData)(formData);\n            if (!validation.isValid) {\n                setValidationErrors(validation.errors);\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"خطأ في البيانات\",\n                    description: \"يرجى تصحيح \".concat(validation.errors.length, \" خطأ قبل الحفظ\"),\n                    variant: \"destructive\"\n                });\n                setIsLoading(false);\n                return;\n            }\n            // Show warnings if any\n            if (validation.warnings.length > 0) {\n                setValidationWarnings(validation.warnings);\n                toast({\n                    title: \"تحذيرات\",\n                    description: \"\".concat(validation.warnings.length, \" تحذير - يمكنك المتابعة أو تحسين البيانات\"),\n                    variant: \"default\"\n                });\n            }\n            // Enhance packages with discount calculations\n            const enhancedPackages = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.map((pkg, index)=>({\n                    ...pkg,\n                    sortOrder: index,\n                    ...(0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.enhancePackageWithDiscountInfo)(pkg)\n                }))) || [];\n            // Prepare product data\n            const productData = {\n                name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.name),\n                description: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.description || \"\"),\n                category: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(formData.category),\n                image: formData.image,\n                deliveryType: formData.deliveryType,\n                productType: formData.productType,\n                processingType: formData.processingType,\n                fields: ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.map((field, index)=>({\n                        ...field,\n                        sortOrder: index,\n                        label: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(field.label),\n                        name: field.name || \"field_\".concat(Date.now(), \"_\").concat(index)\n                    }))) || [],\n                packages: enhancedPackages,\n                features: ((_formData_features = formData.features) === null || _formData_features === void 0 ? void 0 : _formData_features.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                tags: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.map(_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)) || [],\n                isActive: formData.isActive,\n                isFeatured: formData.isFeatured,\n                createdBy: undefined // TODO: Get from auth\n            };\n            let savedProduct;\n            if (isEditing && product) {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.updateProduct)(product.id, productData);\n                toast({\n                    title: \"تم التحديث بنجاح\",\n                    description: \"تم تحديث المنتج بنجاح\"\n                });\n            } else {\n                savedProduct = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_3__.createProduct)(productData);\n                toast({\n                    title: \"تم الإنشاء بنجاح\",\n                    description: \"تم إنشاء المنتج بنجاح\"\n                });\n            }\n            setHasUnsavedChanges(false);\n            onSave(savedProduct);\n        } catch (error) {\n            console.error(\"Error saving product:\", error);\n            toast({\n                title: \"خطأ في الحفظ\",\n                description: \"حدث خطأ أثناء حفظ المنتج. يرجى المحاولة مرة أخرى.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Reset package form\n    const resetPackageForm = ()=>{\n        setPackageForm({\n            name: \"\",\n            amount: \"\",\n            price: 0,\n            originalPrice: 0,\n            description: \"\",\n            popular: false,\n            digitalCodes: \"\"\n        });\n    };\n    // Reset field form\n    const resetFieldForm = ()=>{\n        setFieldForm({\n            label: \"\",\n            type: \"universal_input\",\n            placeholder: \"\",\n            required: false,\n            options: []\n        });\n        setNewOptionText(\"\");\n    };\n    // Open package dialog for creating new package\n    const openPackageDialog = ()=>{\n        resetPackageForm();\n        setEditingPackageIndex(null);\n        setIsPackageDialogOpen(true);\n    };\n    // Open package dialog for editing existing package\n    const editPackage = (index)=>{\n        var _pkg_digitalCodes;\n        const pkg = formData.packages[index];\n        setPackageForm({\n            name: pkg.name,\n            amount: pkg.amount,\n            price: pkg.price,\n            originalPrice: pkg.originalPrice || 0,\n            description: pkg.description || \"\",\n            popular: pkg.popular || false,\n            digitalCodes: ((_pkg_digitalCodes = pkg.digitalCodes) === null || _pkg_digitalCodes === void 0 ? void 0 : _pkg_digitalCodes.map((code)=>code.key).join('\\n')) || \"\"\n        });\n        setEditingPackageIndex(index);\n        setIsPackageDialogOpen(true);\n    };\n    // Save package from dialog\n    const savePackage = ()=>{\n        var _formData_packages;\n        // Validate package name\n        const nameValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validatePackageName)(packageForm.name);\n        if (!nameValidation.isValid) {\n            toast({\n                title: \"خطأ في اسم الحزمة\",\n                description: nameValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate package price\n        const priceValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validatePackagePrice)(packageForm.price);\n        if (!priceValidation.isValid) {\n            toast({\n                title: \"خطأ في السعر\",\n                description: priceValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Validate discount pricing if original price is provided\n        if (packageForm.originalPrice > 0) {\n            const discountValidation = (0,_lib_types__WEBPACK_IMPORTED_MODULE_2__.validateDiscountPricing)(packageForm.originalPrice, packageForm.price);\n            if (!discountValidation.isValid) {\n                toast({\n                    title: \"خطأ في الخصم\",\n                    description: discountValidation.error,\n                    variant: \"destructive\"\n                });\n                return;\n            }\n        }\n        // Process and validate digital codes\n        const codeLines = packageForm.digitalCodes.split('\\n').map((line)=>line.trim()).filter(Boolean);\n        const codesValidation = (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.validateDigitalCodes)(codeLines);\n        if (!codesValidation.isValid) {\n            toast({\n                title: \"خطأ في الأكواد الرقمية\",\n                description: codesValidation.error,\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create digital codes with proper structure\n        const digitalCodes = (codesValidation.sanitizedCodes || []).map((key, i)=>({\n                id: \"\".concat(Date.now(), \"-\").concat(i),\n                key: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeDigitalCode)(key),\n                used: false,\n                assignedToOrderId: null,\n                createdAt: new Date()\n            }));\n        const newPackage = {\n            id: editingPackageIndex !== null ? formData.packages[editingPackageIndex].id : Date.now().toString(),\n            name: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.name),\n            amount: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.amount),\n            price: (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeNumber)(packageForm.price),\n            originalPrice: packageForm.originalPrice > 0 ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeNumber)(packageForm.originalPrice) : undefined,\n            description: packageForm.description ? (0,_lib_utils_validation__WEBPACK_IMPORTED_MODULE_8__.sanitizeText)(packageForm.description) : undefined,\n            popular: packageForm.popular,\n            isActive: true,\n            sortOrder: editingPackageIndex !== null ? editingPackageIndex : ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n            digitalCodes\n        };\n        updateFormData((prev)=>{\n            const packages = [\n                ...prev.packages || []\n            ];\n            if (editingPackageIndex !== null) {\n                packages[editingPackageIndex] = newPackage;\n            } else {\n                packages.push(newPackage);\n            }\n            return {\n                ...prev,\n                packages\n            };\n        });\n        toast({\n            title: \"تم الحفظ\",\n            description: editingPackageIndex !== null ? \"تم تحديث الحزمة بنجاح\" : \"تم إضافة الحزمة بنجاح\"\n        });\n        setIsPackageDialogOpen(false);\n        resetPackageForm();\n    };\n    // Remove package\n    const removePackage = (index)=>{\n        var _formData_packages_index, _formData_packages;\n        const packageName = ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : (_formData_packages_index = _formData_packages[index]) === null || _formData_packages_index === void 0 ? void 0 : _formData_packages_index.name) || \"الحزمة \".concat(index + 1);\n        if (confirm('هل أنت متأكد من حذف \"'.concat(packageName, '\"؟\\n\\nسيتم حذف جميع الأكواد الرقمية المرتبطة بها أيضاً.'))) {\n            updateFormData((prev)=>{\n                var _prev_packages;\n                return {\n                    ...prev,\n                    packages: ((_prev_packages = prev.packages) === null || _prev_packages === void 0 ? void 0 : _prev_packages.filter((_, i)=>i !== index)) || []\n                };\n            });\n            toast({\n                title: \"تم الحذف\",\n                description: 'تم حذف \"'.concat(packageName, '\" بنجاح')\n            });\n        }\n    };\n    // Open field dialog for creating new field\n    const openFieldDialog = ()=>{\n        resetFieldForm();\n        setEditingFieldIndex(null);\n        setIsFieldDialogOpen(true);\n    };\n    // Open field dialog for editing existing field\n    const editField = (index)=>{\n        const field = formData.fields[index];\n        setFieldForm({\n            label: field.label,\n            type: field.type,\n            placeholder: field.placeholder || \"\",\n            required: field.required,\n            options: field.options || []\n        });\n        setEditingFieldIndex(index);\n        setIsFieldDialogOpen(true);\n    };\n    // Save field from dialog\n    const saveField = ()=>{\n        if (!fieldForm.label.trim()) {\n            alert(\"يرجى إدخال تسمية الحقل\");\n            return;\n        }\n        const newField = {\n            id: editingFieldIndex !== null ? formData.fields[editingFieldIndex].id : Date.now().toString(),\n            type: fieldForm.type,\n            name: editingFieldIndex !== null ? formData.fields[editingFieldIndex].name : \"field_\".concat(Date.now()),\n            label: fieldForm.label,\n            placeholder: fieldForm.placeholder,\n            required: fieldForm.required,\n            isActive: true,\n            validation: {}\n        };\n        setFormData((prev)=>{\n            const fields = [\n                ...prev.fields || []\n            ];\n            if (editingFieldIndex !== null) {\n                fields[editingFieldIndex] = newField;\n            } else {\n                fields.push(newField);\n            }\n            return {\n                ...prev,\n                fields\n            };\n        });\n        setIsFieldDialogOpen(false);\n        resetFieldForm();\n    };\n    // Remove field\n    const removeField = (index)=>{\n        if (confirm(\"هل أنت متأكد من حذف هذا الحقل؟\")) {\n            setFormData((prev)=>{\n                var _prev_fields;\n                return {\n                    ...prev,\n                    fields: ((_prev_fields = prev.fields) === null || _prev_fields === void 0 ? void 0 : _prev_fields.filter((_, i)=>i !== index)) || []\n                };\n            });\n        }\n    };\n    var _formData_isActive;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-br from-gray-900 to-gray-800 backdrop-blur-md rounded-2xl max-w-6xl w-full max-h-[95vh] md:max-h-[90vh] overflow-y-auto border border-gray-700/30 shadow-2xl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 border-b border-gray-700/30 bg-gradient-to-r from-gray-800/50 to-gray-700/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 551,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-white\",\n                                            children: isEditing ? \"تعديل المنتج\" : \"إنشاء منتج جديد\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400 text-sm\",\n                                            children: isEditing ? \"قم بتحديث معلومات المنتج\" : \"أضف منتج جديد إلى المتجر\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 554,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                            variant: \"outline\",\n                            size: \"sm\",\n                            onClick: onCancel,\n                            className: \"border-gray-600 text-gray-400 hover:bg-gray-700 hover:text-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this),\n                                \"إلغاء\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 563,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 548,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6 md:p-8 space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-gray-800/40 to-gray-700/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-600/20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-xl font-semibold text-white mb-6 flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"المعلومات الأساسية\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 578,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"اسم المنتج *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.name || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    name: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"أدخل اسم المنتج\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium mb-3 text-white\",\n                                                        children: \"الفئة *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        value: formData.category || \"\",\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    category: e.target.value\n                                                                })),\n                                                        className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                        placeholder: \"مثل: MOBA, RPG, باتل رويال\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 600,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"الوصف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                value: formData.description || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            description: e.target.value\n                                                        })),\n                                                rows: 4,\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400 resize-none\",\n                                                placeholder: \"وصف المنتج\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 612,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 610,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"العلامات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 622,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: ((_formData_tags = formData.tags) === null || _formData_tags === void 0 ? void 0 : _formData_tags.join(\", \")) || \"\",\n                                                onChange: (e)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            tags: e.target.value.split(\",\").map((tag)=>tag.trim()).filter(Boolean)\n                                                        })),\n                                                className: \"w-full bg-gray-700/50 backdrop-blur-sm border border-gray-600/50 rounded-xl px-4 py-3 focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all duration-300 text-white placeholder-gray-400\",\n                                                placeholder: \"شائع, مميز, جديد (مفصولة بفاصلة)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 623,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 621,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium mb-3 text-white\",\n                                                children: \"صورة الغلاف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 641,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ImageUploader__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                currentImage: formData.image || \"\",\n                                                onImageChanged: (url)=>setFormData((prev)=>({\n                                                            ...prev,\n                                                            image: url\n                                                        })),\n                                                label: \"صورة المنتج\",\n                                                placeholderText: \"أدخل رابط صورة المنتج أو قم برفع صورة\",\n                                                aspectRatio: 1,\n                                                maxFileSize: 10,\n                                                showUrlInput: true,\n                                                className: \"space-y-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 640,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-6 pt-4 border-t border-gray-600/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: formData.isFeatured || false,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isFeatured: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 658,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج مميز\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 664,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 657,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"flex items-center gap-3 text-white cursor-pointer\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        checked: (_formData_isActive = formData.isActive) !== null && _formData_isActive !== void 0 ? _formData_isActive : true,\n                                                        onChange: (e)=>setFormData((prev)=>({\n                                                                    ...prev,\n                                                                    isActive: e.target.checked\n                                                                })),\n                                                        className: \"w-4 h-4 rounded border-gray-600 bg-gray-700 text-purple-600 focus:ring-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 667,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"منتج نشط\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 673,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 585,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 683,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحزم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_packages = formData.packages) === null || _formData_packages === void 0 ? void 0 : _formData_packages.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 685,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 681,\n                                columnNumber: 11\n                            }, this),\n                            formData.packages && formData.packages.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.packages.map((pkg, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-purple-500/30 transition-all duration-300\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                className: \"text-lg font-semibold text-white mb-1\",\n                                                                children: pkg.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-purple-300 font-bold text-xl\",\n                                                                children: [\n                                                                    \"$\",\n                                                                    pkg.price\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            pkg.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-300 text-sm\",\n                                                                children: pkg.amount\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 708,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            pkg.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-400 text-sm mt-2\",\n                                                                children: pkg.description\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 711,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 704,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            pkg.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-yellow-500/20 text-yellow-300 px-2 py-1 rounded-full text-xs\",\n                                                                children: \"شائع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 716,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>editPackage(index),\n                                                                className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"w-3 h-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                        lineNumber: 726,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"تعديل\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 720,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                onClick: ()=>removePackage(index),\n                                                                className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 735,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 729,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 714,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 19\n                                            }, this),\n                                            pkg.digitalCodes && pkg.digitalCodes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2 mt-3 p-3 bg-blue-500/10 rounded-lg border border-blue-500/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"w-4 h-4 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 742,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-blue-300 text-sm\",\n                                                        children: [\n                                                            pkg.digitalCodes.length,\n                                                            \" كود رقمي متاح\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                        lineNumber: 743,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 741,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, pkg.id, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 753,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حزم بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 754,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openPackageDialog,\n                                        variant: \"outline\",\n                                        className: \"border-purple-600 text-purple-400 hover:bg-purple-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 760,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حزمة\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 755,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 752,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 771,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"text-xl font-semibold text-white\",\n                                                children: \"الحقول المخصصة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-400\",\n                                                children: [\n                                                    \"(\",\n                                                    ((_formData_fields = formData.fields) === null || _formData_fields === void 0 ? void 0 : _formData_fields.length) || 0,\n                                                    \")\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 773,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 770,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 779,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"إضافة حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 775,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 769,\n                                columnNumber: 11\n                            }, this),\n                            formData.fields && formData.fields.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid gap-4\",\n                                children: formData.fields.map((field, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-gray-800/50 to-gray-700/50 backdrop-blur-sm rounded-xl p-6 border border-gray-600/30 hover:border-blue-500/30 transition-all duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                            className: \"text-lg font-semibold text-white mb-1\",\n                                                            children: field.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 793,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center gap-4 text-sm text-gray-400\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-blue-500/20 text-blue-300 px-2 py-1 rounded-full\",\n                                                                    children: field.type === \"text\" ? \"نص\" : field.type === \"email\" ? \"بريد إلكتروني\" : \"رقم\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                field.required && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"bg-red-500/20 text-red-300 px-2 py-1 rounded-full\",\n                                                                    children: \"مطلوب\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        field.placeholder && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-400 text-sm mt-2\",\n                                                            children: [\n                                                                '\"',\n                                                                field.placeholder,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 805,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 792,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>editField(index),\n                                                            className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3 mr-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                    lineNumber: 815,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"تعديل\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 809,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeField(index),\n                                                            className: \"border-red-600 text-red-400 hover:bg-red-600/10\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"w-3 h-3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                                lineNumber: 824,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 818,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 808,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 791,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, field.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 787,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 785,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12 bg-gray-800/30 rounded-xl border-2 border-dashed border-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-12 h-12 text-gray-500 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 833,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mb-4\",\n                                        children: \"لم يتم إضافة أي حقول مخصصة بعد\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 834,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                        onClick: openFieldDialog,\n                                        variant: \"outline\",\n                                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"إضافة أول حقل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 835,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 768,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4 pt-8 border-t border-gray-600/30\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                onClick: handleSave,\n                                disabled: isLoading,\n                                className: \"flex-1 bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 disabled:from-gray-600 disabled:to-gray-600 text-white py-4 text-lg font-semibold shadow-lg\",\n                                size: \"lg\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 857,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"جاري الحفظ...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 858,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 856,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 862,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: isEditing ? \"تحديث المنتج\" : \"إنشاء المنتج\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 861,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 849,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                variant: \"outline\",\n                                onClick: onCancel,\n                                disabled: isLoading,\n                                className: \"flex-1 sm:flex-none border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white py-4 px-8 text-lg\",\n                                size: \"lg\",\n                                children: \"إلغاء\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 848,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 575,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isPackageDialogOpen,\n                onOpenChange: setIsPackageDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-2xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-5 h-5 text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 884,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingPackageIndex !== null ? \"تعديل الحزمة\" : \"إضافة حزمة جديدة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 883,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 882,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"اسم الحزمة *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 893,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.name,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                name: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 894,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 892,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"الكمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 904,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: packageForm.amount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                amount: e.target.value\n                                                            })),\n                                                    placeholder: \"مثل: 60 يوسي\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 905,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 891,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر *\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 918,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.price,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                price: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 919,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 917,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"السعر الأصلي\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 930,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    step: \"0.01\",\n                                                    value: packageForm.originalPrice,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                originalPrice: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0.00\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 931,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 929,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium mb-2\",\n                                                    children: \"نسبة الخصم (%)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 942,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: packageForm.discount,\n                                                    onChange: (e)=>setPackageForm((prev)=>({\n                                                                ...prev,\n                                                                discount: Number(e.target.value)\n                                                            })),\n                                                    placeholder: \"0\",\n                                                    className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 943,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 941,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 916,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.description,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        description: e.target.value\n                                                    })),\n                                            placeholder: \"وصف الحزمة (اختياري)\",\n                                            rows: 3,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"w-5 h-5 text-blue-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 968,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium\",\n                                                    children: \"الأكواد الرقمية\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 969,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-400\",\n                                                    children: \"(اختياري)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 967,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-500/10 border border-blue-500/20 rounded-lg p-3 mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-300 mb-2\",\n                                                    children: \"\\uD83D\\uDCA1 إرشادات:\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 974,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-xs text-blue-200 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• أدخل كود واحد في كل سطر\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 976,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• سيتم تخصيص كود واحد فقط لكل طلب\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 977,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• الأكواد المستخدمة لن تظهر للمشترين الآخرين\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                            lineNumber: 978,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 975,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            value: packageForm.digitalCodes,\n                                            onChange: (e)=>setPackageForm((prev)=>({\n                                                        ...prev,\n                                                        digitalCodes: e.target.value\n                                                    })),\n                                            placeholder: \"أدخل الأكواد الرقمية (كود واحد في كل سطر) مثال: AB12-XY34-ZZ78 CD56-PL90-QW12\",\n                                            rows: 6,\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-purple-500 font-mono text-sm resize-none\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"flex items-center gap-2 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"checkbox\",\n                                                checked: packageForm.popular,\n                                                onChange: (e)=>setPackageForm((prev)=>({\n                                                            ...prev,\n                                                            popular: e.target.checked\n                                                        })),\n                                                className: \"rounded\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 994,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"حزمة شائعة\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 993,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 992,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 889,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsPackageDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1007,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: savePackage,\n                                    className: \"bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800\",\n                                    children: editingPackageIndex !== null ? \"تحديث الحزمة\" : \"إضافة الحزمة\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1014,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1006,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 881,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 880,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isFieldDialogOpen,\n                onOpenChange: setIsFieldDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-5 h-5 text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1029,\n                                        columnNumber: 15\n                                    }, this),\n                                    editingFieldIndex !== null ? \"تعديل الحقل\" : \"إضافة حقل جديد\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1028,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1027,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6 py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"تسمية الحقل *\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1037,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.label,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        label: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1038,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"نوع الحقل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1049,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: fieldForm.type,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        type: e.target.value\n                                                    })),\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"text\",\n                                                    children: \"نص\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1055,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"email\",\n                                                    children: \"بريد إلكتروني\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1056,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"number\",\n                                                    children: \"رقم\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                    lineNumber: 1057,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1050,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium mb-2\",\n                                            children: \"النص التوضيحي\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1063,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            value: fieldForm.placeholder,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        placeholder: e.target.value\n                                                    })),\n                                            placeholder: \"مثل: أدخل اسم المستخدم\",\n                                            className: \"w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1064,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1062,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"field-required\",\n                                            checked: fieldForm.required,\n                                            onChange: (e)=>setFieldForm((prev)=>({\n                                                        ...prev,\n                                                        required: e.target.checked\n                                                    })),\n                                            className: \"rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1075,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"field-required\",\n                                            className: \"text-white\",\n                                            children: \"حقل مطلوب\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1082,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1074,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1034,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3 pt-4 border-t border-gray-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsFieldDialogOpen(false),\n                                    className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                                    children: \"إلغاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                    onClick: saveField,\n                                    className: \"bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800\",\n                                    children: editingFieldIndex !== null ? \"تحديث الحقل\" : \"إضافة الحقل\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                    lineNumber: 1097,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1089,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1026,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1025,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                open: isImageCropDialogOpen,\n                onOpenChange: setIsImageCropDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                    className: \"bg-gray-800 border-gray-700 text-white max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                className: \"text-xl flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"w-5 h-5 text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1112,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"قص وتعديل الصورة\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1111,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1110,\n                            columnNumber: 11\n                        }, this),\n                        imagePreview && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ImageCropper, {\n                            imageSrc: imagePreview,\n                            onCrop: handleImageCrop,\n                            onCancel: ()=>setIsImageCropDialogOpen(false)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                            lineNumber: 1118,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                    lineNumber: 1109,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1108,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 547,\n        columnNumber: 5\n    }, this);\n}\n_s(SimpleProductForm, \"8Y1hvcddtm4Q8zWI4nTeUx6y9nU=\", false, function() {\n    return [\n        _components_ui_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = SimpleProductForm;\nfunction ImageCropper(param) {\n    let { imageSrc, onCrop, onCancel } = param;\n    _s1();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [cropArea, setCropArea] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 50,\n        y: 50,\n        width: 200,\n        height: 200\n    });\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageLoaded, setImageLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [imageSize, setImageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        width: 0,\n        height: 0\n    });\n    // Handle both mouse and touch events\n    const getEventPosition = (e)=>{\n        if ('touches' in e) {\n            return {\n                x: e.touches[0].clientX,\n                y: e.touches[0].clientY\n            };\n        }\n        return {\n            x: e.clientX,\n            y: e.clientY\n        };\n    };\n    const handleStart = (e)=>{\n        e.preventDefault();\n        setIsDragging(true);\n    };\n    const handleMove = (e)=>{\n        if (!isDragging || !imageRef.current) return;\n        e.preventDefault();\n        const rect = imageRef.current.getBoundingClientRect();\n        const pos = getEventPosition(e);\n        const relativeX = pos.x - rect.left;\n        const relativeY = pos.y - rect.top;\n        // Keep crop area within image bounds\n        const newX = Math.max(0, Math.min(relativeX - cropArea.width / 2, rect.width - cropArea.width));\n        const newY = Math.max(0, Math.min(relativeY - cropArea.height / 2, rect.height - cropArea.height));\n        setCropArea((prev)=>({\n                ...prev,\n                x: newX,\n                y: newY\n            }));\n    };\n    const handleEnd = ()=>{\n        setIsDragging(false);\n    };\n    const handleCrop = ()=>{\n        const canvas = canvasRef.current;\n        const image = imageRef.current;\n        if (!canvas || !image) return;\n        const ctx = canvas.getContext('2d');\n        if (!ctx) return;\n        // Calculate scale factors\n        const scaleX = image.naturalWidth / image.offsetWidth;\n        const scaleY = image.naturalHeight / image.offsetHeight;\n        // Set canvas size to desired output size\n        const outputSize = 400;\n        canvas.width = outputSize;\n        canvas.height = outputSize;\n        // Draw cropped and resized image\n        ctx.drawImage(image, cropArea.x * scaleX, cropArea.y * scaleY, cropArea.width * scaleX, cropArea.height * scaleY, 0, 0, outputSize, outputSize);\n        // Convert to base64\n        const croppedImageData = canvas.toDataURL('image/jpeg', 0.9);\n        onCrop(croppedImageData);\n    };\n    const setCropSize = (size)=>{\n        const maxSize = Math.min(imageSize.width, imageSize.height) * 0.8;\n        const newSize = Math.min(size, maxSize);\n        setCropArea((prev)=>({\n                ...prev,\n                width: newSize,\n                height: newSize,\n                x: Math.max(0, Math.min(prev.x, imageSize.width - newSize)),\n                y: Math.max(0, Math.min(prev.y, imageSize.height - newSize))\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6 py-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center gap-2 mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(150),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"صغير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1230,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(200),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"متوسط\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1239,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        type: \"button\",\n                        size: \"sm\",\n                        variant: \"outline\",\n                        onClick: ()=>setCropSize(300),\n                        className: \"border-blue-600 text-blue-400 hover:bg-blue-600/10\",\n                        children: \"كبير\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1248,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1229,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-300 mb-4\",\n                        children: \"اضغط واسحب لتحريك منطقة القص\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1260,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative inline-block bg-gray-900 rounded-lg overflow-hidden\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                ref: imageRef,\n                                src: imageSrc,\n                                alt: \"صورة للقص\",\n                                className: \"max-w-full max-h-96 object-contain block select-none\",\n                                onLoad: ()=>{\n                                    if (imageRef.current) {\n                                        const { offsetWidth, offsetHeight } = imageRef.current;\n                                        setImageSize({\n                                            width: offsetWidth,\n                                            height: offsetHeight\n                                        });\n                                        const size = Math.min(offsetWidth, offsetHeight) * 0.6;\n                                        setCropArea({\n                                            x: (offsetWidth - size) / 2,\n                                            y: (offsetHeight - size) / 2,\n                                            width: size,\n                                            height: size\n                                        });\n                                        setImageLoaded(true);\n                                    }\n                                },\n                                onMouseMove: handleMove,\n                                onMouseUp: handleEnd,\n                                onMouseLeave: handleEnd,\n                                onTouchMove: handleMove,\n                                onTouchEnd: handleEnd\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1263,\n                                columnNumber: 11\n                            }, this),\n                            imageLoaded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute border-4 border-green-400 bg-green-400/10 cursor-move select-none touch-none\",\n                                style: {\n                                    left: cropArea.x,\n                                    top: cropArea.y,\n                                    width: cropArea.width,\n                                    height: cropArea.height,\n                                    userSelect: 'none',\n                                    WebkitUserSelect: 'none',\n                                    touchAction: 'none'\n                                },\n                                onMouseDown: handleStart,\n                                onTouchStart: handleStart,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-8 h-8 border-2 border-white rounded-full bg-green-400/80 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                                lineNumber: 1308,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                            lineNumber: 1307,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1306,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1314,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -left-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1315,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 border-2 border-white rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                        lineNumber: 1316,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1291,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1262,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                ref: canvasRef,\n                className: \"hidden\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1322,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-green-300 text-sm mb-2\",\n                        children: \"\\uD83D\\uDCA1 كيفية الاستخدام:\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1325,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-green-200 text-xs space-y-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اختر حجم منطقة القص من الأزرار أعلاه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1327,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• اضغط واسحب المربع الأخضر لتحريكه\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1328,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• يعمل باللمس على الهاتف والماوس على الكمبيوتر\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1329,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"• الصورة ستُحفظ بجودة عالية مربعة الشكل\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1326,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1324,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end gap-3 pt-6 border-t border-gray-700\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        onClick: onCancel,\n                        className: \"border-gray-600 text-gray-300 hover:bg-gray-700\",\n                        children: \"إلغاء\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1335,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        onClick: handleCrop,\n                        disabled: !imageLoaded,\n                        className: \"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 disabled:opacity-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crop_Edit_Key_Package_Plus_Trash2_Type_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                                lineNumber: 1347,\n                                columnNumber: 11\n                            }, this),\n                            \"قص واستخدام\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                        lineNumber: 1342,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n                lineNumber: 1334,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\admin\\\\SimpleProductForm.tsx\",\n        lineNumber: 1227,\n        columnNumber: 5\n    }, this);\n}\n_s1(ImageCropper, \"+2GuA6xaqd1Bn+DeXkHYPbq06CU=\");\n_c1 = ImageCropper;\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleProductForm\");\n$RefreshReg$(_c1, \"ImageCropper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/admin/SimpleProductForm.tsx\n"));

/***/ })

});