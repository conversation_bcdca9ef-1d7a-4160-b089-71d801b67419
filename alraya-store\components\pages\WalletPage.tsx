"use client"

import { useState, useEffect } from "react"
import { <PERSON>ppHeader } from "@/components/layout/AppHeader"
import { SideMenu } from "@/components/layout/SideMenu"
import { NewsTicket } from "@/components/shared/NewsTicket"
import { MobileNavigation } from "@/components/layout/MobileNavigation"
import { DesktopFooter } from "@/components/layout/DesktopFooter"
import { WalletBalance } from "@/components/wallet/WalletBalance"
import { WalletTransactions } from "@/components/wallet/WalletTransactions"

import { Currency, Transaction } from "@/lib/types"
import { Wallet } from "lucide-react"
import { useRouter } from "next/navigation"
import { useCurrency } from "@/contexts/CurrencyContext"
import { getDigitalContentNotificationCount } from "@/lib/utils/digitalContentUtils"
import { useChat } from "@/lib/hooks/useChat"

/**
 * Convert orders to transactions for unified display in the transactions component
 */
async function loadOrdersAsTransactions(userEmail: string): Promise<Transaction[]> {
  try {
    // Import order service dynamically to avoid circular dependencies
    const { getOrdersByUser } = await import("@/lib/services/orderService")
    const orders = await getOrdersByUser(userEmail)

    return orders.map(order => ({
      id: `order_${order.id}`,
      userId: userEmail,
      walletId: `wallet_${userEmail}`,
      type: "purchase" as const,
      amount: order.totalPrice,
      currency: order.currency,
      description: `🛒 ${order.productName} - ${order.packageName}`,
      referenceNumber: order.id,
      status: order.status === 'completed' ? 'completed' as const :
              order.status === 'pending' ? 'pending' as const :
              order.status === 'failed' ? 'failed' as const : 'pending' as const,
      orderId: order.id,
      hasDigitalContent: order.digitalCodes && order.digitalCodes.length > 0,
      digitalContent: order.digitalCodes && order.digitalCodes.length > 0 ? {
        status: order.status === 'completed' ? 'ready' as const : 'pending' as const,
        contents: order.digitalCodes.map(code => ({
          id: code.id,
          type: 'game_code' as const,
          title: `${order.packageName} - كود رقمي`,
          content: code.key,
          instructions: "استخدم هذا الكود في التطبيق المحدد",
          isRevealed: false,
          deliveredAt: order.updatedAt || order.createdAt
        })),
        deliveryMethod: 'instant' as const,
        lastUpdated: order.updatedAt || order.createdAt
      } : undefined,
      date: order.createdAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt || order.createdAt
    }))
  } catch (error) {
    console.error('Error converting orders to transactions:', error)
    return []
  }
}

export function WalletPage() {
  const [activeTab, setActiveTab] = useState("wallet") // Set to wallet tab since this is wallet page
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const router = useRouter()

  // Use global currency context
  const { selectedCurrency, availableCurrencies, updateCurrency, isLoading: currencyLoading } = useCurrency()

  // Mock user email (in real app, this would come from auth)
  const userEmail = "<EMAIL>"

  // Load user transactions (including orders converted to transactions)
  useEffect(() => {
    const loadTransactions = async () => {
      try {
        setIsLoading(true)

        // Load transactions from localStorage (created by order system)
        const localTransactions = JSON.parse(localStorage.getItem('walletTransactions') || '[]')

        // Load orders and convert them to transactions for unified display
        const orderTransactions = await loadOrdersAsTransactions(userEmail)

        // Merge and sort by date (newest first)
        const allTransactions = [...localTransactions, ...orderTransactions]
          .sort((a, b) => new Date(b.date || b.createdAt).getTime() - new Date(a.date || a.createdAt).getTime())

        setTransactions(allTransactions)
      } catch (error) {
        console.error('Error loading transactions:', error)
        setError('فشل في تحميل المعاملات')
      } finally {
        setIsLoading(false)
      }
    }

    loadTransactions()

    // Listen for real-time transaction updates (from order creation)
    const handleTransactionsUpdated = (event: CustomEvent) => {
      console.log('Transactions updated:', event.detail)
      loadTransactions() // Reload transactions when they change
    }

    window.addEventListener('ordersUpdated', handleTransactionsUpdated as EventListener)
    window.addEventListener('transactionsUpdated', handleTransactionsUpdated as EventListener)

    return () => {
      window.removeEventListener('ordersUpdated', handleTransactionsUpdated as EventListener)
      window.removeEventListener('transactionsUpdated', handleTransactionsUpdated as EventListener)
    }
  }, [userEmail])

  // Get chat unread count for navigation badge
  const { unreadCount: chatUnreadCount } = useChat({
    userId: 'customer-demo', // TODO: Replace with actual user ID
    userType: 'customer'
  })

  // Enhanced handler for currency change - now updates global currency context
  const handleCurrencyChange = async (currency: Currency) => {
    try {
      // Update global currency context (affects entire app)
      await updateCurrency(currency)
    } catch (err) {
      console.error('Failed to update currency preference:', err)
    }
  }

  // Calculate wallet statistics from transactions
  const walletStats = {
    totalSpent: transactions
      .filter(transaction => transaction.status === 'completed' && transaction.type === 'purchase')
      .reduce((sum, transaction) => sum + transaction.amount, 0),
    totalTransactions: transactions.length,
    completedTransactions: transactions.filter(transaction => transaction.status === 'completed').length,
    pendingTransactions: transactions.filter(transaction => transaction.status === 'pending').length,
    digitalCodes: transactions
      .filter(transaction => transaction.status === 'completed' && transaction.hasDigitalContent)
      .reduce((sum, transaction) => sum + (transaction.digitalContent?.contents?.length || 0), 0)
  }

  // ## Handler for adding balance - navigates to checkout page
  const handleAddBalance = () => {
    router.push("/checkout")
  }

  // Navigation handler for navbar
  const handleTabChange = (tab: string) => {
    if (tab === "wallet") {
      router.push("/wallet")
    } else if (tab === "profile") {
      router.push("/profile")
    } else if (tab === "shop") {
      router.push("/shop")
    } else if (tab === "home") {
      router.push("/")
      router.refresh()
    } else {
      setActiveTab(tab)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-yellow-400/20 via-transparent to-transparent" />
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl" />
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl" />

      <AppHeader onMenuOpen={() => setIsMenuOpen(true)} />
      <NewsTicket />
      <SideMenu isOpen={isMenuOpen} onClose={() => setIsMenuOpen(false)} />

      {/* Main Content */}
      <main className="relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32">
        {/* Page Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-4 mb-4">
            <div className="p-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg">
              <Wallet className="h-8 w-8 text-slate-900" />
            </div>
          </div>
          <h1 className="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4">
            محفظتي
          </h1>
          <p className="text-slate-300 text-lg">
            إدارة رصيدك ومعاملاتك المالية بسهولة وأمان
          </p>
        </div>

        {/* Wallet Balance Section */}
        <div className="mb-8">
          <WalletBalance
            walletData={{
              balances: [
                {
                  id: "balance_demo",
                  userId: userEmail,
                  currency: selectedCurrency,
                  amount: walletStats.totalSpent, // Show total spent as balance for demo
                  reservedBalance: 0,
                  totalDeposits: walletStats.totalSpent,
                  totalWithdrawals: 0,
                  totalPurchases: walletStats.totalSpent,
                  lastTransactionAt: transactions.length > 0 ? transactions[0].date || transactions[0].createdAt : undefined,
                  lastUpdated: new Date(),
                  createdAt: new Date()
                }
              ],
              selectedCurrency: selectedCurrency,
              availableCurrencies: availableCurrencies,
              totalPurchases: walletStats.totalSpent,
              transactions: transactions
            }}
            selectedCurrency={selectedCurrency}
            onCurrencyChange={handleCurrencyChange}
            onAddBalance={handleAddBalance}
            isLoading={isLoading}
          />
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-8 p-4 bg-red-900/20 border border-red-700/50 rounded-lg">
            <p className="text-red-100">{error}</p>
          </div>
        )}

        {/* Transactions Section - Primary system for all order and transaction data */}
        <WalletTransactions
          transactions={transactions}
          selectedCurrency={selectedCurrency}
          isLoading={isLoading}
        />
      </main>

      <MobileNavigation
        activeTab={activeTab}
        onTabChange={handleTabChange}
        walletNotificationCount={getDigitalContentNotificationCount(transactions)}
        unreadChatCount={chatUnreadCount}
      />
      <DesktopFooter activeTab={activeTab} onTabChange={handleTabChange} />
    </div>
  )
}
