"use client"

import React, { useState, useEffect, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Upload, X, Refresh<PERSON>w, Check, Loader2, Camera, ImageIcon } from "lucide-react"
import ReactCrop, { centerCrop, makeAspectCrop, Crop, PixelCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { useToast } from "@/components/ui/use-toast"
import { cn } from "@/lib/utils"

// Function to generate a blob from an image with a crop applied
function getCroppedImg(
  image: HTMLImageElement,
  crop: PixelCrop,
  fileName: string = 'cropped.jpg',
  qualityFactor: number = 0.98
): Promise<Blob> {
  const canvas = document.createElement('canvas')
  const scaleX = image.naturalWidth / image.width
  const scaleY = image.naturalHeight / image.height
  
  // Set to the actual pixel dimensions of the crop for high resolution
  canvas.width = crop.width * scaleX
  canvas.height = crop.height * scaleY
  
  const ctx = canvas.getContext('2d')

  if (!ctx) {
    throw new Error('No 2d context')
  }

  // Use high quality rendering
  ctx.imageSmoothingQuality = 'high'
  ctx.imageSmoothingEnabled = true

  ctx.drawImage(
    image,
    crop.x * scaleX,
    crop.y * scaleY,
    crop.width * scaleX,
    crop.height * scaleY,
    0,
    0,
    crop.width * scaleX,
    crop.height * scaleY
  )

  return new Promise((resolve, reject) => {
    canvas.toBlob(
      (blob) => {
        if (!blob) {
          reject(new Error('Canvas is empty'))
          return
        }
        resolve(blob)
      },
      'image/jpeg',
      qualityFactor
    )
  })
}

// Upload service types
export type UploadMode = 'base64' | 'file' | 'url'

export interface UploadService {
  upload: (file: File) => Promise<string>
  mode: UploadMode
}

// Default base64 upload service
const defaultUploadService: UploadService = {
  mode: 'base64',
  upload: async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onloadend = () => {
        const result = reader.result as string
        resolve(result)
      }
      reader.onerror = () => reject(new Error('Failed to read file'))
    })
  }
}

// Image Uploader Component Props
export interface ImageUploaderProps {
  currentImage?: string
  onImageChanged: (url: string) => void
  label?: string
  placeholderText?: string
  aspectRatio?: number
  className?: string
  showUrlInput?: boolean
  maxFileSize?: number // in MB
  allowedTypes?: string[]
  uploadService?: UploadService
  disabled?: boolean
  required?: boolean
  variant?: 'default' | 'compact' | 'card'
}

const ImageUploader: React.FC<ImageUploaderProps> = ({ 
  currentImage = "",
  onImageChanged,
  label = "صورة",
  placeholderText = "أدخل رابط الصورة أو قم برفع صورة",
  aspectRatio = 1,
  className = "",
  showUrlInput = true,
  maxFileSize = 10,
  allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  uploadService = defaultUploadService,
  disabled = false,
  required = false,
  variant = 'default'
}) => {
  const { toast } = useToast()
  
  // State management
  const [previewUrl, setPreviewUrl] = useState(currentImage)
  const [tempUrl, setTempUrl] = useState("")
  const [isValidImage, setIsValidImage] = useState(true)
  const [isTestingUrl, setIsTestingUrl] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [imgSrc, setImgSrc] = useState<string | null>(null)
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  
  // Refs
  const imgRef = useRef<HTMLImageElement>(null)
  const inputId = useRef(`image-upload-${Math.random().toString(36).substr(2, 9)}`)

  // Update preview when currentImage changes
  useEffect(() => {
    setPreviewUrl(currentImage)
    setTempUrl(currentImage)
  }, [currentImage])

  // File selection handler
  const onSelectFile = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return
    
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      toast({
        title: "خطأ في نوع الملف",
        description: `الأنواع المدعومة: ${allowedTypes.join(', ')}`,
        variant: "destructive",
      })
      return
    }

    // Check file size
    if (file.size > maxFileSize * 1024 * 1024) {
      toast({
        title: "حجم الملف كبير جداً",
        description: `الحد الأقصى ${maxFileSize} ميجابايت`,
        variant: "destructive",
      })
      return
    }
    
    const reader = new FileReader()
    reader.addEventListener('load', () => {
      setImgSrc(reader.result?.toString() || '')
      setIsDialogOpen(true)
    })
    reader.readAsDataURL(file)
  }, [allowedTypes, maxFileSize, toast])

  // Image load handler for crop initialization
  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget
    
    // Initialize with centered crop
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 90,
        },
        aspectRatio,
        width,
        height
      ),
      width,
      height
    )
    
    setCrop(crop)
  }, [aspectRatio])

  // Crop completion handler
  const handleCropComplete = useCallback(async () => {
    if (!imgRef.current || !completedCrop) return

    try {
      setIsUploading(true)

      const qualityFactor = 0.98
      const croppedBlob = await getCroppedImg(
        imgRef.current,
        completedCrop,
        `cropped_image.jpg`,
        qualityFactor
      )

      const file = new File([croppedBlob], `cropped_image.jpg`, {
        type: "image/jpeg"
      })

      // Use the provided upload service
      const imageUrl = await uploadService.upload(file)

      onImageChanged(imageUrl)
      setPreviewUrl(imageUrl)
      setTempUrl(imageUrl)
      setIsDialogOpen(false)

      toast({
        title: "تم بنجاح",
        description: "تم معالجة الصورة بنجاح",
      })
    } catch (error) {
      console.error(error)
      toast({
        title: "خطأ",
        description: "فشل في معالجة الصورة",
        variant: "destructive",
      })
    } finally {
      setIsUploading(false)
      setImgSrc(null)
    }
  }, [completedCrop, uploadService, onImageChanged, toast])

  // URL input change handler
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTempUrl(e.target.value)
    setIsValidImage(true)
  }

  // Apply URL handler
  const handleApplyUrl = async () => {
    if (!tempUrl.trim()) return

    try {
      setIsTestingUrl(true)
      const isValid = await testImageUrl(tempUrl)

      if (isValid) {
        setPreviewUrl(tempUrl)
        onImageChanged(tempUrl)
        setIsValidImage(true)
        toast({
          title: "تم بنجاح",
          description: "تم تطبيق رابط الصورة",
        })
      } else {
        setIsValidImage(false)
        toast({
          title: "خطأ",
          description: "رابط الصورة غير صالح",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error("Error validating image URL:", error)
      setIsValidImage(false)
      toast({
        title: "خطأ",
        description: "فشل التحقق من رابط الصورة",
        variant: "destructive",
      })
    } finally {
      setIsTestingUrl(false)
    }
  }

  // Helper function to test if an image URL is valid
  const testImageUrl = (url: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = url
    })
  }

  // Handle image error
  const handleImageError = () => {
    setIsValidImage(false)
    setPreviewUrl("")
    setTempUrl("")
  }

  // Handle upload button click
  const handleUploadButtonClick = () => {
    const fileInput = document.getElementById(inputId.current)
    if (fileInput) {
      fileInput.click()
    }
  }

  // Handle delete image
  const handleDeleteImage = () => {
    setPreviewUrl("")
    setTempUrl("")
    onImageChanged("")
    toast({
      title: "تم الحذف",
      description: "تم حذف الصورة",
    })
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Hidden file input */}
      <input
        id={inputId.current}
        type="file"
        accept={allowedTypes.join(',')}
        onChange={onSelectFile}
        className="hidden"
        disabled={disabled}
      />

      {/* Image Preview */}
      {previewUrl && (
        <div className="relative group">
          <div className="relative overflow-hidden rounded-xl border border-slate-700/50 bg-slate-800/50">
            <img
              src={previewUrl}
              alt="معاينة الصورة"
              className="w-full h-48 object-cover"
              onError={handleImageError}
            />
            {!disabled && (
              <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                <Button
                  type="button"
                  variant="destructive"
                  size="sm"
                  onClick={handleDeleteImage}
                  className="bg-red-600 hover:bg-red-700"
                >
                  <X className="h-4 w-4 mr-2" />
                  حذف
                </Button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* URL Input and Upload Button */}
      {showUrlInput && !disabled && (
        <div className="space-y-2">
          <Label htmlFor={`url-${inputId.current}`}>{label}</Label>
          <div className="flex gap-2">
            <Input
              id={`url-${inputId.current}`}
              value={tempUrl}
              onChange={handleInputChange}
              placeholder={placeholderText}
              className="flex-1"
              disabled={disabled}
            />
            <Button
              type="button"
              variant="outline"
              size="icon"
              onClick={handleApplyUrl}
              disabled={isTestingUrl || disabled}
            >
              {isTestingUrl ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Check className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="default"
              size="icon"
              onClick={handleUploadButtonClick}
              disabled={disabled}
            >
              <Upload className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Upload button only (when URL input is hidden) */}
      {!showUrlInput && !disabled && (
        <div className="space-y-2">
          <Label>{label}</Label>
          <Button
            type="button"
            variant="outline"
            onClick={handleUploadButtonClick}
            className="w-full h-32 border-dashed border-2 hover:border-yellow-400 hover:bg-yellow-400/10"
            disabled={disabled}
          >
            <div className="flex flex-col items-center gap-2">
              <Camera className="h-8 w-8 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">
                اضغط لرفع صورة
              </span>
            </div>
          </Button>
        </div>
      )}

      {/* Crop Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-lg">
          <DialogHeader>
            <DialogTitle>تعديل وضبط الصورة</DialogTitle>
            <DialogDescription>
              قم بضبط الصورة بالشكل المناسب قبل حفظها
            </DialogDescription>
          </DialogHeader>

          <div className="mt-2 mb-4 overflow-hidden rounded-lg border border-slate-700/50 bg-slate-800/50">
            {imgSrc && (
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={aspectRatio}
                className="max-h-[60vh] mx-auto"
              >
                <img
                  ref={imgRef}
                  src={imgSrc}
                  alt="صورة للقص"
                  className="max-w-full max-h-[60vh] mx-auto object-contain"
                  onLoad={onImageLoad}
                />
              </ReactCrop>
            )}
          </div>

          <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end gap-2">
            <Button
              type="button"
              variant="ghost"
              onClick={() => {
                setIsDialogOpen(false)
                setImgSrc(null)
              }}
              className="w-full sm:w-auto"
            >
              إلغاء
            </Button>
            <Button
              type="button"
              variant="default"
              disabled={isUploading || !completedCrop?.width || !completedCrop?.height}
              onClick={handleCropComplete}
              className="w-full sm:w-auto bg-yellow-400 hover:bg-yellow-500 text-slate-900"
            >
              {isUploading ? (
                <>
                  <Loader2 className="ml-2 h-4 w-4 animate-spin" />
                  جاري المعالجة...
                </>
              ) : (
                <>
                  <Check className="ml-2 h-4 w-4" />
                  حفظ الصورة
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ImageUploader
