// Common types used across the application

export interface GameCard {
  title: string
  subtitle: string
  gradient: string
  icon: string
}

export interface MenuItem {
  icon: React.ReactNode
  label: string
  href: string
}

export interface Slide {
  id: number
  title: string
  subtitle: string
  buttonText: string
  gradient: string
  image: string
}

export interface Feature {
  icon: string
  title: string
  desc: string
}

export interface Stat {
  number: string
  label: string
}

export interface NavigationItem {
  id: string
  icon: React.ReactNode
  label: string
  center?: boolean
}

// =====================================================
// PRODUCT MANAGEMENT SYSTEM TYPES
// =====================================================

// ## TODO: Backend Integration - All product types will connect to Supabase
// ## DATABASE LATER: Create tables for products, packages, custom_fields, encrypted_codes

/**
 * Super simplified field types for dynamic product forms
 * CLEAN: Only the 2 essential field types you actually need
 */
export type FieldType =
  | "universal_input" // Universal input field - can be text, email, password, phone, etc.
  | "dropdown"        // Select dropdown (server, region, etc.)

/**
 * Simple category structure for gaming products
 * REQUIRED: Both name and image are mandatory
 */
export interface ProductCategory {
  id: string
  name: string
  image: string  // Required: Either uploaded image URL or emoji
  createdAt: Date
  updatedAt: Date
}

/**
 * Input validation types for universal input field
 * Determines validation rules and input formatting
 */
export type UniversalInputValidation =
  | "text"      // Any text input (names, usernames, etc.)
  | "email"     // Email format validation
  | "phone"     // Phone number format
  | "number"    // Numbers only (IDs, amounts, etc.)
  | "id"        // Alphanumeric ID format (player IDs, etc.)
  | "password"  // Password with security requirements
  | "custom"    // Custom regex pattern

/**
 * Product delivery types - determines how the product is delivered
 */
export type ProductDeliveryType =
  | "direct_charge"   // Direct charging to user account (PUBG UC, Free Fire Diamonds)
  | "code_based"      // Pre-generated codes delivered to user (Gift cards, coupons)

/**
 * Code status for inventory management
 */
export type CodeStatus =
  | "available"   // Ready to be assigned
  | "reserved"    // Temporarily held for an order
  | "sold"        // Assigned to a customer
  | "expired"     // Code has expired
  | "invalid"     // Code is invalid/corrupted

/**
 * Digital code interface for code-based products
 */
export interface DigitalCode {
  id: string
  productId: string              // Associated product
  packageId?: string             // Associated package (optional)
  code: string                   // The actual code (encrypted in storage)
  status: CodeStatus

  // Assignment tracking
  orderId?: string               // Order this code was assigned to
  userId?: string                // User who purchased this code
  assignedAt?: Date              // When code was assigned
  revealedAt?: Date              // When user first viewed the code

  // Code metadata
  createdAt: Date
  expiresAt?: Date               // Code expiration date
  batchId?: string               // For bulk uploads
  notes?: string                 // Admin notes
}

/**
 * Digital code interface for package codes
 * ## DATABASE LATER: Maps to 'digital_codes' table
 */
export interface DigitalCode {
  id: string
  key: string                    // The actual code/key
  used: boolean                  // Whether this code has been used
  assignedToOrderId: string | null // Order ID this code was assigned to
  assignedAt?: Date              // When the code was assigned
  usedAt?: Date                  // When the code was marked as used
  createdAt: Date
}

/**
 * Product package interface for gaming/digital products
 * ## DATABASE LATER: Maps to 'packages' table
 */
export interface ProductPackage {
  id: string
  name: string                   // Package name
  amount: string                 // Display amount "60 UC"
  price: number                  // Current/selling price in USD (base currency)
  originalPrice?: number         // Original price for discount display (optional)
  popular?: boolean              // Mark as popular/recommended
  description?: string           // Package description
  digitalCodes?: DigitalCode[]   // Digital codes for this package
  quantityLimit?: number         // Manual quantity limit for non-code products (optional)
  isActive: boolean
  sortOrder: number

  // Computed fields (calculated automatically)
  readonly discountPercentage?: number  // Auto-calculated: ((originalPrice - price) / originalPrice) * 100
  readonly hasDiscount?: boolean        // Auto-calculated: originalPrice > price
}

/**
 * Dropdown option interface for select/dropdown fields
 */
export interface DropdownOption {
  id: string
  value: string                  // Internal value
  label: string                  // Display label
  sortOrder: number
  isActive: boolean
}

/**
 * Dynamic field configuration for product forms
 * ## DATABASE LATER: Maps to 'custom_fields' table
 */
export interface DynamicField {
  id: string
  type: FieldType
  name: string                   // Internal field name (auto-generated)
  label: string                  // Display label (customizable by admin)
  placeholder?: string           // Placeholder text
  required: boolean

  // Universal input configuration
  inputValidation?: UniversalInputValidation  // For universal_input type
  customPattern?: string         // Custom regex for validation type "custom"

  // General validation (applies to all field types)
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string             // Regex pattern (legacy support)
    min?: number                 // For number validation
    max?: number                 // For number validation
  }

  // Dropdown/Select field configuration
  options?: DropdownOption[]     // For dropdown/select fields
  allowMultiple?: boolean        // For multi-select dropdowns

  dependsOn?: string             // Field dependency (show only if other field has value)
  sortOrder: number
  isActive: boolean
}

/**
 * Product template interface - main product definition
 * ## DATABASE LATER: Maps to 'products' table in Supabase
 */
export interface ProductTemplate {
  id: string
  name: string                   // Product name
  description?: string           // Product description
  category: string               // Product category

  // Core product information
  image?: string                 // ## TODO: Supabase Storage integration
  basePrice?: number             // Base price in USD (can be overridden by packages)
  estimatedTime?: string         // "فوري", "5-10 دقائق", etc.

  // Product delivery type - NEW unified system
  deliveryType: ProductDeliveryType  // "direct_charge" or "code_based"

  // Game/Service information
  gameInfo?: {
    gameName: string             // PUBG Mobile, Free Fire, etc.
    gameIcon?: string            // Game icon URL
    serverRegions?: string[]     // Available server regions
  }

  // Legacy support - will be migrated to deliveryType
  productType: "physical" | "digital" | "service"
  processingType: "instant" | "manual"

  // Code-based product configuration (when deliveryType = "code_based")
  codeConfig?: {
    autoDeliver: boolean         // Auto-deliver digital codes
    codeType: "game_code" | "coupon" | "license" | "download_link"
    deliveryInstructions?: string // How to use the codes
    expiryDays?: number          // Code expiry in days
    lowStockThreshold?: number   // Alert when codes below this number
  }

  // Dynamic fields and packages
  fields: DynamicField[]         // Custom form fields
  packages: ProductPackage[]     // Available packages/options

  // Display and features
  features?: string[]            // Product features in Arabic
  tags?: string[]               // Search tags
  isActive: boolean
  isFeatured?: boolean

  // Metadata
  createdAt: Date
  updatedAt: Date
  createdBy?: string            // ## TODO: Admin user ID from Supabase Auth
}

/**
 * Product form submission data
 * Contains all user-entered data from dynamic form
 */
export interface ProductFormData {
  templateId: string
  selectedPackage: ProductPackage
  quantity: number
  customFields: Record<string, any>  // Field name -> value mapping
  totalPrice: number
  currency: Currency
}

/**
 * Product creation/edit form state
 * Used in admin interface for creating products
 */
export interface ProductFormState {
  // Basic information
  name: string
  description: string
  category: string
  image?: File | string

  // Configuration
  basePrice: number
  estimatedTime: string

  // Dynamic content
  fields: DynamicField[]
  packages: ProductPackage[]
  features: string[]
  tags: string[]

  // Status
  isActive: boolean
  isFeatured: boolean
  isPopular?: boolean
}

/**
 * Admin dashboard statistics for products
 * ## TODO: Calculate from Supabase queries
 */
export interface ProductStats {
  totalProducts: number
  activeProducts: number
  digitalProducts: number
  physicalProducts: number
  totalPackages: number
  totalOrders: number
  popularCategories: Array<{
    category: string
    count: number
  }>
}

/**
 * Product search and filter options
 */
export interface ProductFilters {
  category?: string
  productType?: ProductTemplate['productType']
  processingType?: ProductTemplate['processingType']
  isActive?: boolean
  isFeatured?: boolean
  search?: string
  sortBy?: 'name' | 'createdAt' | 'updatedAt' | 'category'
  sortOrder?: 'asc' | 'desc'
}

/**
 * Field validation result
 */
export interface FieldValidation {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

/**
 * Product template validation
 */
export interface ProductValidation {
  isValid: boolean
  errors: string[]
  warnings?: string[]
  fieldValidations: Record<string, FieldValidation>
}

/**
 * Quick setup wizard configuration
 * Predefined templates for common product types
 */
export interface QuickSetupTemplate {
  id: string
  name: string
  description: string
  category: string
  productType: ProductTemplate['productType']
  icon: string
  defaultFields: Omit<DynamicField, 'id' | 'sortOrder'>[]
  defaultPackages: Omit<ProductPackage, 'id' | 'sortOrder'>[]
  features: string[]
  estimatedTime: string
}

/**
 * Component props for product management
 */
export interface ProductDashboardProps {
  initialProducts?: ProductTemplate[]
  onProductCreate?: (product: ProductTemplate) => void
  onProductUpdate?: (product: ProductTemplate) => void
  onProductDelete?: (productId: string) => void
}

export interface ProductFormProps {
  product?: ProductTemplate
  onSave: (product: ProductTemplate) => void
  onCancel: () => void
  isEditing?: boolean
}

export interface PackageSelectorProps {
  packages: ProductPackage[]
  selectedPackage?: ProductPackage
  onPackageSelect: (pkg: ProductPackage) => void
  currency: Currency
  showPricing?: boolean
  disabled?: boolean
}

export interface CustomFieldProps {
  field: DynamicField
  value: any
  onChange: (value: any) => void
  error?: string
  disabled?: boolean
}

// =====================================================
// ENHANCED MULTI-CURRENCY TYPES
// =====================================================

// Dynamic currency type - will be populated from database
export type Currency = string

// Legacy currency type for backward compatibility
export type LegacyCurrency = "SDG" | "EGP"

// =====================================================
// UTILITY FUNCTIONS FOR PACKAGE CALCULATIONS
// =====================================================

/**
 * Calculate discount percentage from original and current price
 */
export const calculateDiscountPercentage = (originalPrice: number, currentPrice: number): number => {
  if (!originalPrice || originalPrice <= 0 || !currentPrice || currentPrice <= 0) {
    return 0
  }
  if (originalPrice <= currentPrice) {
    return 0
  }
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100)
}

/**
 * Validate discount pricing logic
 */
export const validateDiscountPricing = (originalPrice?: number, currentPrice?: number): {
  isValid: boolean
  error?: string
} => {
  // If no original price, no discount validation needed
  if (!originalPrice) {
    return { isValid: true }
  }

  // If original price is provided, current price must be provided and valid
  if (!currentPrice || currentPrice <= 0) {
    return { isValid: false, error: "يرجى إدخال السعر الحالي" }
  }

  // Original price must be greater than current price for discount to make sense
  if (originalPrice <= currentPrice) {
    return { isValid: false, error: "السعر الأصلي يجب أن يكون أكبر من السعر الحالي" }
  }

  return { isValid: true }
}

/**
 * Enhanced ProductPackage with computed properties
 */
export const enhancePackageWithDiscountInfo = (pkg: ProductPackage): ProductPackage => {
  const discountPercentage = pkg.originalPrice
    ? calculateDiscountPercentage(pkg.originalPrice, pkg.price)
    : 0

  return {
    ...pkg,
    discountPercentage,
    hasDiscount: discountPercentage > 0
  }
}

// Enhanced currency information from database
export interface CurrencyInfo {
  id: string
  code: Currency
  name: string
  symbol: string
  decimalPlaces: number
  isRTL: boolean
  isActive: boolean
  sortOrder: number
  createdAt: Date
  updatedAt: Date
}

// Simplified currency info for UI components
export interface CurrencyDisplay {
  code: Currency
  name: string
  symbol: string
  isRTL?: boolean
}

// Exchange rate information
export interface ExchangeRate {
  id: string
  fromCurrencyCode: Currency
  toCurrencyCode: Currency
  rate: number
  effectiveDate: Date
  createdBy?: string
  createdAt: Date
  isActive: boolean
}

// Exchange rate for API responses
export interface ExchangeRateResponse {
  fromCurrency: Currency
  toCurrency: Currency
  rate: number
  timestamp: Date
  source?: string
}

// Currency conversion result
export interface CurrencyConversion {
  originalAmount: number
  originalCurrency: Currency
  convertedAmount: number
  targetCurrency: Currency
  exchangeRate: number
  conversionFee?: number
  timestamp: Date
}

// Client currency configuration
export interface ClientCurrencySettings {
  id: string
  clientId?: string
  primaryCurrencyCode: Currency
  enabledCurrencies: Currency[]
  enableMultiCurrency: boolean
  enableCurrencyConversion: boolean
  enableAdvancedReporting: boolean
  autoUpdateRates: boolean
  rateUpdateFrequencyHours: number
  createdAt: Date
  updatedAt: Date
}

// Feature flags for currency system
export interface CurrencyFeatureFlags {
  enableMultiCurrency: boolean
  enableCurrencyConversion: boolean
  enableAdvancedReporting: boolean
  enableCrossCurrencyPayments: boolean
  enableRealTimeRates: boolean
}

// Enhanced wallet balance with additional tracking
export interface WalletBalance {
  id: string
  userId: string
  currency: Currency
  amount: number
  reservedBalance: number
  totalDeposits: number
  totalWithdrawals: number
  totalPurchases: number
  lastTransactionAt?: Date
  lastUpdated: Date
  createdAt: Date
}

// Enhanced transaction with currency conversion support and digital content
export interface Transaction {
  id: string
  userId: string
  walletId: string
  type: "deposit" | "withdrawal" | "purchase" | "refund" | "currency_conversion" | "admin_adjustment"
  amount: number
  currency: Currency

  // For currency conversions
  originalAmount?: number
  originalCurrency?: Currency
  exchangeRate?: number
  conversionFee?: number

  // Transaction details
  description: string
  referenceNumber?: string
  externalReference?: string

  // Status and metadata
  status: "pending" | "completed" | "failed" | "cancelled" | "refunded"
  metadata?: Record<string, any>

  // Related records
  orderId?: string
  relatedTransactionId?: string

  // Digital content for purchase transactions
  digitalContent?: DigitalContentDelivery
  hasDigitalContent?: boolean

  // Audit fields
  date: Date
  processedAt?: Date
  createdBy?: string
  createdAt: Date
  updatedAt: Date
}

// Digital content types for orders
export interface DigitalContent {
  id: string
  type: 'game_code' | 'coupon' | 'license' | 'download_link' | 'credentials'
  title: string
  content: string // The actual code/content (encrypted in storage)
  instructions?: string // How to use the code
  expiryDate?: Date
  isRevealed: boolean // Whether user has viewed the content
  deliveredAt: Date
  accessedAt?: Date
}

export interface DigitalContentDelivery {
  status: 'pending' | 'processing' | 'ready' | 'delivered' | 'accessed'
  contents: DigitalContent[]
  deliveryMethod: 'instant' | 'manual'
  estimatedDeliveryTime?: string
  lastUpdated: Date
}



// Enhanced wallet data with multi-currency support
export interface WalletData {
  balances: WalletBalance[]
  selectedCurrency: Currency
  availableCurrencies: CurrencyDisplay[]
  totalPurchases: number
  transactions: Transaction[]
  conversionHistory?: CurrencyConversion[]
  preferences?: UserCurrencyPreferences
}

// User currency preferences
export interface UserCurrencyPreferences {
  id: string
  userId: string
  preferredCurrency: Currency
  displayCurrency: Currency
  enableCurrencyConversion: boolean
  conversionConfirmationRequired: boolean
  preferences: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// Checkout System Types
export interface BankAccount {
  id: string
  name: string
  accountNumber: string
  logoUrl?: string
  isActive: boolean
}

export interface RechargeOption {
  id: string
  amount: number
  currency: Currency
  isActive: boolean
}

export interface CheckoutUserDetails {
  firstName: string
  lastName: string
  phone: string
  email: string
}

export interface CheckoutData {
  step: 1 | 2 | 3
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails | null
  selectedBank: BankAccount | null
  referenceNumber: string
  receiptFile: File | null
  receiptPreview: string | null
}

export interface CheckoutOrder {
  id: string
  amount: number
  currency: Currency
  userDetails: CheckoutUserDetails
  selectedBank: BankAccount
  referenceNumber: string
  receiptFileName?: string
  status: "pending" | "completed" | "failed"
  createdAt: Date
  // Enhanced with currency conversion support
  paymentCurrency?: Currency
  conversionInfo?: {
    originalAmount: number
    originalCurrency: Currency
    convertedAmount: number
    targetCurrency: Currency
    exchangeRate: number
    conversionFee?: number
    timestamp: Date
  }
}

// ===== PRODUCT ORDER SYSTEM TYPES =====

export type OrderStatus = "pending" | "processing" | "completed" | "failed" | "cancelled"
export type ProcessingType = "instant" | "manual"
export type OrderType = "product_order" | "recharge_order"

export interface PriceModifier {
  fieldName: string
  fieldLabel: string
  modifier: number
  type: "add" | "multiply" | "override"
  source: "option" | "package" | "quantity"
}

// Enhanced order pricing with multi-currency support
export interface OrderPricing {
  basePrice: number
  basePriceUSD?: number
  modifiers: PriceModifier[]
  quantity: number
  subtotal: number
  totalPrice: number
  currency: Currency
  exchangeRate?: number
  exchangeRateTimestamp?: Date
  conversionFees?: number
}

// Pricing calculation request
export interface PricingCalculationRequest {
  basePriceUSD: number
  targetCurrency: Currency
  modifiers?: PriceModifier[]
  quantity?: number
  includeConversionFees?: boolean
}

// Pricing calculation response
export interface PricingCalculationResponse {
  basePriceUSD: number
  targetCurrency: Currency
  exchangeRate: number
  targetPrice: number
  modifiers: PriceModifier[]
  quantity: number
  subtotal: number
  totalPrice: number
  conversionFees: number
  rateTimestamp: Date
}

export interface OrderEvent {
  id: string
  type: "created" | "status_change" | "admin_note" | "user_action" | "system_action"
  description: string
  details?: Record<string, any>
  createdAt: Date
  createdBy?: string // user ID or "system"
}

// Simplified Order interface for wallet and order management
export interface Order {
  id: string
  productId: string
  productName: string
  templateName: string
  packageId: string
  packageName: string
  quantity: number
  unitPrice: number
  totalPrice: number
  currency: Currency
  status: OrderStatus
  userDetails: CheckoutUserDetails
  customFields: Record<string, any>
  digitalCodes: DigitalCode[]
  digitalContent?: DigitalContentDelivery
  processingType: ProcessingType
  deliveryType: string
  timeline: OrderEvent[]
  createdAt: Date
  updatedAt: Date
  // Optional pricing structure for compatibility
  pricing?: OrderPricing
}

export interface ProductOrder {
  id: string
  type: OrderType
  templateId: string
  templateName: string
  templateCategory: string
  productData: Record<string, any> // All field values from form submission
  pricing: OrderPricing
  userDetails: CheckoutUserDetails
  status: OrderStatus
  processingType: ProcessingType
  adminNotes?: string
  internalNotes?: string // Private admin notes
  timeline: OrderEvent[]
  attachments?: OrderAttachment[]
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  // Supabase integration fields
  userId?: string // Will be populated from auth
  assignedAdminId?: string
  priority?: "low" | "normal" | "high" | "urgent"
}

// ===== CHAT SYSTEM TYPES =====

/**
 * ## Supabase Integration: Chat Message Interface
 * Maps to 'chats' table in Supabase
 * Real-time subscriptions will use this interface
 */
export interface ChatMessage {
  id: string
  userId: string // Customer ID (references auth.users)
  adminId?: string // Admin who responded (references auth.users)
  message: string
  senderType: 'customer' | 'admin'
  orderId?: string // Optional: link to specific order
  messageType: 'text' | 'image' | 'system' | 'order_update'
  attachmentUrl?: string // For file/image sharing
  isRead: boolean
  createdAt: Date
  updatedAt?: Date
}

/**
 * ## Supabase Integration: Chat Room Interface
 * Represents a conversation between customer and admin
 */
export interface ChatRoom {
  userId: string
  customerName: string
  customerEmail: string
  lastMessage?: ChatMessage
  unreadCount: number
  isOnline: boolean
  lastSeen: Date
  assignedAdminId?: string
  activeOrders: ProductOrder[]
  createdAt: Date
}

/**
 * ## Supabase Integration: User Online Status
 * Maps to 'user_presence' table for real-time status
 */
export interface UserPresence {
  userId: string
  userType: 'customer' | 'admin'
  isOnline: boolean
  lastSeen: Date
  currentPage?: string
}

/**
 * ## Chat System Configuration
 */
export interface ChatConfig {
  maxMessageLength: number
  allowFileUpload: boolean
  allowedFileTypes: string[]
  maxFileSize: number // in MB
  autoMarkAsRead: boolean
  showTypingIndicator: boolean
  enableNotifications: boolean
}

export interface OrderAttachment {
  id: string
  orderId: string
  fieldName: string
  fileName: string
  fileUrl: string
  fileSize: number
  mimeType: string
  uploadedAt: Date
}

// Admin order management interfaces
export interface OrderFilters {
  status?: OrderStatus[]
  type?: OrderType[]
  processingType?: ProcessingType[]
  dateRange?: {
    start: Date
    end: Date
  }
  templateId?: string
  assignedAdmin?: string
  priority?: string[]
  search?: string
}

export interface OrderListItem {
  id: string
  templateName: string
  customerName: string
  customerEmail: string
  totalPrice: number
  currency: Currency
  basePriceUSD?: number
  exchangeRate?: number
  status: OrderStatus
  processingType: ProcessingType
  createdAt: Date
  priority?: string
}

// Enhanced admin order statistics with multi-currency revenue
export interface AdminOrderStats {
  total: number
  pending: number
  processing: number
  completed: number
  failed: number
  cancelled: number
  todayOrders: number
  avgProcessingTime: number // in hours
  revenue: Record<Currency, number>
  revenueConsolidated?: RevenueConsolidated
}

// Consolidated revenue in primary currency
export interface RevenueConsolidated {
  primaryCurrency: Currency
  totalRevenue: number
  revenueByCurrency: Record<Currency, number>
  exchangeRatesUsed: Record<string, number>
  calculatedAt: Date
  periodStart?: Date
  periodEnd?: Date
}

// Revenue calculation options
export interface RevenueCalculationOptions {
  startDate?: Date
  endDate?: Date
  primaryCurrency?: Currency
  includePending?: boolean
  groupByCurrency?: boolean
  includeExchangeRates?: boolean
}

export interface CheckoutConfig {
  bankAccounts: BankAccount[]
  rechargeOptions: RechargeOption[]
  notes: string[]
  lastUpdated: Date
}

// =====================================================
// API TYPES FOR CURRENCY MANAGEMENT
// =====================================================

// API request/response types for currency management
export interface CurrencyManagementRequest {
  action: 'create' | 'update' | 'delete' | 'activate' | 'deactivate'
  currency?: Partial<CurrencyInfo>
  currencyCode?: Currency
}

export interface CurrencyManagementResponse {
  success: boolean
  message: string
  currency?: CurrencyInfo
  error?: string
}

// Exchange rate update request
export interface ExchangeRateUpdateRequest {
  rates: Array<{
    fromCurrency: Currency
    toCurrency: Currency
    rate: number
  }>
  effectiveDate?: Date
  source?: string
}

export interface ExchangeRateUpdateResponse {
  success: boolean
  message: string
  updatedRates: ExchangeRate[]
  errors?: string[]
}

// Wallet operation requests
export interface WalletOperationRequest {
  userId: string
  currency: Currency
  amount: number
  type: Transaction['type']
  description: string
  reference?: string
  metadata?: Record<string, any>
}

export interface WalletOperationResponse {
  success: boolean
  message: string
  transaction?: Transaction
  newBalance?: number
  error?: string
}

// Currency conversion request
export interface CurrencyConversionRequest {
  userId: string
  fromCurrency: Currency
  toCurrency: Currency
  amount: number
  acceptFees?: boolean
}

export interface CurrencyConversionResponse {
  success: boolean
  message: string
  conversion?: CurrencyConversion
  transaction?: Transaction
  error?: string
}

// =====================================================
// UTILITY AND HELPER TYPES
// =====================================================

// Currency validation result
export interface CurrencyValidation {
  isValid: boolean
  errors: string[]
  warnings?: string[]
}

// Exchange rate validation
export interface ExchangeRateValidation {
  isValid: boolean
  errors: string[]
  warnings?: string[]
  isStale?: boolean
  lastUpdated?: Date
}

// Currency system health check
export interface CurrencySystemHealth {
  status: 'healthy' | 'warning' | 'error'
  currencies: {
    total: number
    active: number
    inactive: number
  }
  exchangeRates: {
    total: number
    active: number
    stale: number
    missing: string[]
  }
  lastRateUpdate?: Date
  issues: string[]
}

// Audit log entry
export interface CurrencyAuditLog {
  id: string
  tableName: string
  recordId: string
  action: 'INSERT' | 'UPDATE' | 'DELETE'
  oldValues?: Record<string, any>
  newValues?: Record<string, any>
  changedBy?: string
  changedAt: Date
  ipAddress?: string
  userAgent?: string
}

// =====================================================
// BACKWARD COMPATIBILITY TYPES
// =====================================================

// Legacy types for backward compatibility during migration
export interface LegacyCurrencyInfo {
  code: LegacyCurrency
  name: string
  symbol: string
}

export interface LegacyWalletBalance {
  currency: LegacyCurrency
  amount: number
  lastUpdated: Date
}

export interface LegacyTransaction {
  id: string
  type: "deposit" | "withdrawal" | "purchase"
  amount: number
  currency: LegacyCurrency
  description: string
  date: Date
  status: "completed" | "pending" | "failed"
  reference?: string
}

export interface LegacyWalletData {
  balances: LegacyWalletBalance[]
  selectedCurrency: LegacyCurrency
  totalPurchases: number
  transactions: LegacyTransaction[]
}

// Migration helper types
export interface MigrationStatus {
  isComplete: boolean
  currentPhase: string
  completedPhases: string[]
  errors: string[]
  warnings: string[]
  startedAt?: Date
  completedAt?: Date
}

export interface DataMigrationResult {
  success: boolean
  migratedRecords: {
    currencies: number
    exchangeRates: number
    wallets: number
    transactions: number
    orders: number
    preferences: number
  }
  errors: string[]
  warnings: string[]
}

// =====================================================
// COMPONENT PROP TYPES
// =====================================================

// Props for currency selector component
export interface CurrencySelectorProps {
  selectedCurrency: Currency
  onCurrencyChange: (currency: Currency) => void
  availableCurrencies?: CurrencyDisplay[]
  disabled?: boolean
  className?: string
  showFlags?: boolean
  showFullName?: boolean
}

// Props for currency converter component
export interface CurrencyConverterProps {
  fromCurrency: Currency
  toCurrency: Currency
  amount: number
  onConvert: (conversion: CurrencyConversion) => void
  onCurrencyChange: (from: Currency, to: Currency) => void
  availableCurrencies: CurrencyDisplay[]
  isLoading?: boolean
  disabled?: boolean
}

// Props for wallet balance display
export interface WalletBalanceProps {
  balance: WalletBalance
  showConvertButton?: boolean
  onConvert?: (currency: Currency) => void
  isLoading?: boolean
}

// Props for transaction list
export interface TransactionListProps {
  transactions: TransactionDisplay[]
  selectedCurrency?: Currency
  onCurrencyFilter?: (currency: Currency | null) => void
  isLoading?: boolean
  showPagination?: boolean
  pageSize?: number
}

// =====================================================
// CONFIGURATION TYPES
// =====================================================

// Application currency configuration
export interface AppCurrencyConfig {
  defaultCurrency: Currency
  enabledCurrencies: Currency[]
  features: CurrencyFeatureFlags
  exchangeRateProvider?: string
  updateFrequency?: number
  conversionFeeRate?: number
  minimumConversionAmount?: Record<Currency, number>
}

// Environment-specific currency settings
export interface CurrencyEnvironmentConfig {
  development: AppCurrencyConfig
  staging: AppCurrencyConfig
  production: AppCurrencyConfig
}

// Contact Page Management Types
export interface ContactInfo {
  id: string
  whatsapp: string
  email: string
  workingHours: string
  location: string
  createdAt: string
  updatedAt: string
}

export interface ContactFormField {
  id: string
  name: string
  label: string
  type: 'text' | 'email' | 'tel' | 'textarea' | 'select'
  required: boolean
  placeholder: string
  options?: string[] // For select fields
  validation?: {
    minLength?: number
    maxLength?: number
    pattern?: string
  }
  order: number
  isActive: boolean
}

export interface AboutUsSection {
  id: string
  title: string
  subtitle: string
  vision: {
    title: string
    description: string
    icon: string
  }
  mission: {
    title: string
    description: string
    icon: string
  }
  values: {
    title: string
    items: string[]
    icon: string
  }
  team: {
    title: string
    description: string
    icon: string
  }
  createdAt: string
  updatedAt: string
}

export interface CompanyStats {
  id: string
  customers: {
    value: string
    label: string
    color: string
  }
  successRate: {
    value: string
    label: string
    color: string
  }
  averageTime: {
    value: string
    label: string
    color: string
  }
  support: {
    value: string
    label: string
    color: string
  }
  createdAt: string
  updatedAt: string
}

export interface FAQ {
  id: string
  question: string
  answer: string
  order: number
  isActive: boolean
  category?: string
  createdAt: string
  updatedAt: string
}

export interface TrustIndicator {
  id: string
  title: string
  description: string
  icon: string
  color: string
  order: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ContactPageHeader {
  id: string
  title: string
  description: string
  backgroundImage?: string
  createdAt: string
  updatedAt: string
}

export interface ContactPageContent {
  id: string
  header: ContactPageHeader
  contactInfo: ContactInfo
  formFields: ContactFormField[]
  aboutUs: AboutUsSection
  stats: CompanyStats
  faqs: FAQ[]
  trustIndicators: TrustIndicator[]
  createdAt: string
  updatedAt: string
}

// Contact Form Submission Types
export interface ContactFormSubmission {
  id: string
  formData: Record<string, string>
  submittedAt: string
  status: 'new' | 'read' | 'responded' | 'closed'
  response?: string
  respondedAt?: string
  respondedBy?: string
  ipAddress?: string
  userAgent?: string
}

// Contact Admin Action Types
export type ContactAdminAction = 
  | 'edit_header'
  | 'edit_contact_info'
  | 'manage_form_fields'
  | 'edit_about_us'
  | 'edit_stats'
  | 'manage_faqs'
  | 'manage_trust_indicators'
  | 'view_submissions'
  | 'export_data'

// =====================================================
// BULK DATA LOADING SYSTEM TYPES
// =====================================================

// Bulk Data Loading Types for Single-Request Architecture
export interface BulkAppData {
  products: ProductTemplate[]
  currencies: CurrencyDisplay[]
  exchangeRates: Record<string, number>
  gameCards: GameCard[]
  slides: Slide[]
  walletData?: WalletData
  userPreferences?: UserPreferences
  contactInfo?: ContactInfo
  aboutUs?: AboutUsSection
  stats?: CompanyStats
  faqs?: FAQ[]
  trustIndicators?: TrustIndicator[]
}

export interface UserPreferences {
  preferredCurrency: Currency
  theme: 'light' | 'dark'
  language: 'ar' | 'en'
  notifications: boolean
  autoRefresh: boolean
}

export interface AppDataState {
  data: BulkAppData | null
  isLoading: boolean
  isInitialized: boolean
  error: string | null
  lastUpdated: Date | null
  isOffline: boolean
}

export interface DataLoadingActions {
  loadBulkData: () => Promise<void>
  updateData: (data: Partial<BulkAppData>) => void
  setError: (error: string | null) => void
  clearData: () => void
  refreshData: () => Promise<void>
  setOfflineMode: (isOffline: boolean) => void
}

// Combined store interface
export interface AppStore extends AppDataState, DataLoadingActions {}

// API Response for bulk data endpoint
export interface BulkDataResponse {
  success: boolean
  data: BulkAppData
  timestamp: Date
  version?: string
  error?: string
}
