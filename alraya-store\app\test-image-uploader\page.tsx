"use client"

import React, { useState } from 'react'
import ImageUploader from '@/components/ui/ImageUploader'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { base64UploadService, createFileUploadService } from '@/lib/services/uploadService'

export default function TestImageUploaderPage() {
  const [profileImage, setProfileImage] = useState("")
  const [productImage, setProductImage] = useState("")
  const [bannerImage, setBannerImage] = useState("")

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-yellow-400">
            اختبار مكون رفع الصور
          </h1>
          <p className="text-slate-300 text-lg">
            اختبار مكون ImageUploader المحسن مع خيارات متعددة
          </p>
        </div>

        <Tabs defaultValue="profile" className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
            <TabsTrigger value="profile" className="data-[state=active]:bg-yellow-400 data-[state=active]:text-slate-900">
              صورة شخصية
            </TabsTrigger>
            <TabsTrigger value="product" className="data-[state=active]:bg-yellow-400 data-[state=active]:text-slate-900">
              صورة منتج
            </TabsTrigger>
            <TabsTrigger value="banner" className="data-[state=active]:bg-yellow-400 data-[state=active]:text-slate-900">
              صورة بانر
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile" className="space-y-6">
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-yellow-400">صورة شخصية</CardTitle>
                <CardDescription>
                  رفع صورة شخصية بنسبة 1:1 (مربعة)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUploader
                  currentImage={profileImage}
                  onImageChanged={setProfileImage}
                  label="صورة شخصية"
                  placeholderText="أدخل رابط الصورة الشخصية"
                  aspectRatio={1}
                  uploadService={base64UploadService}
                  maxFileSize={5}
                  showUrlInput={true}
                />
                {profileImage && (
                  <div className="mt-4 p-4 bg-slate-700/50 rounded-lg">
                    <p className="text-sm text-slate-300 mb-2">النتيجة:</p>
                    <p className="text-xs text-slate-400 break-all font-mono">
                      {profileImage.substring(0, 100)}...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="product" className="space-y-6">
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-yellow-400">صورة منتج</CardTitle>
                <CardDescription>
                  رفع صورة منتج بنسبة 4:3
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUploader
                  currentImage={productImage}
                  onImageChanged={setProductImage}
                  label="صورة المنتج"
                  placeholderText="أدخل رابط صورة المنتج"
                  aspectRatio={4/3}
                  uploadService={base64UploadService}
                  maxFileSize={10}
                  showUrlInput={true}
                  variant="default"
                />
                {productImage && (
                  <div className="mt-4 p-4 bg-slate-700/50 rounded-lg">
                    <p className="text-sm text-slate-300 mb-2">النتيجة:</p>
                    <p className="text-xs text-slate-400 break-all font-mono">
                      {productImage.substring(0, 100)}...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="banner" className="space-y-6">
            <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
              <CardHeader>
                <CardTitle className="text-yellow-400">صورة بانر</CardTitle>
                <CardDescription>
                  رفع صورة بانر بنسبة 16:9 (بدون إدخال رابط)
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ImageUploader
                  currentImage={bannerImage}
                  onImageChanged={setBannerImage}
                  label="صورة البانر"
                  aspectRatio={16/9}
                  uploadService={base64UploadService}
                  maxFileSize={15}
                  showUrlInput={false}
                  variant="compact"
                />
                {bannerImage && (
                  <div className="mt-4 p-4 bg-slate-700/50 rounded-lg">
                    <p className="text-sm text-slate-300 mb-2">النتيجة:</p>
                    <p className="text-xs text-slate-400 break-all font-mono">
                      {bannerImage.substring(0, 100)}...
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Card className="bg-slate-800/50 backdrop-blur-xl border-slate-700/50">
          <CardHeader>
            <CardTitle className="text-yellow-400">معلومات المكون</CardTitle>
            <CardDescription>
              تفاصيل حول مكون ImageUploader المحسن
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-semibold text-yellow-400">الميزات:</h4>
                <ul className="space-y-1 text-slate-300">
                  <li>• قص الصور بنسب مختلفة</li>
                  <li>• رفع الملفات أو إدخال روابط</li>
                  <li>• معاينة فورية للصور</li>
                  <li>• التحقق من صحة الملفات</li>
                  <li>• دعم خدمات رفع متعددة</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-yellow-400">الأنواع المدعومة:</h4>
                <ul className="space-y-1 text-slate-300">
                  <li>• JPEG</li>
                  <li>• PNG</li>
                  <li>• GIF</li>
                  <li>• WebP</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
