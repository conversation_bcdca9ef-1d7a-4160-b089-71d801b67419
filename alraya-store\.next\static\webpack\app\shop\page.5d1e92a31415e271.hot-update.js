"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./lib/storage/localStorage.ts":
/*!*************************************!*\
  !*** ./lib/storage/localStorage.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStorage: () => (/* binding */ OrderStorage),\n/* harmony export */   ProductStorage: () => (/* binding */ ProductStorage),\n/* harmony export */   SettingsStorage: () => (/* binding */ SettingsStorage),\n/* harmony export */   UserStorage: () => (/* binding */ UserStorage),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   exportDatabase: () => (/* binding */ exportDatabase),\n/* harmony export */   importDatabase: () => (/* binding */ importDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/**\n * Local Storage Database System\n * \n * Provides a complete database-like interface using localStorage\n * Handles products, orders, users, and digital codes\n */ // Storage keys\nconst STORAGE_KEYS = {\n    PRODUCTS: 'alraya_products',\n    ORDERS: 'alraya_orders',\n    USERS: 'alraya_users',\n    SETTINGS: 'alraya_settings',\n    COUNTERS: 'alraya_counters'\n};\n// =====================================================\n// CORE STORAGE UTILITIES\n// =====================================================\n/**\n * Safe localStorage operations with error handling\n */ class SafeStorage {\n    static get(key, defaultValue) {\n        try {\n            if (false) {}\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : defaultValue;\n        } catch (error) {\n            console.error('Error reading from localStorage key \"'.concat(key, '\":'), error);\n            return defaultValue;\n        }\n    }\n    static set(key, value) {\n        try {\n            if (false) {}\n            localStorage.setItem(key, JSON.stringify(value));\n            return true;\n        } catch (error) {\n            console.error('Error writing to localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static remove(key) {\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            return true;\n        } catch (error) {\n            console.error('Error removing localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static clear() {\n        try {\n            if (false) {}\n            localStorage.clear();\n            return true;\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n            return false;\n        }\n    }\n}\n/**\n * Generate unique IDs\n */ function generateId() {\n    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '';\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp, \"_\").concat(random);\n}\n/**\n * Get and increment counter\n */ function getNextId(type) {\n    const counters = SafeStorage.get(STORAGE_KEYS.COUNTERS, {\n        products: 1,\n        orders: 1,\n        users: 1\n    });\n    const nextId = counters[type];\n    counters[type] = nextId + 1;\n    SafeStorage.set(STORAGE_KEYS.COUNTERS, counters);\n    return nextId;\n}\n// =====================================================\n// PRODUCT STORAGE OPERATIONS\n// =====================================================\nclass ProductStorage {\n    /**\n   * Get all products\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.PRODUCTS, []);\n    }\n    /**\n   * Get product by ID\n   */ static getById(id) {\n        const products = this.getAll();\n        return products.find((p)=>p.id === id) || null;\n    }\n    /**\n   * Get active products only\n   */ static getActive() {\n        return this.getAll().filter((p)=>p.isActive);\n    }\n    /**\n   * Get products by category\n   */ static getByCategory(category) {\n        return this.getActive().filter((p)=>p.category.toLowerCase().includes(category.toLowerCase()));\n    }\n    /**\n   * Search products\n   */ static search(query) {\n        const searchTerm = query.toLowerCase();\n        return this.getActive().filter((p)=>{\n            var _p_description, _p_tags;\n            return p.name.toLowerCase().includes(searchTerm) || ((_p_description = p.description) === null || _p_description === void 0 ? void 0 : _p_description.toLowerCase().includes(searchTerm)) || p.category.toLowerCase().includes(searchTerm) || ((_p_tags = p.tags) === null || _p_tags === void 0 ? void 0 : _p_tags.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n        });\n    }\n    /**\n   * Create new product\n   */ static create(productData) {\n        const products = this.getAll();\n        const now = new Date();\n        const newProduct = {\n            ...productData,\n            id: generateId('prod_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        products.push(newProduct);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event for real-time updates\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'create',\n                product: newProduct\n            }\n        }));\n        return newProduct;\n    }\n    /**\n   * Update existing product\n   */ static update(id, updates) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        const updatedProduct = {\n            ...products[index],\n            ...updates,\n            id,\n            updatedAt: new Date()\n        };\n        products[index] = updatedProduct;\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'update',\n                product: updatedProduct\n            }\n        }));\n        return updatedProduct;\n    }\n    /**\n   * Delete product\n   */ static delete(id) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        const deletedProduct = products[index];\n        products.splice(index, 1);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'delete',\n                product: deletedProduct\n            }\n        }));\n        return true;\n    }\n    /**\n   * Get available digital codes for a package\n   */ static getAvailableCodes(productId, packageId) {\n        const product = this.getById(productId);\n        if (!product) return [];\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return [];\n        return pkg.digitalCodes.filter((code)=>!code.used);\n    }\n    /**\n   * Assign digital code to order\n   */ static assignDigitalCode(productId, packageId, orderId) {\n        const product = this.getById(productId);\n        if (!product) return null;\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return null;\n        const availableCode = pkg.digitalCodes.find((code)=>!code.used);\n        if (!availableCode) return null;\n        // Mark code as used and assign to order\n        availableCode.used = true;\n        availableCode.assignedToOrderId = orderId;\n        availableCode.usedAt = new Date();\n        // Update product in storage\n        this.update(productId, product);\n        return availableCode;\n    }\n    /**\n   * Initialize with sample data if empty\n   */ static initializeSampleData() {\n        const existingProducts = this.getAll();\n        if (existingProducts.length > 0) return;\n        console.log('🔄 Initializing sample products...');\n        // Sample product with enhanced features\n        const sampleProduct = {\n            name: \"PUBG Mobile UC - شحن يوسي\",\n            description: \"شحن يوسي لعبة ببجي موبايل - توصيل فوري مع ضمان الجودة\",\n            category: \"ألعاب الموبايل\",\n            image: \"https://images.unsplash.com/photo-1542751371-adc38448a05e?w=500\",\n            deliveryType: \"code_based\",\n            productType: \"digital\",\n            processingType: \"instant\",\n            fields: [\n                {\n                    id: \"field_1\",\n                    type: \"universal_input\",\n                    name: \"player_id\",\n                    label: \"معرف اللاعب\",\n                    placeholder: \"أدخل معرف اللاعب\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 0,\n                    validation: {}\n                },\n                {\n                    id: \"field_2\",\n                    type: \"dropdown\",\n                    name: \"server_region\",\n                    label: \"منطقة الخادم\",\n                    placeholder: \"اختر منطقة الخادم\",\n                    required: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    validation: {},\n                    options: [\n                        {\n                            id: \"opt_1\",\n                            value: \"global\",\n                            label: \"عالمي\",\n                            sortOrder: 0,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_2\",\n                            value: \"asia\",\n                            label: \"آسيا\",\n                            sortOrder: 1,\n                            isActive: true\n                        },\n                        {\n                            id: \"opt_3\",\n                            value: \"europe\",\n                            label: \"أوروبا\",\n                            sortOrder: 2,\n                            isActive: true\n                        }\n                    ]\n                }\n            ],\n            packages: [\n                {\n                    id: \"pkg_1\",\n                    name: \"60 UC\",\n                    amount: \"60 Unknown Cash\",\n                    price: 0.99,\n                    isActive: true,\n                    sortOrder: 0,\n                    digitalCodes: [\n                        {\n                            id: \"code_1\",\n                            key: \"UC60-ABCD-1234\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_2\",\n                            key: \"UC60-EFGH-5678\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_2\",\n                    name: \"300 UC\",\n                    amount: \"300 Unknown Cash\",\n                    price: 4.99,\n                    originalPrice: 5.99,\n                    popular: true,\n                    isActive: true,\n                    sortOrder: 1,\n                    digitalCodes: [\n                        {\n                            id: \"code_3\",\n                            key: \"UC300-IJKL-9012\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        },\n                        {\n                            id: \"code_4\",\n                            key: \"UC300-MNOP-3456\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                },\n                {\n                    id: \"pkg_3\",\n                    name: \"1800 UC\",\n                    amount: \"1800 Unknown Cash\",\n                    price: 24.99,\n                    originalPrice: 29.99,\n                    description: \"أفضل قيمة - وفر 17%\",\n                    isActive: true,\n                    sortOrder: 2,\n                    digitalCodes: [\n                        {\n                            id: \"code_5\",\n                            key: \"UC1800-QRST-7890\",\n                            used: false,\n                            assignedToOrderId: null,\n                            createdAt: new Date()\n                        }\n                    ]\n                }\n            ],\n            features: [\n                \"توصيل فوري\",\n                \"ضمان الجودة\",\n                \"دعم فني 24/7\",\n                \"أسعار تنافسية\"\n            ],\n            tags: [\n                \"pubg\",\n                \"mobile\",\n                \"uc\",\n                \"gaming\",\n                \"instant\"\n            ],\n            isActive: true,\n            isFeatured: true\n        };\n        this.create(sampleProduct);\n        console.log('✅ Sample product initialized successfully');\n    }\n}\n// =====================================================\n// ORDER STORAGE OPERATIONS\n// =====================================================\nclass OrderStorage {\n    /**\n   * Get all orders\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.ORDERS, []);\n    }\n    /**\n   * Get order by ID\n   */ static getById(id) {\n        const orders = this.getAll();\n        return orders.find((o)=>o.id === id) || null;\n    }\n    /**\n   * Get orders by user (using email as identifier)\n   */ static getByUser(userEmail) {\n        return this.getAll().filter((o)=>o.userDetails.email === userEmail);\n    }\n    /**\n   * Get orders by status\n   */ static getByStatus(status) {\n        return this.getAll().filter((o)=>o.status === status);\n    }\n    /**\n   * Create new order\n   */ static create(orderData) {\n        const orders = this.getAll();\n        const now = new Date();\n        const newOrder = {\n            ...orderData,\n            id: generateId('order_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        orders.push(newOrder);\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'create',\n                order: newOrder\n            }\n        }));\n        return newOrder;\n    }\n    /**\n   * Update order status\n   */ static updateStatus(id, status, notes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === id);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            status,\n            updatedAt: new Date(),\n            ...notes && {\n                notes\n            }\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'update',\n                order: updatedOrder\n            }\n        }));\n        return updatedOrder;\n    }\n    /**\n   * Add digital codes to order\n   */ static addDigitalCodes(orderId, codes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === orderId);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            digitalCodes: [\n                ...orders[index].digitalCodes || [],\n                ...codes\n            ],\n            updatedAt: new Date()\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        return updatedOrder;\n    }\n}\n// =====================================================\n// USER STORAGE OPERATIONS\n// =====================================================\nclass UserStorage {\n    /**\n   * Get all users\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.USERS, []);\n    }\n    /**\n   * Get user by email\n   */ static getByEmail(email) {\n        const users = this.getAll();\n        return users.find((u)=>u.email === email) || null;\n    }\n    /**\n   * Create or update user\n   */ static upsert(userData) {\n        const users = this.getAll();\n        const existingIndex = users.findIndex((u)=>u.email === userData.email);\n        const now = new Date();\n        if (existingIndex >= 0) {\n            // Update existing user\n            const updatedUser = {\n                ...users[existingIndex],\n                ...userData,\n                updatedAt: now\n            };\n            users[existingIndex] = updatedUser;\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return updatedUser;\n        } else {\n            // Create new user\n            const newUser = {\n                ...userData,\n                id: generateId('user_'),\n                createdAt: now,\n                updatedAt: now\n            };\n            users.push(newUser);\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return newUser;\n        }\n    }\n}\n// =====================================================\n// SETTINGS STORAGE\n// =====================================================\nclass SettingsStorage {\n    /**\n   * Get application settings\n   */ static get() {\n        return SafeStorage.get(STORAGE_KEYS.SETTINGS, {});\n    }\n    /**\n   * Update settings\n   */ static update(settings) {\n        const currentSettings = this.get();\n        const updatedSettings = {\n            ...currentSettings,\n            ...settings\n        };\n        SafeStorage.set(STORAGE_KEYS.SETTINGS, updatedSettings);\n    }\n    /**\n   * Get specific setting\n   */ static getSetting(key, defaultValue) {\n        const settings = this.get();\n        return settings[key] !== undefined ? settings[key] : defaultValue;\n    }\n    /**\n   * Set specific setting\n   */ static setSetting(key, value) {\n        this.update({\n            [key]: value\n        });\n    }\n}\n// =====================================================\n// DATABASE INITIALIZATION\n// =====================================================\n/**\n * Initialize the entire localStorage database\n */ function initializeDatabase() {\n    console.log('🔄 Initializing localStorage database...');\n    // Initialize sample data if needed\n    ProductStorage.initializeSampleData();\n    console.log('✅ Database initialized successfully');\n}\n/**\n * Clear all data (for development/testing)\n */ function clearDatabase() {\n    console.log('🗑️ Clearing all localStorage data...');\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        SafeStorage.remove(key);\n    });\n    console.log('✅ Database cleared successfully');\n}\n/**\n * Export database (for backup/migration)\n */ function exportDatabase() {\n    const data = {};\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        data[name] = SafeStorage.get(key, null);\n    });\n    return data;\n}\n/**\n * Import database (for backup/migration)\n */ function importDatabase(data) {\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        if (data[name] !== undefined) {\n            SafeStorage.set(key, data[name]);\n        }\n    });\n    console.log('✅ Database imported successfully');\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9zdG9yYWdlL2xvY2FsU3RvcmFnZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFBOzs7OztDQUtDLEdBSUQsZUFBZTtBQUNmLE1BQU1BLGVBQWU7SUFDbkJDLFVBQVU7SUFDVkMsUUFBUTtJQUNSQyxPQUFPO0lBQ1BDLFVBQVU7SUFDVkMsVUFBVTtBQUNaO0FBU0Esd0RBQXdEO0FBQ3hELHlCQUF5QjtBQUN6Qix3REFBd0Q7QUFFeEQ7O0NBRUMsR0FDRCxNQUFNQztJQUNKLE9BQU9DLElBQU9DLEdBQVcsRUFBRUMsWUFBZSxFQUFLO1FBQzdDLElBQUk7WUFDRixJQUFJLEtBQTZCLEVBQUUsRUFBT0E7WUFDMUMsTUFBTUMsT0FBT0MsYUFBYUMsT0FBTyxDQUFDSjtZQUNsQyxPQUFPRSxPQUFPRyxLQUFLQyxLQUFLLENBQUNKLFFBQVFEO1FBQ25DLEVBQUUsT0FBT00sT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsd0NBQTRDLE9BQUpQLEtBQUksT0FBS087WUFDL0QsT0FBT047UUFDVDtJQUNGO0lBRUEsT0FBT1EsSUFBT1QsR0FBVyxFQUFFVSxLQUFRLEVBQVc7UUFDNUMsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBQzFDUCxhQUFhUSxPQUFPLENBQUNYLEtBQUtLLEtBQUtPLFNBQVMsQ0FBQ0Y7WUFDekMsT0FBTztRQUNULEVBQUUsT0FBT0gsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsc0NBQTBDLE9BQUpQLEtBQUksT0FBS087WUFDN0QsT0FBTztRQUNUO0lBQ0Y7SUFFQSxPQUFPTSxPQUFPYixHQUFXLEVBQVc7UUFDbEMsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBQzFDRyxhQUFhVyxVQUFVLENBQUNkO1lBQ3hCLE9BQU87UUFDVCxFQUFFLE9BQU9PLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLG9DQUF3QyxPQUFKUCxLQUFJLE9BQUtPO1lBQzNELE9BQU87UUFDVDtJQUNGO0lBRUEsT0FBT1EsUUFBaUI7UUFDdEIsSUFBSTtZQUNGLElBQUksS0FBNkIsRUFBRSxFQUFPO1lBQzFDWixhQUFhWSxLQUFLO1lBQ2xCLE9BQU87UUFDVCxFQUFFLE9BQU9SLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLGdDQUFnQ0E7WUFDOUMsT0FBTztRQUNUO0lBQ0Y7QUFDRjtBQUVBOztDQUVDLEdBQ0QsU0FBU1M7UUFBV0MsU0FBQUEsaUVBQWlCO0lBQ25DLE1BQU1DLFlBQVlDLEtBQUtDLEdBQUc7SUFDMUIsTUFBTUMsU0FBU0MsS0FBS0QsTUFBTSxHQUFHRSxRQUFRLENBQUMsSUFBSUMsTUFBTSxDQUFDLEdBQUc7SUFDcEQsT0FBTyxHQUFZTixPQUFURCxRQUFzQkksT0FBYkgsV0FBVSxLQUFVLE9BQVBHO0FBQ2xDO0FBRUE7O0NBRUMsR0FDRCxTQUFTSSxVQUFVQyxJQUFvQjtJQUNyQyxNQUFNQyxXQUFXN0IsWUFBWUMsR0FBRyxDQUFXUCxhQUFhSyxRQUFRLEVBQUU7UUFDaEUrQixVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsT0FBTztJQUNUO0lBRUEsTUFBTUMsU0FBU0osUUFBUSxDQUFDRCxLQUFLO0lBQzdCQyxRQUFRLENBQUNELEtBQUssR0FBR0ssU0FBUztJQUUxQmpDLFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFLLFFBQVEsRUFBRThCO0lBQ3ZDLE9BQU9JO0FBQ1Q7QUFFQSx3REFBd0Q7QUFDeEQsNkJBQTZCO0FBQzdCLHdEQUF3RDtBQUVqRCxNQUFNQztJQUNYOztHQUVDLEdBQ0QsT0FBT0MsU0FBNEI7UUFDakMsT0FBT25DLFlBQVlDLEdBQUcsQ0FBb0JQLGFBQWFDLFFBQVEsRUFBRSxFQUFFO0lBQ3JFO0lBRUE7O0dBRUMsR0FDRCxPQUFPeUMsUUFBUUMsRUFBVSxFQUEwQjtRQUNqRCxNQUFNUCxXQUFXLElBQUksQ0FBQ0ssTUFBTTtRQUM1QixPQUFPTCxTQUFTUSxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVGLEVBQUUsS0FBS0EsT0FBTztJQUM1QztJQUVBOztHQUVDLEdBQ0QsT0FBT0csWUFBK0I7UUFDcEMsT0FBTyxJQUFJLENBQUNMLE1BQU0sR0FBR00sTUFBTSxDQUFDRixDQUFBQSxJQUFLQSxFQUFFRyxRQUFRO0lBQzdDO0lBRUE7O0dBRUMsR0FDRCxPQUFPQyxjQUFjQyxRQUFnQixFQUFxQjtRQUN4RCxPQUFPLElBQUksQ0FBQ0osU0FBUyxHQUFHQyxNQUFNLENBQUNGLENBQUFBLElBQzdCQSxFQUFFSyxRQUFRLENBQUNDLFdBQVcsR0FBR0MsUUFBUSxDQUFDRixTQUFTQyxXQUFXO0lBRTFEO0lBRUE7O0dBRUMsR0FDRCxPQUFPRSxPQUFPQyxLQUFhLEVBQXFCO1FBQzlDLE1BQU1DLGFBQWFELE1BQU1ILFdBQVc7UUFDcEMsT0FBTyxJQUFJLENBQUNMLFNBQVMsR0FBR0MsTUFBTSxDQUFDRixDQUFBQTtnQkFFN0JBLGdCQUVBQTttQkFIQUEsRUFBRVcsSUFBSSxDQUFDTCxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0csaUJBQzlCVixpQkFBQUEsRUFBRVksV0FBVyxjQUFiWixxQ0FBQUEsZUFBZU0sV0FBVyxHQUFHQyxRQUFRLENBQUNHLGdCQUN0Q1YsRUFBRUssUUFBUSxDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ0csaUJBQ2xDVixVQUFBQSxFQUFFYSxJQUFJLGNBQU5iLDhCQUFBQSxRQUFRYyxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlULFdBQVcsR0FBR0MsUUFBUSxDQUFDRzs7SUFFbkQ7SUFFQTs7R0FFQyxHQUNELE9BQU9NLE9BQU9DLFdBQW9FLEVBQW1CO1FBQ25HLE1BQU0xQixXQUFXLElBQUksQ0FBQ0ssTUFBTTtRQUM1QixNQUFNYixNQUFNLElBQUlEO1FBRWhCLE1BQU1vQyxhQUE4QjtZQUNsQyxHQUFHRCxXQUFXO1lBQ2RuQixJQUFJbkIsV0FBVztZQUNmd0MsV0FBV3BDO1lBQ1hxQyxXQUFXckM7UUFDYjtRQUVBUSxTQUFTOEIsSUFBSSxDQUFDSDtRQUNkekQsWUFBWVcsR0FBRyxDQUFDakIsYUFBYUMsUUFBUSxFQUFFbUM7UUFFdkMsOENBQThDO1FBQzlDK0IsT0FBT0MsYUFBYSxDQUFDLElBQUlDLFlBQVksbUJBQW1CO1lBQ3REQyxRQUFRO2dCQUFFQyxRQUFRO2dCQUFVQyxTQUFTVDtZQUFXO1FBQ2xEO1FBRUEsT0FBT0E7SUFDVDtJQUVBOztHQUVDLEdBQ0QsT0FBT1UsT0FBTzlCLEVBQVUsRUFBRStCLE9BQWlDLEVBQTBCO1FBQ25GLE1BQU10QyxXQUFXLElBQUksQ0FBQ0ssTUFBTTtRQUM1QixNQUFNa0MsUUFBUXZDLFNBQVN3QyxTQUFTLENBQUMvQixDQUFBQSxJQUFLQSxFQUFFRixFQUFFLEtBQUtBO1FBRS9DLElBQUlnQyxVQUFVLENBQUMsR0FBRyxPQUFPO1FBRXpCLE1BQU1FLGlCQUFrQztZQUN0QyxHQUFHekMsUUFBUSxDQUFDdUMsTUFBTTtZQUNsQixHQUFHRCxPQUFPO1lBQ1YvQjtZQUNBc0IsV0FBVyxJQUFJdEM7UUFDakI7UUFFQVMsUUFBUSxDQUFDdUMsTUFBTSxHQUFHRTtRQUNsQnZFLFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFDLFFBQVEsRUFBRW1DO1FBRXZDLHdCQUF3QjtRQUN4QitCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLG1CQUFtQjtZQUN0REMsUUFBUTtnQkFBRUMsUUFBUTtnQkFBVUMsU0FBU0s7WUFBZTtRQUN0RDtRQUVBLE9BQU9BO0lBQ1Q7SUFFQTs7R0FFQyxHQUNELE9BQU9DLE9BQU9uQyxFQUFVLEVBQVc7UUFDakMsTUFBTVAsV0FBVyxJQUFJLENBQUNLLE1BQU07UUFDNUIsTUFBTWtDLFFBQVF2QyxTQUFTd0MsU0FBUyxDQUFDL0IsQ0FBQUEsSUFBS0EsRUFBRUYsRUFBRSxLQUFLQTtRQUUvQyxJQUFJZ0MsVUFBVSxDQUFDLEdBQUcsT0FBTztRQUV6QixNQUFNSSxpQkFBaUIzQyxRQUFRLENBQUN1QyxNQUFNO1FBQ3RDdkMsU0FBUzRDLE1BQU0sQ0FBQ0wsT0FBTztRQUN2QnJFLFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFDLFFBQVEsRUFBRW1DO1FBRXZDLHdCQUF3QjtRQUN4QitCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLG1CQUFtQjtZQUN0REMsUUFBUTtnQkFBRUMsUUFBUTtnQkFBVUMsU0FBU087WUFBZTtRQUN0RDtRQUVBLE9BQU87SUFDVDtJQUVBOztHQUVDLEdBQ0QsT0FBT0Usa0JBQWtCQyxTQUFpQixFQUFFQyxTQUFpQixFQUFpQjtRQUM1RSxNQUFNWCxVQUFVLElBQUksQ0FBQzlCLE9BQU8sQ0FBQ3dDO1FBQzdCLElBQUksQ0FBQ1YsU0FBUyxPQUFPLEVBQUU7UUFFdkIsTUFBTVksTUFBTVosUUFBUWEsUUFBUSxDQUFDekMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFRixFQUFFLEtBQUt3QztRQUNoRCxJQUFJLENBQUNDLE9BQU8sQ0FBQ0EsSUFBSUUsWUFBWSxFQUFFLE9BQU8sRUFBRTtRQUV4QyxPQUFPRixJQUFJRSxZQUFZLENBQUN2QyxNQUFNLENBQUN3QyxDQUFBQSxPQUFRLENBQUNBLEtBQUtDLElBQUk7SUFDbkQ7SUFFQTs7R0FFQyxHQUNELE9BQU9DLGtCQUFrQlAsU0FBaUIsRUFBRUMsU0FBaUIsRUFBRU8sT0FBZSxFQUFzQjtRQUNsRyxNQUFNbEIsVUFBVSxJQUFJLENBQUM5QixPQUFPLENBQUN3QztRQUM3QixJQUFJLENBQUNWLFNBQVMsT0FBTztRQUVyQixNQUFNWSxNQUFNWixRQUFRYSxRQUFRLENBQUN6QyxJQUFJLENBQUNDLENBQUFBLElBQUtBLEVBQUVGLEVBQUUsS0FBS3dDO1FBQ2hELElBQUksQ0FBQ0MsT0FBTyxDQUFDQSxJQUFJRSxZQUFZLEVBQUUsT0FBTztRQUV0QyxNQUFNSyxnQkFBZ0JQLElBQUlFLFlBQVksQ0FBQzFDLElBQUksQ0FBQzJDLENBQUFBLE9BQVEsQ0FBQ0EsS0FBS0MsSUFBSTtRQUM5RCxJQUFJLENBQUNHLGVBQWUsT0FBTztRQUUzQix3Q0FBd0M7UUFDeENBLGNBQWNILElBQUksR0FBRztRQUNyQkcsY0FBY0MsaUJBQWlCLEdBQUdGO1FBQ2xDQyxjQUFjRSxNQUFNLEdBQUcsSUFBSWxFO1FBRTNCLDRCQUE0QjtRQUM1QixJQUFJLENBQUM4QyxNQUFNLENBQUNTLFdBQVdWO1FBRXZCLE9BQU9tQjtJQUNUO0lBRUE7O0dBRUMsR0FDRCxPQUFPRyx1QkFBNkI7UUFDbEMsTUFBTUMsbUJBQW1CLElBQUksQ0FBQ3RELE1BQU07UUFDcEMsSUFBSXNELGlCQUFpQkMsTUFBTSxHQUFHLEdBQUc7UUFFakNoRixRQUFRaUYsR0FBRyxDQUFDO1FBRVosd0NBQXdDO1FBQ3hDLE1BQU1DLGdCQUFnQjtZQUNwQjFDLE1BQU07WUFDTkMsYUFBYTtZQUNiUCxVQUFVO1lBQ1ZpRCxPQUFPO1lBQ1BDLGNBQWM7WUFDZEMsYUFBYTtZQUNiQyxnQkFBZ0I7WUFDaEJDLFFBQVE7Z0JBQ047b0JBQ0U1RCxJQUFJO29CQUNKVCxNQUFNO29CQUNOc0IsTUFBTTtvQkFDTmdELE9BQU87b0JBQ1BDLGFBQWE7b0JBQ2JDLFVBQVU7b0JBQ1YxRCxVQUFVO29CQUNWMkQsV0FBVztvQkFDWEMsWUFBWSxDQUFDO2dCQUNmO2dCQUNBO29CQUNFakUsSUFBSTtvQkFDSlQsTUFBTTtvQkFDTnNCLE1BQU07b0JBQ05nRCxPQUFPO29CQUNQQyxhQUFhO29CQUNiQyxVQUFVO29CQUNWMUQsVUFBVTtvQkFDVjJELFdBQVc7b0JBQ1hDLFlBQVksQ0FBQztvQkFDYkMsU0FBUzt3QkFDUDs0QkFBRWxFLElBQUk7NEJBQVN6QixPQUFPOzRCQUFVc0YsT0FBTzs0QkFBU0csV0FBVzs0QkFBRzNELFVBQVU7d0JBQUs7d0JBQzdFOzRCQUFFTCxJQUFJOzRCQUFTekIsT0FBTzs0QkFBUXNGLE9BQU87NEJBQVFHLFdBQVc7NEJBQUczRCxVQUFVO3dCQUFLO3dCQUMxRTs0QkFBRUwsSUFBSTs0QkFBU3pCLE9BQU87NEJBQVVzRixPQUFPOzRCQUFVRyxXQUFXOzRCQUFHM0QsVUFBVTt3QkFBSztxQkFDL0U7Z0JBQ0g7YUFDRDtZQUNEcUMsVUFBVTtnQkFDUjtvQkFDRTFDLElBQUk7b0JBQ0phLE1BQU07b0JBQ05zRCxRQUFRO29CQUNSQyxPQUFPO29CQUNQL0QsVUFBVTtvQkFDVjJELFdBQVc7b0JBQ1hyQixjQUFjO3dCQUNaOzRCQUNFM0MsSUFBSTs0QkFDSm5DLEtBQUs7NEJBQ0xnRixNQUFNOzRCQUNOSSxtQkFBbUI7NEJBQ25CNUIsV0FBVyxJQUFJckM7d0JBQ2pCO3dCQUNBOzRCQUNFZ0IsSUFBSTs0QkFDSm5DLEtBQUs7NEJBQ0xnRixNQUFNOzRCQUNOSSxtQkFBbUI7NEJBQ25CNUIsV0FBVyxJQUFJckM7d0JBQ2pCO3FCQUNEO2dCQUNIO2dCQUNBO29CQUNFZ0IsSUFBSTtvQkFDSmEsTUFBTTtvQkFDTnNELFFBQVE7b0JBQ1JDLE9BQU87b0JBQ1BDLGVBQWU7b0JBQ2ZDLFNBQVM7b0JBQ1RqRSxVQUFVO29CQUNWMkQsV0FBVztvQkFDWHJCLGNBQWM7d0JBQ1o7NEJBQ0UzQyxJQUFJOzRCQUNKbkMsS0FBSzs0QkFDTGdGLE1BQU07NEJBQ05JLG1CQUFtQjs0QkFDbkI1QixXQUFXLElBQUlyQzt3QkFDakI7d0JBQ0E7NEJBQ0VnQixJQUFJOzRCQUNKbkMsS0FBSzs0QkFDTGdGLE1BQU07NEJBQ05JLG1CQUFtQjs0QkFDbkI1QixXQUFXLElBQUlyQzt3QkFDakI7cUJBQ0Q7Z0JBQ0g7Z0JBQ0E7b0JBQ0VnQixJQUFJO29CQUNKYSxNQUFNO29CQUNOc0QsUUFBUTtvQkFDUkMsT0FBTztvQkFDUEMsZUFBZTtvQkFDZnZELGFBQWE7b0JBQ2JULFVBQVU7b0JBQ1YyRCxXQUFXO29CQUNYckIsY0FBYzt3QkFDWjs0QkFDRTNDLElBQUk7NEJBQ0puQyxLQUFLOzRCQUNMZ0YsTUFBTTs0QkFDTkksbUJBQW1COzRCQUNuQjVCLFdBQVcsSUFBSXJDO3dCQUNqQjtxQkFDRDtnQkFDSDthQUNEO1lBQ0R1RixVQUFVO2dCQUFDO2dCQUFjO2dCQUFlO2dCQUFnQjthQUFnQjtZQUN4RXhELE1BQU07Z0JBQUM7Z0JBQVE7Z0JBQVU7Z0JBQU07Z0JBQVU7YUFBVTtZQUNuRFYsVUFBVTtZQUNWbUUsWUFBWTtRQUNkO1FBRUEsSUFBSSxDQUFDdEQsTUFBTSxDQUFDcUM7UUFDWmxGLFFBQVFpRixHQUFHLENBQUM7SUFDZDtBQUNGO0FBRUEsd0RBQXdEO0FBQ3hELDJCQUEyQjtBQUMzQix3REFBd0Q7QUFFakQsTUFBTW1CO0lBQ1g7O0dBRUMsR0FDRCxPQUFPM0UsU0FBa0I7UUFDdkIsT0FBT25DLFlBQVlDLEdBQUcsQ0FBVVAsYUFBYUUsTUFBTSxFQUFFLEVBQUU7SUFDekQ7SUFFQTs7R0FFQyxHQUNELE9BQU93QyxRQUFRQyxFQUFVLEVBQWdCO1FBQ3ZDLE1BQU1OLFNBQVMsSUFBSSxDQUFDSSxNQUFNO1FBQzFCLE9BQU9KLE9BQU9PLElBQUksQ0FBQ3lFLENBQUFBLElBQUtBLEVBQUUxRSxFQUFFLEtBQUtBLE9BQU87SUFDMUM7SUFFQTs7R0FFQyxHQUNELE9BQU8yRSxVQUFVQyxTQUFpQixFQUFXO1FBQzNDLE9BQU8sSUFBSSxDQUFDOUUsTUFBTSxHQUFHTSxNQUFNLENBQUNzRSxDQUFBQSxJQUFLQSxFQUFFRyxXQUFXLENBQUNDLEtBQUssS0FBS0Y7SUFDM0Q7SUFFQTs7R0FFQyxHQUNELE9BQU9HLFlBQVlDLE1BQWMsRUFBVztRQUMxQyxPQUFPLElBQUksQ0FBQ2xGLE1BQU0sR0FBR00sTUFBTSxDQUFDc0UsQ0FBQUEsSUFBS0EsRUFBRU0sTUFBTSxLQUFLQTtJQUNoRDtJQUVBOztHQUVDLEdBQ0QsT0FBTzlELE9BQU8rRCxTQUF3RCxFQUFTO1FBQzdFLE1BQU12RixTQUFTLElBQUksQ0FBQ0ksTUFBTTtRQUMxQixNQUFNYixNQUFNLElBQUlEO1FBRWhCLE1BQU1rRyxXQUFrQjtZQUN0QixHQUFHRCxTQUFTO1lBQ1pqRixJQUFJbkIsV0FBVztZQUNmd0MsV0FBV3BDO1lBQ1hxQyxXQUFXckM7UUFDYjtRQUVBUyxPQUFPNkIsSUFBSSxDQUFDMkQ7UUFDWnZILFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFFLE1BQU0sRUFBRW1DO1FBRXJDLHdCQUF3QjtRQUN4QjhCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGlCQUFpQjtZQUNwREMsUUFBUTtnQkFBRUMsUUFBUTtnQkFBVXVELE9BQU9EO1lBQVM7UUFDOUM7UUFFQSxPQUFPQTtJQUNUO0lBRUE7O0dBRUMsR0FDRCxPQUFPRSxhQUFhcEYsRUFBVSxFQUFFZ0YsTUFBYyxFQUFFSyxLQUFjLEVBQWdCO1FBQzVFLE1BQU0zRixTQUFTLElBQUksQ0FBQ0ksTUFBTTtRQUMxQixNQUFNa0MsUUFBUXRDLE9BQU91QyxTQUFTLENBQUN5QyxDQUFBQSxJQUFLQSxFQUFFMUUsRUFBRSxLQUFLQTtRQUU3QyxJQUFJZ0MsVUFBVSxDQUFDLEdBQUcsT0FBTztRQUV6QixNQUFNc0QsZUFBc0I7WUFDMUIsR0FBRzVGLE1BQU0sQ0FBQ3NDLE1BQU07WUFDaEJnRDtZQUNBMUQsV0FBVyxJQUFJdEM7WUFDZixHQUFJcUcsU0FBUztnQkFBRUE7WUFBTSxDQUFDO1FBQ3hCO1FBRUEzRixNQUFNLENBQUNzQyxNQUFNLEdBQUdzRDtRQUNoQjNILFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFFLE1BQU0sRUFBRW1DO1FBRXJDLHdCQUF3QjtRQUN4QjhCLE9BQU9DLGFBQWEsQ0FBQyxJQUFJQyxZQUFZLGlCQUFpQjtZQUNwREMsUUFBUTtnQkFBRUMsUUFBUTtnQkFBVXVELE9BQU9HO1lBQWE7UUFDbEQ7UUFFQSxPQUFPQTtJQUNUO0lBRUE7O0dBRUMsR0FDRCxPQUFPQyxnQkFBZ0J4QyxPQUFlLEVBQUV5QyxLQUFvQixFQUFnQjtRQUMxRSxNQUFNOUYsU0FBUyxJQUFJLENBQUNJLE1BQU07UUFDMUIsTUFBTWtDLFFBQVF0QyxPQUFPdUMsU0FBUyxDQUFDeUMsQ0FBQUEsSUFBS0EsRUFBRTFFLEVBQUUsS0FBSytDO1FBRTdDLElBQUlmLFVBQVUsQ0FBQyxHQUFHLE9BQU87UUFFekIsTUFBTXNELGVBQXNCO1lBQzFCLEdBQUc1RixNQUFNLENBQUNzQyxNQUFNO1lBQ2hCVyxjQUFjO21CQUFLakQsTUFBTSxDQUFDc0MsTUFBTSxDQUFDVyxZQUFZLElBQUksRUFBRTttQkFBTTZDO2FBQU07WUFDL0RsRSxXQUFXLElBQUl0QztRQUNqQjtRQUVBVSxNQUFNLENBQUNzQyxNQUFNLEdBQUdzRDtRQUNoQjNILFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFFLE1BQU0sRUFBRW1DO1FBRXJDLE9BQU80RjtJQUNUO0FBQ0Y7QUFFQSx3REFBd0Q7QUFDeEQsMEJBQTBCO0FBQzFCLHdEQUF3RDtBQUVqRCxNQUFNRztJQUNYOztHQUVDLEdBQ0QsT0FBTzNGLFNBQWlCO1FBQ3RCLE9BQU9uQyxZQUFZQyxHQUFHLENBQVNQLGFBQWFHLEtBQUssRUFBRSxFQUFFO0lBQ3ZEO0lBRUE7O0dBRUMsR0FDRCxPQUFPa0ksV0FBV1osS0FBYSxFQUFlO1FBQzVDLE1BQU1uRixRQUFRLElBQUksQ0FBQ0csTUFBTTtRQUN6QixPQUFPSCxNQUFNTSxJQUFJLENBQUMwRixDQUFBQSxJQUFLQSxFQUFFYixLQUFLLEtBQUtBLFVBQVU7SUFDL0M7SUFFQTs7R0FFQyxHQUNELE9BQU9jLE9BQU9DLFFBQXNELEVBQVE7UUFDMUUsTUFBTWxHLFFBQVEsSUFBSSxDQUFDRyxNQUFNO1FBQ3pCLE1BQU1nRyxnQkFBZ0JuRyxNQUFNc0MsU0FBUyxDQUFDMEQsQ0FBQUEsSUFBS0EsRUFBRWIsS0FBSyxLQUFLZSxTQUFTZixLQUFLO1FBQ3JFLE1BQU03RixNQUFNLElBQUlEO1FBRWhCLElBQUk4RyxpQkFBaUIsR0FBRztZQUN0Qix1QkFBdUI7WUFDdkIsTUFBTUMsY0FBb0I7Z0JBQ3hCLEdBQUdwRyxLQUFLLENBQUNtRyxjQUFjO2dCQUN2QixHQUFHRCxRQUFRO2dCQUNYdkUsV0FBV3JDO1lBQ2I7WUFDQVUsS0FBSyxDQUFDbUcsY0FBYyxHQUFHQztZQUN2QnBJLFlBQVlXLEdBQUcsQ0FBQ2pCLGFBQWFHLEtBQUssRUFBRW1DO1lBQ3BDLE9BQU9vRztRQUNULE9BQU87WUFDTCxrQkFBa0I7WUFDbEIsTUFBTUMsVUFBZ0I7Z0JBQ3BCLEdBQUdILFFBQVE7Z0JBQ1g3RixJQUFJbkIsV0FBVztnQkFDZndDLFdBQVdwQztnQkFDWHFDLFdBQVdyQztZQUNiO1lBQ0FVLE1BQU00QixJQUFJLENBQUN5RTtZQUNYckksWUFBWVcsR0FBRyxDQUFDakIsYUFBYUcsS0FBSyxFQUFFbUM7WUFDcEMsT0FBT3FHO1FBQ1Q7SUFDRjtBQUNGO0FBRUEsd0RBQXdEO0FBQ3hELG1CQUFtQjtBQUNuQix3REFBd0Q7QUFFakQsTUFBTUM7SUFDWDs7R0FFQyxHQUNELE9BQU9ySSxNQUEyQjtRQUNoQyxPQUFPRCxZQUFZQyxHQUFHLENBQUNQLGFBQWFJLFFBQVEsRUFBRSxDQUFDO0lBQ2pEO0lBRUE7O0dBRUMsR0FDRCxPQUFPcUUsT0FBT29FLFFBQTZCLEVBQVE7UUFDakQsTUFBTUMsa0JBQWtCLElBQUksQ0FBQ3ZJLEdBQUc7UUFDaEMsTUFBTXdJLGtCQUFrQjtZQUFFLEdBQUdELGVBQWU7WUFBRSxHQUFHRCxRQUFRO1FBQUM7UUFDMUR2SSxZQUFZVyxHQUFHLENBQUNqQixhQUFhSSxRQUFRLEVBQUUySTtJQUN6QztJQUVBOztHQUVDLEdBQ0QsT0FBT0MsV0FBY3hJLEdBQVcsRUFBRUMsWUFBZSxFQUFLO1FBQ3BELE1BQU1vSSxXQUFXLElBQUksQ0FBQ3RJLEdBQUc7UUFDekIsT0FBT3NJLFFBQVEsQ0FBQ3JJLElBQUksS0FBS3lJLFlBQVlKLFFBQVEsQ0FBQ3JJLElBQUksR0FBR0M7SUFDdkQ7SUFFQTs7R0FFQyxHQUNELE9BQU95SSxXQUFXMUksR0FBVyxFQUFFVSxLQUFVLEVBQVE7UUFDL0MsSUFBSSxDQUFDdUQsTUFBTSxDQUFDO1lBQUUsQ0FBQ2pFLElBQUksRUFBRVU7UUFBTTtJQUM3QjtBQUNGO0FBRUEsd0RBQXdEO0FBQ3hELDBCQUEwQjtBQUMxQix3REFBd0Q7QUFFeEQ7O0NBRUMsR0FDTSxTQUFTaUk7SUFDZG5JLFFBQVFpRixHQUFHLENBQUM7SUFFWixtQ0FBbUM7SUFDbkN6RCxlQUFlc0Qsb0JBQW9CO0lBRW5DOUUsUUFBUWlGLEdBQUcsQ0FBQztBQUNkO0FBRUE7O0NBRUMsR0FDTSxTQUFTbUQ7SUFDZHBJLFFBQVFpRixHQUFHLENBQUM7SUFFWm9ELE9BQU9DLE1BQU0sQ0FBQ3RKLGNBQWN1SixPQUFPLENBQUMvSSxDQUFBQTtRQUNsQ0YsWUFBWWUsTUFBTSxDQUFDYjtJQUNyQjtJQUVBUSxRQUFRaUYsR0FBRyxDQUFDO0FBQ2Q7QUFFQTs7Q0FFQyxHQUNNLFNBQVN1RDtJQUNkLE1BQU1DLE9BQTRCLENBQUM7SUFFbkNKLE9BQU9LLE9BQU8sQ0FBQzFKLGNBQWN1SixPQUFPLENBQUM7WUFBQyxDQUFDL0YsTUFBTWhELElBQUk7UUFDL0NpSixJQUFJLENBQUNqRyxLQUFLLEdBQUdsRCxZQUFZQyxHQUFHLENBQUNDLEtBQUs7SUFDcEM7SUFFQSxPQUFPaUo7QUFDVDtBQUVBOztDQUVDLEdBQ00sU0FBU0UsZUFBZUYsSUFBeUI7SUFDdERKLE9BQU9LLE9BQU8sQ0FBQzFKLGNBQWN1SixPQUFPLENBQUM7WUFBQyxDQUFDL0YsTUFBTWhELElBQUk7UUFDL0MsSUFBSWlKLElBQUksQ0FBQ2pHLEtBQUssS0FBS3lGLFdBQVc7WUFDNUIzSSxZQUFZVyxHQUFHLENBQUNULEtBQUtpSixJQUFJLENBQUNqRyxLQUFLO1FBQ2pDO0lBQ0Y7SUFFQXhDLFFBQVFpRixHQUFHLENBQUM7QUFDZCIsInNvdXJjZXMiOlsiRDpcXFZTLXByb2plY3RzXFx0cnlcXGFscmF5YS1zdG9yZVxcbGliXFxzdG9yYWdlXFxsb2NhbFN0b3JhZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBMb2NhbCBTdG9yYWdlIERhdGFiYXNlIFN5c3RlbVxuICogXG4gKiBQcm92aWRlcyBhIGNvbXBsZXRlIGRhdGFiYXNlLWxpa2UgaW50ZXJmYWNlIHVzaW5nIGxvY2FsU3RvcmFnZVxuICogSGFuZGxlcyBwcm9kdWN0cywgb3JkZXJzLCB1c2VycywgYW5kIGRpZ2l0YWwgY29kZXNcbiAqL1xuXG5pbXBvcnQgeyBQcm9kdWN0VGVtcGxhdGUsIE9yZGVyLCBVc2VyLCBEaWdpdGFsQ29kZSB9IGZyb20gJ0AvbGliL3R5cGVzJ1xuXG4vLyBTdG9yYWdlIGtleXNcbmNvbnN0IFNUT1JBR0VfS0VZUyA9IHtcbiAgUFJPRFVDVFM6ICdhbHJheWFfcHJvZHVjdHMnLFxuICBPUkRFUlM6ICdhbHJheWFfb3JkZXJzJywgXG4gIFVTRVJTOiAnYWxyYXlhX3VzZXJzJyxcbiAgU0VUVElOR1M6ICdhbHJheWFfc2V0dGluZ3MnLFxuICBDT1VOVEVSUzogJ2FscmF5YV9jb3VudGVycydcbn0gYXMgY29uc3RcblxuLy8gQ291bnRlciBtYW5hZ2VtZW50IGZvciBhdXRvLWluY3JlbWVudGluZyBJRHNcbmludGVyZmFjZSBDb3VudGVycyB7XG4gIHByb2R1Y3RzOiBudW1iZXJcbiAgb3JkZXJzOiBudW1iZXJcbiAgdXNlcnM6IG51bWJlclxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gQ09SRSBTVE9SQUdFIFVUSUxJVElFU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuLyoqXG4gKiBTYWZlIGxvY2FsU3RvcmFnZSBvcGVyYXRpb25zIHdpdGggZXJyb3IgaGFuZGxpbmdcbiAqL1xuY2xhc3MgU2FmZVN0b3JhZ2Uge1xuICBzdGF0aWMgZ2V0PFQ+KGtleTogc3RyaW5nLCBkZWZhdWx0VmFsdWU6IFQpOiBUIHtcbiAgICB0cnkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgICBjb25zdCBpdGVtID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oa2V5KVxuICAgICAgcmV0dXJuIGl0ZW0gPyBKU09OLnBhcnNlKGl0ZW0pIDogZGVmYXVsdFZhbHVlXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYEVycm9yIHJlYWRpbmcgZnJvbSBsb2NhbFN0b3JhZ2Uga2V5IFwiJHtrZXl9XCI6YCwgZXJyb3IpXG4gICAgICByZXR1cm4gZGVmYXVsdFZhbHVlXG4gICAgfVxuICB9XG5cbiAgc3RhdGljIHNldDxUPihrZXk6IHN0cmluZywgdmFsdWU6IFQpOiBib29sZWFuIHtcbiAgICB0cnkge1xuICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09ICd1bmRlZmluZWQnKSByZXR1cm4gZmFsc2VcbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKGtleSwgSlNPTi5zdHJpbmdpZnkodmFsdWUpKVxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihgRXJyb3Igd3JpdGluZyB0byBsb2NhbFN0b3JhZ2Uga2V5IFwiJHtrZXl9XCI6YCwgZXJyb3IpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgcmVtb3ZlKGtleTogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIGZhbHNlXG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShrZXkpXG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBFcnJvciByZW1vdmluZyBsb2NhbFN0b3JhZ2Uga2V5IFwiJHtrZXl9XCI6YCwgZXJyb3IpXG4gICAgICByZXR1cm4gZmFsc2VcbiAgICB9XG4gIH1cblxuICBzdGF0aWMgY2xlYXIoKTogYm9vbGVhbiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSAndW5kZWZpbmVkJykgcmV0dXJuIGZhbHNlXG4gICAgICBsb2NhbFN0b3JhZ2UuY2xlYXIoKVxuICAgICAgcmV0dXJuIHRydWVcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY2xlYXJpbmcgbG9jYWxTdG9yYWdlOicsIGVycm9yKVxuICAgICAgcmV0dXJuIGZhbHNlXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogR2VuZXJhdGUgdW5pcXVlIElEc1xuICovXG5mdW5jdGlvbiBnZW5lcmF0ZUlkKHByZWZpeDogc3RyaW5nID0gJycpOiBzdHJpbmcge1xuICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpXG4gIGNvbnN0IHJhbmRvbSA9IE1hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KVxuICByZXR1cm4gYCR7cHJlZml4fSR7dGltZXN0YW1wfV8ke3JhbmRvbX1gXG59XG5cbi8qKlxuICogR2V0IGFuZCBpbmNyZW1lbnQgY291bnRlclxuICovXG5mdW5jdGlvbiBnZXROZXh0SWQodHlwZToga2V5b2YgQ291bnRlcnMpOiBudW1iZXIge1xuICBjb25zdCBjb3VudGVycyA9IFNhZmVTdG9yYWdlLmdldDxDb3VudGVycz4oU1RPUkFHRV9LRVlTLkNPVU5URVJTLCB7XG4gICAgcHJvZHVjdHM6IDEsXG4gICAgb3JkZXJzOiAxLFxuICAgIHVzZXJzOiAxXG4gIH0pXG4gIFxuICBjb25zdCBuZXh0SWQgPSBjb3VudGVyc1t0eXBlXVxuICBjb3VudGVyc1t0eXBlXSA9IG5leHRJZCArIDFcbiAgXG4gIFNhZmVTdG9yYWdlLnNldChTVE9SQUdFX0tFWVMuQ09VTlRFUlMsIGNvdW50ZXJzKVxuICByZXR1cm4gbmV4dElkXG59XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBQUk9EVUNUIFNUT1JBR0UgT1BFUkFUSU9OU1xuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuZXhwb3J0IGNsYXNzIFByb2R1Y3RTdG9yYWdlIHtcbiAgLyoqXG4gICAqIEdldCBhbGwgcHJvZHVjdHNcbiAgICovXG4gIHN0YXRpYyBnZXRBbGwoKTogUHJvZHVjdFRlbXBsYXRlW10ge1xuICAgIHJldHVybiBTYWZlU3RvcmFnZS5nZXQ8UHJvZHVjdFRlbXBsYXRlW10+KFNUT1JBR0VfS0VZUy5QUk9EVUNUUywgW10pXG4gIH1cblxuICAvKipcbiAgICogR2V0IHByb2R1Y3QgYnkgSURcbiAgICovXG4gIHN0YXRpYyBnZXRCeUlkKGlkOiBzdHJpbmcpOiBQcm9kdWN0VGVtcGxhdGUgfCBudWxsIHtcbiAgICBjb25zdCBwcm9kdWN0cyA9IHRoaXMuZ2V0QWxsKClcbiAgICByZXR1cm4gcHJvZHVjdHMuZmluZChwID0+IHAuaWQgPT09IGlkKSB8fCBudWxsXG4gIH1cblxuICAvKipcbiAgICogR2V0IGFjdGl2ZSBwcm9kdWN0cyBvbmx5XG4gICAqL1xuICBzdGF0aWMgZ2V0QWN0aXZlKCk6IFByb2R1Y3RUZW1wbGF0ZVtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRBbGwoKS5maWx0ZXIocCA9PiBwLmlzQWN0aXZlKVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBwcm9kdWN0cyBieSBjYXRlZ29yeVxuICAgKi9cbiAgc3RhdGljIGdldEJ5Q2F0ZWdvcnkoY2F0ZWdvcnk6IHN0cmluZyk6IFByb2R1Y3RUZW1wbGF0ZVtdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRBY3RpdmUoKS5maWx0ZXIocCA9PiBcbiAgICAgIHAuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhjYXRlZ29yeS50b0xvd2VyQ2FzZSgpKVxuICAgIClcbiAgfVxuXG4gIC8qKlxuICAgKiBTZWFyY2ggcHJvZHVjdHNcbiAgICovXG4gIHN0YXRpYyBzZWFyY2gocXVlcnk6IHN0cmluZyk6IFByb2R1Y3RUZW1wbGF0ZVtdIHtcbiAgICBjb25zdCBzZWFyY2hUZXJtID0gcXVlcnkudG9Mb3dlckNhc2UoKVxuICAgIHJldHVybiB0aGlzLmdldEFjdGl2ZSgpLmZpbHRlcihwID0+IFxuICAgICAgcC5uYW1lLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgIHAuZGVzY3JpcHRpb24/LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybSkgfHxcbiAgICAgIHAuY2F0ZWdvcnkudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtKSB8fFxuICAgICAgcC50YWdzPy5zb21lKHRhZyA9PiB0YWcudG9Mb3dlckNhc2UoKS5pbmNsdWRlcyhzZWFyY2hUZXJtKSlcbiAgICApXG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIG5ldyBwcm9kdWN0XG4gICAqL1xuICBzdGF0aWMgY3JlYXRlKHByb2R1Y3REYXRhOiBPbWl0PFByb2R1Y3RUZW1wbGF0ZSwgJ2lkJyB8ICdjcmVhdGVkQXQnIHwgJ3VwZGF0ZWRBdCc+KTogUHJvZHVjdFRlbXBsYXRlIHtcbiAgICBjb25zdCBwcm9kdWN0cyA9IHRoaXMuZ2V0QWxsKClcbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgXG4gICAgY29uc3QgbmV3UHJvZHVjdDogUHJvZHVjdFRlbXBsYXRlID0ge1xuICAgICAgLi4ucHJvZHVjdERhdGEsXG4gICAgICBpZDogZ2VuZXJhdGVJZCgncHJvZF8nKSxcbiAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgdXBkYXRlZEF0OiBub3dcbiAgICB9XG5cbiAgICBwcm9kdWN0cy5wdXNoKG5ld1Byb2R1Y3QpXG4gICAgU2FmZVN0b3JhZ2Uuc2V0KFNUT1JBR0VfS0VZUy5QUk9EVUNUUywgcHJvZHVjdHMpXG4gICAgXG4gICAgLy8gVHJpZ2dlciBzdG9yYWdlIGV2ZW50IGZvciByZWFsLXRpbWUgdXBkYXRlc1xuICAgIHdpbmRvdy5kaXNwYXRjaEV2ZW50KG5ldyBDdXN0b21FdmVudCgncHJvZHVjdHNVcGRhdGVkJywgeyBcbiAgICAgIGRldGFpbDogeyBhY3Rpb246ICdjcmVhdGUnLCBwcm9kdWN0OiBuZXdQcm9kdWN0IH0gXG4gICAgfSkpXG4gICAgXG4gICAgcmV0dXJuIG5ld1Byb2R1Y3RcbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgZXhpc3RpbmcgcHJvZHVjdFxuICAgKi9cbiAgc3RhdGljIHVwZGF0ZShpZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFByb2R1Y3RUZW1wbGF0ZT4pOiBQcm9kdWN0VGVtcGxhdGUgfCBudWxsIHtcbiAgICBjb25zdCBwcm9kdWN0cyA9IHRoaXMuZ2V0QWxsKClcbiAgICBjb25zdCBpbmRleCA9IHByb2R1Y3RzLmZpbmRJbmRleChwID0+IHAuaWQgPT09IGlkKVxuICAgIFxuICAgIGlmIChpbmRleCA9PT0gLTEpIHJldHVybiBudWxsXG4gICAgXG4gICAgY29uc3QgdXBkYXRlZFByb2R1Y3Q6IFByb2R1Y3RUZW1wbGF0ZSA9IHtcbiAgICAgIC4uLnByb2R1Y3RzW2luZGV4XSxcbiAgICAgIC4uLnVwZGF0ZXMsXG4gICAgICBpZCwgLy8gRW5zdXJlIElEIGRvZXNuJ3QgY2hhbmdlXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKClcbiAgICB9XG4gICAgXG4gICAgcHJvZHVjdHNbaW5kZXhdID0gdXBkYXRlZFByb2R1Y3RcbiAgICBTYWZlU3RvcmFnZS5zZXQoU1RPUkFHRV9LRVlTLlBST0RVQ1RTLCBwcm9kdWN0cylcbiAgICBcbiAgICAvLyBUcmlnZ2VyIHN0b3JhZ2UgZXZlbnRcbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ3Byb2R1Y3RzVXBkYXRlZCcsIHsgXG4gICAgICBkZXRhaWw6IHsgYWN0aW9uOiAndXBkYXRlJywgcHJvZHVjdDogdXBkYXRlZFByb2R1Y3QgfSBcbiAgICB9KSlcbiAgICBcbiAgICByZXR1cm4gdXBkYXRlZFByb2R1Y3RcbiAgfVxuXG4gIC8qKlxuICAgKiBEZWxldGUgcHJvZHVjdFxuICAgKi9cbiAgc3RhdGljIGRlbGV0ZShpZDogc3RyaW5nKTogYm9vbGVhbiB7XG4gICAgY29uc3QgcHJvZHVjdHMgPSB0aGlzLmdldEFsbCgpXG4gICAgY29uc3QgaW5kZXggPSBwcm9kdWN0cy5maW5kSW5kZXgocCA9PiBwLmlkID09PSBpZClcbiAgICBcbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gZmFsc2VcbiAgICBcbiAgICBjb25zdCBkZWxldGVkUHJvZHVjdCA9IHByb2R1Y3RzW2luZGV4XVxuICAgIHByb2R1Y3RzLnNwbGljZShpbmRleCwgMSlcbiAgICBTYWZlU3RvcmFnZS5zZXQoU1RPUkFHRV9LRVlTLlBST0RVQ1RTLCBwcm9kdWN0cylcbiAgICBcbiAgICAvLyBUcmlnZ2VyIHN0b3JhZ2UgZXZlbnRcbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ3Byb2R1Y3RzVXBkYXRlZCcsIHsgXG4gICAgICBkZXRhaWw6IHsgYWN0aW9uOiAnZGVsZXRlJywgcHJvZHVjdDogZGVsZXRlZFByb2R1Y3QgfSBcbiAgICB9KSlcbiAgICBcbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBhdmFpbGFibGUgZGlnaXRhbCBjb2RlcyBmb3IgYSBwYWNrYWdlXG4gICAqL1xuICBzdGF0aWMgZ2V0QXZhaWxhYmxlQ29kZXMocHJvZHVjdElkOiBzdHJpbmcsIHBhY2thZ2VJZDogc3RyaW5nKTogRGlnaXRhbENvZGVbXSB7XG4gICAgY29uc3QgcHJvZHVjdCA9IHRoaXMuZ2V0QnlJZChwcm9kdWN0SWQpXG4gICAgaWYgKCFwcm9kdWN0KSByZXR1cm4gW11cbiAgICBcbiAgICBjb25zdCBwa2cgPSBwcm9kdWN0LnBhY2thZ2VzLmZpbmQocCA9PiBwLmlkID09PSBwYWNrYWdlSWQpXG4gICAgaWYgKCFwa2cgfHwgIXBrZy5kaWdpdGFsQ29kZXMpIHJldHVybiBbXVxuICAgIFxuICAgIHJldHVybiBwa2cuZGlnaXRhbENvZGVzLmZpbHRlcihjb2RlID0+ICFjb2RlLnVzZWQpXG4gIH1cblxuICAvKipcbiAgICogQXNzaWduIGRpZ2l0YWwgY29kZSB0byBvcmRlclxuICAgKi9cbiAgc3RhdGljIGFzc2lnbkRpZ2l0YWxDb2RlKHByb2R1Y3RJZDogc3RyaW5nLCBwYWNrYWdlSWQ6IHN0cmluZywgb3JkZXJJZDogc3RyaW5nKTogRGlnaXRhbENvZGUgfCBudWxsIHtcbiAgICBjb25zdCBwcm9kdWN0ID0gdGhpcy5nZXRCeUlkKHByb2R1Y3RJZClcbiAgICBpZiAoIXByb2R1Y3QpIHJldHVybiBudWxsXG4gICAgXG4gICAgY29uc3QgcGtnID0gcHJvZHVjdC5wYWNrYWdlcy5maW5kKHAgPT4gcC5pZCA9PT0gcGFja2FnZUlkKVxuICAgIGlmICghcGtnIHx8ICFwa2cuZGlnaXRhbENvZGVzKSByZXR1cm4gbnVsbFxuICAgIFxuICAgIGNvbnN0IGF2YWlsYWJsZUNvZGUgPSBwa2cuZGlnaXRhbENvZGVzLmZpbmQoY29kZSA9PiAhY29kZS51c2VkKVxuICAgIGlmICghYXZhaWxhYmxlQ29kZSkgcmV0dXJuIG51bGxcbiAgICBcbiAgICAvLyBNYXJrIGNvZGUgYXMgdXNlZCBhbmQgYXNzaWduIHRvIG9yZGVyXG4gICAgYXZhaWxhYmxlQ29kZS51c2VkID0gdHJ1ZVxuICAgIGF2YWlsYWJsZUNvZGUuYXNzaWduZWRUb09yZGVySWQgPSBvcmRlcklkXG4gICAgYXZhaWxhYmxlQ29kZS51c2VkQXQgPSBuZXcgRGF0ZSgpXG4gICAgXG4gICAgLy8gVXBkYXRlIHByb2R1Y3QgaW4gc3RvcmFnZVxuICAgIHRoaXMudXBkYXRlKHByb2R1Y3RJZCwgcHJvZHVjdClcbiAgICBcbiAgICByZXR1cm4gYXZhaWxhYmxlQ29kZVxuICB9XG5cbiAgLyoqXG4gICAqIEluaXRpYWxpemUgd2l0aCBzYW1wbGUgZGF0YSBpZiBlbXB0eVxuICAgKi9cbiAgc3RhdGljIGluaXRpYWxpemVTYW1wbGVEYXRhKCk6IHZvaWQge1xuICAgIGNvbnN0IGV4aXN0aW5nUHJvZHVjdHMgPSB0aGlzLmdldEFsbCgpXG4gICAgaWYgKGV4aXN0aW5nUHJvZHVjdHMubGVuZ3RoID4gMCkgcmV0dXJuXG5cbiAgICBjb25zb2xlLmxvZygn8J+UhCBJbml0aWFsaXppbmcgc2FtcGxlIHByb2R1Y3RzLi4uJylcblxuICAgIC8vIFNhbXBsZSBwcm9kdWN0IHdpdGggZW5oYW5jZWQgZmVhdHVyZXNcbiAgICBjb25zdCBzYW1wbGVQcm9kdWN0ID0ge1xuICAgICAgbmFtZTogXCJQVUJHIE1vYmlsZSBVQyAtINi02K3ZhiDZitmI2LPZilwiLFxuICAgICAgZGVzY3JpcHRpb246IFwi2LTYrdmGINmK2YjYs9mKINmE2LnYqNipINio2KjYrNmKINmF2YjYqNin2YrZhCAtINiq2YjYtdmK2YQg2YHZiNix2Yog2YXYuSDYttmF2KfZhiDYp9mE2KzZiNiv2KlcIixcbiAgICAgIGNhdGVnb3J5OiBcItij2YTYudin2Kgg2KfZhNmF2YjYqNin2YrZhFwiLFxuICAgICAgaW1hZ2U6IFwiaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDI3NTEzNzEtYWRjMzg0NDhhMDVlP3c9NTAwXCIsXG4gICAgICBkZWxpdmVyeVR5cGU6IFwiY29kZV9iYXNlZFwiIGFzIGNvbnN0LFxuICAgICAgcHJvZHVjdFR5cGU6IFwiZGlnaXRhbFwiIGFzIGNvbnN0LFxuICAgICAgcHJvY2Vzc2luZ1R5cGU6IFwiaW5zdGFudFwiIGFzIGNvbnN0LFxuICAgICAgZmllbGRzOiBbXG4gICAgICAgIHtcbiAgICAgICAgICBpZDogXCJmaWVsZF8xXCIsXG4gICAgICAgICAgdHlwZTogXCJ1bml2ZXJzYWxfaW5wdXRcIiBhcyBjb25zdCxcbiAgICAgICAgICBuYW1lOiBcInBsYXllcl9pZFwiLFxuICAgICAgICAgIGxhYmVsOiBcItmF2LnYsdmBINin2YTZhNin2LnYqFwiLFxuICAgICAgICAgIHBsYWNlaG9sZGVyOiBcItij2K/YrtmEINmF2LnYsdmBINin2YTZhNin2LnYqFwiLFxuICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICAgIHNvcnRPcmRlcjogMCxcbiAgICAgICAgICB2YWxpZGF0aW9uOiB7fVxuICAgICAgICB9LFxuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwiZmllbGRfMlwiLFxuICAgICAgICAgIHR5cGU6IFwiZHJvcGRvd25cIiBhcyBjb25zdCxcbiAgICAgICAgICBuYW1lOiBcInNlcnZlcl9yZWdpb25cIixcbiAgICAgICAgICBsYWJlbDogXCLZhdmG2LfZgtipINin2YTYrtin2K/ZhVwiLFxuICAgICAgICAgIHBsYWNlaG9sZGVyOiBcItin2K7YqtixINmF2YbYt9mC2Kkg2KfZhNiu2KfYr9mFXCIsXG4gICAgICAgICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgICAgICAgaXNBY3RpdmU6IHRydWUsXG4gICAgICAgICAgc29ydE9yZGVyOiAxLFxuICAgICAgICAgIHZhbGlkYXRpb246IHt9LFxuICAgICAgICAgIG9wdGlvbnM6IFtcbiAgICAgICAgICAgIHsgaWQ6IFwib3B0XzFcIiwgdmFsdWU6IFwiZ2xvYmFsXCIsIGxhYmVsOiBcIti52KfZhNmF2YpcIiwgc29ydE9yZGVyOiAwLCBpc0FjdGl2ZTogdHJ1ZSB9LFxuICAgICAgICAgICAgeyBpZDogXCJvcHRfMlwiLCB2YWx1ZTogXCJhc2lhXCIsIGxhYmVsOiBcItii2LPZitinXCIsIHNvcnRPcmRlcjogMSwgaXNBY3RpdmU6IHRydWUgfSxcbiAgICAgICAgICAgIHsgaWQ6IFwib3B0XzNcIiwgdmFsdWU6IFwiZXVyb3BlXCIsIGxhYmVsOiBcItij2YjYsdmI2KjYp1wiLCBzb3J0T3JkZXI6IDIsIGlzQWN0aXZlOiB0cnVlIH1cbiAgICAgICAgICBdXG4gICAgICAgIH1cbiAgICAgIF0sXG4gICAgICBwYWNrYWdlczogW1xuICAgICAgICB7XG4gICAgICAgICAgaWQ6IFwicGtnXzFcIixcbiAgICAgICAgICBuYW1lOiBcIjYwIFVDXCIsXG4gICAgICAgICAgYW1vdW50OiBcIjYwIFVua25vd24gQ2FzaFwiLFxuICAgICAgICAgIHByaWNlOiAwLjk5LFxuICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICAgIHNvcnRPcmRlcjogMCxcbiAgICAgICAgICBkaWdpdGFsQ29kZXM6IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IFwiY29kZV8xXCIsXG4gICAgICAgICAgICAgIGtleTogXCJVQzYwLUFCQ0QtMTIzNFwiLFxuICAgICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgICAgYXNzaWduZWRUb09yZGVySWQ6IG51bGwsXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKVxuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IFwiY29kZV8yXCIsXG4gICAgICAgICAgICAgIGtleTogXCJVQzYwLUVGR0gtNTY3OFwiLFxuICAgICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgICAgYXNzaWduZWRUb09yZGVySWQ6IG51bGwsXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcInBrZ18yXCIsXG4gICAgICAgICAgbmFtZTogXCIzMDAgVUNcIixcbiAgICAgICAgICBhbW91bnQ6IFwiMzAwIFVua25vd24gQ2FzaFwiLFxuICAgICAgICAgIHByaWNlOiA0Ljk5LFxuICAgICAgICAgIG9yaWdpbmFsUHJpY2U6IDUuOTksXG4gICAgICAgICAgcG9wdWxhcjogdHJ1ZSxcbiAgICAgICAgICBpc0FjdGl2ZTogdHJ1ZSxcbiAgICAgICAgICBzb3J0T3JkZXI6IDEsXG4gICAgICAgICAgZGlnaXRhbENvZGVzOiBbXG4gICAgICAgICAgICB7XG4gICAgICAgICAgICAgIGlkOiBcImNvZGVfM1wiLFxuICAgICAgICAgICAgICBrZXk6IFwiVUMzMDAtSUpLTC05MDEyXCIsXG4gICAgICAgICAgICAgIHVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBhc3NpZ25lZFRvT3JkZXJJZDogbnVsbCxcbiAgICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICBpZDogXCJjb2RlXzRcIixcbiAgICAgICAgICAgICAga2V5OiBcIlVDMzAwLU1OT1AtMzQ1NlwiLFxuICAgICAgICAgICAgICB1c2VkOiBmYWxzZSxcbiAgICAgICAgICAgICAgYXNzaWduZWRUb09yZGVySWQ6IG51bGwsXG4gICAgICAgICAgICAgIGNyZWF0ZWRBdDogbmV3IERhdGUoKVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIGlkOiBcInBrZ18zXCIsXG4gICAgICAgICAgbmFtZTogXCIxODAwIFVDXCIsXG4gICAgICAgICAgYW1vdW50OiBcIjE4MDAgVW5rbm93biBDYXNoXCIsXG4gICAgICAgICAgcHJpY2U6IDI0Ljk5LFxuICAgICAgICAgIG9yaWdpbmFsUHJpY2U6IDI5Ljk5LFxuICAgICAgICAgIGRlc2NyaXB0aW9uOiBcItij2YHYttmEINmC2YrZhdipIC0g2YjZgdixIDE3JVwiLFxuICAgICAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgICAgIHNvcnRPcmRlcjogMixcbiAgICAgICAgICBkaWdpdGFsQ29kZXM6IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IFwiY29kZV81XCIsXG4gICAgICAgICAgICAgIGtleTogXCJVQzE4MDAtUVJTVC03ODkwXCIsXG4gICAgICAgICAgICAgIHVzZWQ6IGZhbHNlLFxuICAgICAgICAgICAgICBhc3NpZ25lZFRvT3JkZXJJZDogbnVsbCxcbiAgICAgICAgICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgICAgICAgICB9XG4gICAgICAgICAgXVxuICAgICAgICB9XG4gICAgICBdLFxuICAgICAgZmVhdHVyZXM6IFtcItiq2YjYtdmK2YQg2YHZiNix2YpcIiwgXCLYttmF2KfZhiDYp9mE2KzZiNiv2KlcIiwgXCLYr9i52YUg2YHZhtmKIDI0LzdcIiwgXCLYo9iz2LnYp9ixINiq2YbYp9mB2LPZitipXCJdLFxuICAgICAgdGFnczogW1wicHViZ1wiLCBcIm1vYmlsZVwiLCBcInVjXCIsIFwiZ2FtaW5nXCIsIFwiaW5zdGFudFwiXSxcbiAgICAgIGlzQWN0aXZlOiB0cnVlLFxuICAgICAgaXNGZWF0dXJlZDogdHJ1ZVxuICAgIH1cblxuICAgIHRoaXMuY3JlYXRlKHNhbXBsZVByb2R1Y3QpXG4gICAgY29uc29sZS5sb2coJ+KchSBTYW1wbGUgcHJvZHVjdCBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKVxuICB9XG59XG5cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG4vLyBPUkRFUiBTVE9SQUdFIE9QRVJBVElPTlNcbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbmV4cG9ydCBjbGFzcyBPcmRlclN0b3JhZ2Uge1xuICAvKipcbiAgICogR2V0IGFsbCBvcmRlcnNcbiAgICovXG4gIHN0YXRpYyBnZXRBbGwoKTogT3JkZXJbXSB7XG4gICAgcmV0dXJuIFNhZmVTdG9yYWdlLmdldDxPcmRlcltdPihTVE9SQUdFX0tFWVMuT1JERVJTLCBbXSlcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgb3JkZXIgYnkgSURcbiAgICovXG4gIHN0YXRpYyBnZXRCeUlkKGlkOiBzdHJpbmcpOiBPcmRlciB8IG51bGwge1xuICAgIGNvbnN0IG9yZGVycyA9IHRoaXMuZ2V0QWxsKClcbiAgICByZXR1cm4gb3JkZXJzLmZpbmQobyA9PiBvLmlkID09PSBpZCkgfHwgbnVsbFxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBvcmRlcnMgYnkgdXNlciAodXNpbmcgZW1haWwgYXMgaWRlbnRpZmllcilcbiAgICovXG4gIHN0YXRpYyBnZXRCeVVzZXIodXNlckVtYWlsOiBzdHJpbmcpOiBPcmRlcltdIHtcbiAgICByZXR1cm4gdGhpcy5nZXRBbGwoKS5maWx0ZXIobyA9PiBvLnVzZXJEZXRhaWxzLmVtYWlsID09PSB1c2VyRW1haWwpXG4gIH1cblxuICAvKipcbiAgICogR2V0IG9yZGVycyBieSBzdGF0dXNcbiAgICovXG4gIHN0YXRpYyBnZXRCeVN0YXR1cyhzdGF0dXM6IHN0cmluZyk6IE9yZGVyW10ge1xuICAgIHJldHVybiB0aGlzLmdldEFsbCgpLmZpbHRlcihvID0+IG8uc3RhdHVzID09PSBzdGF0dXMpXG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIG5ldyBvcmRlclxuICAgKi9cbiAgc3RhdGljIGNyZWF0ZShvcmRlckRhdGE6IE9taXQ8T3JkZXIsICdpZCcgfCAnY3JlYXRlZEF0JyB8ICd1cGRhdGVkQXQnPik6IE9yZGVyIHtcbiAgICBjb25zdCBvcmRlcnMgPSB0aGlzLmdldEFsbCgpXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuXG4gICAgY29uc3QgbmV3T3JkZXI6IE9yZGVyID0ge1xuICAgICAgLi4ub3JkZXJEYXRhLFxuICAgICAgaWQ6IGdlbmVyYXRlSWQoJ29yZGVyXycpLFxuICAgICAgY3JlYXRlZEF0OiBub3csXG4gICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgIH1cblxuICAgIG9yZGVycy5wdXNoKG5ld09yZGVyKVxuICAgIFNhZmVTdG9yYWdlLnNldChTVE9SQUdFX0tFWVMuT1JERVJTLCBvcmRlcnMpXG5cbiAgICAvLyBUcmlnZ2VyIHN0b3JhZ2UgZXZlbnRcbiAgICB3aW5kb3cuZGlzcGF0Y2hFdmVudChuZXcgQ3VzdG9tRXZlbnQoJ29yZGVyc1VwZGF0ZWQnLCB7XG4gICAgICBkZXRhaWw6IHsgYWN0aW9uOiAnY3JlYXRlJywgb3JkZXI6IG5ld09yZGVyIH1cbiAgICB9KSlcblxuICAgIHJldHVybiBuZXdPcmRlclxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBvcmRlciBzdGF0dXNcbiAgICovXG4gIHN0YXRpYyB1cGRhdGVTdGF0dXMoaWQ6IHN0cmluZywgc3RhdHVzOiBzdHJpbmcsIG5vdGVzPzogc3RyaW5nKTogT3JkZXIgfCBudWxsIHtcbiAgICBjb25zdCBvcmRlcnMgPSB0aGlzLmdldEFsbCgpXG4gICAgY29uc3QgaW5kZXggPSBvcmRlcnMuZmluZEluZGV4KG8gPT4gby5pZCA9PT0gaWQpXG5cbiAgICBpZiAoaW5kZXggPT09IC0xKSByZXR1cm4gbnVsbFxuXG4gICAgY29uc3QgdXBkYXRlZE9yZGVyOiBPcmRlciA9IHtcbiAgICAgIC4uLm9yZGVyc1tpbmRleF0sXG4gICAgICBzdGF0dXMsXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCksXG4gICAgICAuLi4obm90ZXMgJiYgeyBub3RlcyB9KVxuICAgIH1cblxuICAgIG9yZGVyc1tpbmRleF0gPSB1cGRhdGVkT3JkZXJcbiAgICBTYWZlU3RvcmFnZS5zZXQoU1RPUkFHRV9LRVlTLk9SREVSUywgb3JkZXJzKVxuXG4gICAgLy8gVHJpZ2dlciBzdG9yYWdlIGV2ZW50XG4gICAgd2luZG93LmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCdvcmRlcnNVcGRhdGVkJywge1xuICAgICAgZGV0YWlsOiB7IGFjdGlvbjogJ3VwZGF0ZScsIG9yZGVyOiB1cGRhdGVkT3JkZXIgfVxuICAgIH0pKVxuXG4gICAgcmV0dXJuIHVwZGF0ZWRPcmRlclxuICB9XG5cbiAgLyoqXG4gICAqIEFkZCBkaWdpdGFsIGNvZGVzIHRvIG9yZGVyXG4gICAqL1xuICBzdGF0aWMgYWRkRGlnaXRhbENvZGVzKG9yZGVySWQ6IHN0cmluZywgY29kZXM6IERpZ2l0YWxDb2RlW10pOiBPcmRlciB8IG51bGwge1xuICAgIGNvbnN0IG9yZGVycyA9IHRoaXMuZ2V0QWxsKClcbiAgICBjb25zdCBpbmRleCA9IG9yZGVycy5maW5kSW5kZXgobyA9PiBvLmlkID09PSBvcmRlcklkKVxuXG4gICAgaWYgKGluZGV4ID09PSAtMSkgcmV0dXJuIG51bGxcblxuICAgIGNvbnN0IHVwZGF0ZWRPcmRlcjogT3JkZXIgPSB7XG4gICAgICAuLi5vcmRlcnNbaW5kZXhdLFxuICAgICAgZGlnaXRhbENvZGVzOiBbLi4uKG9yZGVyc1tpbmRleF0uZGlnaXRhbENvZGVzIHx8IFtdKSwgLi4uY29kZXNdLFxuICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpXG4gICAgfVxuXG4gICAgb3JkZXJzW2luZGV4XSA9IHVwZGF0ZWRPcmRlclxuICAgIFNhZmVTdG9yYWdlLnNldChTVE9SQUdFX0tFWVMuT1JERVJTLCBvcmRlcnMpXG5cbiAgICByZXR1cm4gdXBkYXRlZE9yZGVyXG4gIH1cbn1cblxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cbi8vIFVTRVIgU1RPUkFHRSBPUEVSQVRJT05TXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuXG5leHBvcnQgY2xhc3MgVXNlclN0b3JhZ2Uge1xuICAvKipcbiAgICogR2V0IGFsbCB1c2Vyc1xuICAgKi9cbiAgc3RhdGljIGdldEFsbCgpOiBVc2VyW10ge1xuICAgIHJldHVybiBTYWZlU3RvcmFnZS5nZXQ8VXNlcltdPihTVE9SQUdFX0tFWVMuVVNFUlMsIFtdKVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCB1c2VyIGJ5IGVtYWlsXG4gICAqL1xuICBzdGF0aWMgZ2V0QnlFbWFpbChlbWFpbDogc3RyaW5nKTogVXNlciB8IG51bGwge1xuICAgIGNvbnN0IHVzZXJzID0gdGhpcy5nZXRBbGwoKVxuICAgIHJldHVybiB1c2Vycy5maW5kKHUgPT4gdS5lbWFpbCA9PT0gZW1haWwpIHx8IG51bGxcbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgb3IgdXBkYXRlIHVzZXJcbiAgICovXG4gIHN0YXRpYyB1cHNlcnQodXNlckRhdGE6IE9taXQ8VXNlciwgJ2lkJyB8ICdjcmVhdGVkQXQnIHwgJ3VwZGF0ZWRBdCc+KTogVXNlciB7XG4gICAgY29uc3QgdXNlcnMgPSB0aGlzLmdldEFsbCgpXG4gICAgY29uc3QgZXhpc3RpbmdJbmRleCA9IHVzZXJzLmZpbmRJbmRleCh1ID0+IHUuZW1haWwgPT09IHVzZXJEYXRhLmVtYWlsKVxuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKClcblxuICAgIGlmIChleGlzdGluZ0luZGV4ID49IDApIHtcbiAgICAgIC8vIFVwZGF0ZSBleGlzdGluZyB1c2VyXG4gICAgICBjb25zdCB1cGRhdGVkVXNlcjogVXNlciA9IHtcbiAgICAgICAgLi4udXNlcnNbZXhpc3RpbmdJbmRleF0sXG4gICAgICAgIC4uLnVzZXJEYXRhLFxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgICAgfVxuICAgICAgdXNlcnNbZXhpc3RpbmdJbmRleF0gPSB1cGRhdGVkVXNlclxuICAgICAgU2FmZVN0b3JhZ2Uuc2V0KFNUT1JBR0VfS0VZUy5VU0VSUywgdXNlcnMpXG4gICAgICByZXR1cm4gdXBkYXRlZFVzZXJcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQ3JlYXRlIG5ldyB1c2VyXG4gICAgICBjb25zdCBuZXdVc2VyOiBVc2VyID0ge1xuICAgICAgICAuLi51c2VyRGF0YSxcbiAgICAgICAgaWQ6IGdlbmVyYXRlSWQoJ3VzZXJfJyksXG4gICAgICAgIGNyZWF0ZWRBdDogbm93LFxuICAgICAgICB1cGRhdGVkQXQ6IG5vd1xuICAgICAgfVxuICAgICAgdXNlcnMucHVzaChuZXdVc2VyKVxuICAgICAgU2FmZVN0b3JhZ2Uuc2V0KFNUT1JBR0VfS0VZUy5VU0VSUywgdXNlcnMpXG4gICAgICByZXR1cm4gbmV3VXNlclxuICAgIH1cbiAgfVxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gU0VUVElOR1MgU1RPUkFHRVxuLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT1cblxuZXhwb3J0IGNsYXNzIFNldHRpbmdzU3RvcmFnZSB7XG4gIC8qKlxuICAgKiBHZXQgYXBwbGljYXRpb24gc2V0dGluZ3NcbiAgICovXG4gIHN0YXRpYyBnZXQoKTogUmVjb3JkPHN0cmluZywgYW55PiB7XG4gICAgcmV0dXJuIFNhZmVTdG9yYWdlLmdldChTVE9SQUdFX0tFWVMuU0VUVElOR1MsIHt9KVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBzZXR0aW5nc1xuICAgKi9cbiAgc3RhdGljIHVwZGF0ZShzZXR0aW5nczogUmVjb3JkPHN0cmluZywgYW55Pik6IHZvaWQge1xuICAgIGNvbnN0IGN1cnJlbnRTZXR0aW5ncyA9IHRoaXMuZ2V0KClcbiAgICBjb25zdCB1cGRhdGVkU2V0dGluZ3MgPSB7IC4uLmN1cnJlbnRTZXR0aW5ncywgLi4uc2V0dGluZ3MgfVxuICAgIFNhZmVTdG9yYWdlLnNldChTVE9SQUdFX0tFWVMuU0VUVElOR1MsIHVwZGF0ZWRTZXR0aW5ncylcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgc3BlY2lmaWMgc2V0dGluZ1xuICAgKi9cbiAgc3RhdGljIGdldFNldHRpbmc8VD4oa2V5OiBzdHJpbmcsIGRlZmF1bHRWYWx1ZTogVCk6IFQge1xuICAgIGNvbnN0IHNldHRpbmdzID0gdGhpcy5nZXQoKVxuICAgIHJldHVybiBzZXR0aW5nc1trZXldICE9PSB1bmRlZmluZWQgPyBzZXR0aW5nc1trZXldIDogZGVmYXVsdFZhbHVlXG4gIH1cblxuICAvKipcbiAgICogU2V0IHNwZWNpZmljIHNldHRpbmdcbiAgICovXG4gIHN0YXRpYyBzZXRTZXR0aW5nKGtleTogc3RyaW5nLCB2YWx1ZTogYW55KTogdm9pZCB7XG4gICAgdGhpcy51cGRhdGUoeyBba2V5XTogdmFsdWUgfSlcbiAgfVxufVxuXG4vLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gREFUQUJBU0UgSU5JVElBTElaQVRJT05cbi8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09XG5cbi8qKlxuICogSW5pdGlhbGl6ZSB0aGUgZW50aXJlIGxvY2FsU3RvcmFnZSBkYXRhYmFzZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaW5pdGlhbGl6ZURhdGFiYXNlKCk6IHZvaWQge1xuICBjb25zb2xlLmxvZygn8J+UhCBJbml0aWFsaXppbmcgbG9jYWxTdG9yYWdlIGRhdGFiYXNlLi4uJylcblxuICAvLyBJbml0aWFsaXplIHNhbXBsZSBkYXRhIGlmIG5lZWRlZFxuICBQcm9kdWN0U3RvcmFnZS5pbml0aWFsaXplU2FtcGxlRGF0YSgpXG5cbiAgY29uc29sZS5sb2coJ+KchSBEYXRhYmFzZSBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKVxufVxuXG4vKipcbiAqIENsZWFyIGFsbCBkYXRhIChmb3IgZGV2ZWxvcG1lbnQvdGVzdGluZylcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGNsZWFyRGF0YWJhc2UoKTogdm9pZCB7XG4gIGNvbnNvbGUubG9nKCfwn5eR77iPIENsZWFyaW5nIGFsbCBsb2NhbFN0b3JhZ2UgZGF0YS4uLicpXG5cbiAgT2JqZWN0LnZhbHVlcyhTVE9SQUdFX0tFWVMpLmZvckVhY2goa2V5ID0+IHtcbiAgICBTYWZlU3RvcmFnZS5yZW1vdmUoa2V5KVxuICB9KVxuXG4gIGNvbnNvbGUubG9nKCfinIUgRGF0YWJhc2UgY2xlYXJlZCBzdWNjZXNzZnVsbHknKVxufVxuXG4vKipcbiAqIEV4cG9ydCBkYXRhYmFzZSAoZm9yIGJhY2t1cC9taWdyYXRpb24pXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBleHBvcnREYXRhYmFzZSgpOiBSZWNvcmQ8c3RyaW5nLCBhbnk+IHtcbiAgY29uc3QgZGF0YTogUmVjb3JkPHN0cmluZywgYW55PiA9IHt9XG5cbiAgT2JqZWN0LmVudHJpZXMoU1RPUkFHRV9LRVlTKS5mb3JFYWNoKChbbmFtZSwga2V5XSkgPT4ge1xuICAgIGRhdGFbbmFtZV0gPSBTYWZlU3RvcmFnZS5nZXQoa2V5LCBudWxsKVxuICB9KVxuXG4gIHJldHVybiBkYXRhXG59XG5cbi8qKlxuICogSW1wb3J0IGRhdGFiYXNlIChmb3IgYmFja3VwL21pZ3JhdGlvbilcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGltcG9ydERhdGFiYXNlKGRhdGE6IFJlY29yZDxzdHJpbmcsIGFueT4pOiB2b2lkIHtcbiAgT2JqZWN0LmVudHJpZXMoU1RPUkFHRV9LRVlTKS5mb3JFYWNoKChbbmFtZSwga2V5XSkgPT4ge1xuICAgIGlmIChkYXRhW25hbWVdICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIFNhZmVTdG9yYWdlLnNldChrZXksIGRhdGFbbmFtZV0pXG4gICAgfVxuICB9KVxuXG4gIGNvbnNvbGUubG9nKCfinIUgRGF0YWJhc2UgaW1wb3J0ZWQgc3VjY2Vzc2Z1bGx5Jylcbn1cbiJdLCJuYW1lcyI6WyJTVE9SQUdFX0tFWVMiLCJQUk9EVUNUUyIsIk9SREVSUyIsIlVTRVJTIiwiU0VUVElOR1MiLCJDT1VOVEVSUyIsIlNhZmVTdG9yYWdlIiwiZ2V0Iiwia2V5IiwiZGVmYXVsdFZhbHVlIiwiaXRlbSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJKU09OIiwicGFyc2UiLCJlcnJvciIsImNvbnNvbGUiLCJzZXQiLCJ2YWx1ZSIsInNldEl0ZW0iLCJzdHJpbmdpZnkiLCJyZW1vdmUiLCJyZW1vdmVJdGVtIiwiY2xlYXIiLCJnZW5lcmF0ZUlkIiwicHJlZml4IiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInJhbmRvbSIsIk1hdGgiLCJ0b1N0cmluZyIsInN1YnN0ciIsImdldE5leHRJZCIsInR5cGUiLCJjb3VudGVycyIsInByb2R1Y3RzIiwib3JkZXJzIiwidXNlcnMiLCJuZXh0SWQiLCJQcm9kdWN0U3RvcmFnZSIsImdldEFsbCIsImdldEJ5SWQiLCJpZCIsImZpbmQiLCJwIiwiZ2V0QWN0aXZlIiwiZmlsdGVyIiwiaXNBY3RpdmUiLCJnZXRCeUNhdGVnb3J5IiwiY2F0ZWdvcnkiLCJ0b0xvd2VyQ2FzZSIsImluY2x1ZGVzIiwic2VhcmNoIiwicXVlcnkiLCJzZWFyY2hUZXJtIiwibmFtZSIsImRlc2NyaXB0aW9uIiwidGFncyIsInNvbWUiLCJ0YWciLCJjcmVhdGUiLCJwcm9kdWN0RGF0YSIsIm5ld1Byb2R1Y3QiLCJjcmVhdGVkQXQiLCJ1cGRhdGVkQXQiLCJwdXNoIiwid2luZG93IiwiZGlzcGF0Y2hFdmVudCIsIkN1c3RvbUV2ZW50IiwiZGV0YWlsIiwiYWN0aW9uIiwicHJvZHVjdCIsInVwZGF0ZSIsInVwZGF0ZXMiLCJpbmRleCIsImZpbmRJbmRleCIsInVwZGF0ZWRQcm9kdWN0IiwiZGVsZXRlIiwiZGVsZXRlZFByb2R1Y3QiLCJzcGxpY2UiLCJnZXRBdmFpbGFibGVDb2RlcyIsInByb2R1Y3RJZCIsInBhY2thZ2VJZCIsInBrZyIsInBhY2thZ2VzIiwiZGlnaXRhbENvZGVzIiwiY29kZSIsInVzZWQiLCJhc3NpZ25EaWdpdGFsQ29kZSIsIm9yZGVySWQiLCJhdmFpbGFibGVDb2RlIiwiYXNzaWduZWRUb09yZGVySWQiLCJ1c2VkQXQiLCJpbml0aWFsaXplU2FtcGxlRGF0YSIsImV4aXN0aW5nUHJvZHVjdHMiLCJsZW5ndGgiLCJsb2ciLCJzYW1wbGVQcm9kdWN0IiwiaW1hZ2UiLCJkZWxpdmVyeVR5cGUiLCJwcm9kdWN0VHlwZSIsInByb2Nlc3NpbmdUeXBlIiwiZmllbGRzIiwibGFiZWwiLCJwbGFjZWhvbGRlciIsInJlcXVpcmVkIiwic29ydE9yZGVyIiwidmFsaWRhdGlvbiIsIm9wdGlvbnMiLCJhbW91bnQiLCJwcmljZSIsIm9yaWdpbmFsUHJpY2UiLCJwb3B1bGFyIiwiZmVhdHVyZXMiLCJpc0ZlYXR1cmVkIiwiT3JkZXJTdG9yYWdlIiwibyIsImdldEJ5VXNlciIsInVzZXJFbWFpbCIsInVzZXJEZXRhaWxzIiwiZW1haWwiLCJnZXRCeVN0YXR1cyIsInN0YXR1cyIsIm9yZGVyRGF0YSIsIm5ld09yZGVyIiwib3JkZXIiLCJ1cGRhdGVTdGF0dXMiLCJub3RlcyIsInVwZGF0ZWRPcmRlciIsImFkZERpZ2l0YWxDb2RlcyIsImNvZGVzIiwiVXNlclN0b3JhZ2UiLCJnZXRCeUVtYWlsIiwidSIsInVwc2VydCIsInVzZXJEYXRhIiwiZXhpc3RpbmdJbmRleCIsInVwZGF0ZWRVc2VyIiwibmV3VXNlciIsIlNldHRpbmdzU3RvcmFnZSIsInNldHRpbmdzIiwiY3VycmVudFNldHRpbmdzIiwidXBkYXRlZFNldHRpbmdzIiwiZ2V0U2V0dGluZyIsInVuZGVmaW5lZCIsInNldFNldHRpbmciLCJpbml0aWFsaXplRGF0YWJhc2UiLCJjbGVhckRhdGFiYXNlIiwiT2JqZWN0IiwidmFsdWVzIiwiZm9yRWFjaCIsImV4cG9ydERhdGFiYXNlIiwiZGF0YSIsImVudHJpZXMiLCJpbXBvcnREYXRhYmFzZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/storage/localStorage.ts\n"));

/***/ })

});