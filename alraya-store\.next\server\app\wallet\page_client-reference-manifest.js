globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/wallet/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx":{"*":{"id":"(ssr)/./components/chat/GlobalChatProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/DataProvider.tsx":{"*":{"id":"(ssr)/./components/providers/DataProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CurrencyContext.tsx":{"*":{"id":"(ssr)/./contexts/CurrencyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pages/HomePage.tsx":{"*":{"id":"(ssr)/./components/pages/HomePage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/shop/page.tsx":{"*":{"id":"(ssr)/./app/shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/DynamicProductPage.tsx":{"*":{"id":"(ssr)/./components/products/DynamicProductPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pages/WalletPage.tsx":{"*":{"id":"(ssr)/./components/pages/WalletPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(ssr)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"D:\\VS-projects\\try\\alraya-store\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\components\\chat\\GlobalChatProvider.tsx":{"id":"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\components\\providers\\DataProvider.tsx":{"id":"(app-pages-browser)/./components/providers/DataProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\contexts\\CurrencyContext.tsx":{"id":"(app-pages-browser)/./contexts/CurrencyContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Cairo\",\"arguments\":[{\"subsets\":[\"arabic\"],\"display\":\"swap\"}],\"variableName\":\"cairo\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\components\\pages\\HomePage.tsx":{"id":"(app-pages-browser)/./components/pages/HomePage.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\app\\shop\\page.tsx":{"id":"(app-pages-browser)/./app/shop/page.tsx","name":"*","chunks":[],"async":false},"D:\\VS-projects\\try\\alraya-store\\components\\products\\DynamicProductPage.tsx":{"id":"(app-pages-browser)/./components/products/DynamicProductPage.tsx","name":"*","chunks":[],"async":false},"D:\\VS-projects\\try\\alraya-store\\components\\pages\\WalletPage.tsx":{"id":"(app-pages-browser)/./components/pages/WalletPage.tsx","name":"*","chunks":["app/wallet/page","static/chunks/app/wallet/page.js"],"async":false},"D:\\VS-projects\\try\\alraya-store\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"D:\\VS-projects\\try\\alraya-store\\":[],"D:\\VS-projects\\try\\alraya-store\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"D:\\VS-projects\\try\\alraya-store\\app\\page":[],"D:\\VS-projects\\try\\alraya-store\\app\\wallet\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./app/globals.css":{"*":{"id":"(rsc)/./app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx":{"*":{"id":"(rsc)/./components/chat/GlobalChatProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/providers/DataProvider.tsx":{"*":{"id":"(rsc)/./components/providers/DataProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./contexts/CurrencyContext.tsx":{"*":{"id":"(rsc)/./contexts/CurrencyContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pages/HomePage.tsx":{"*":{"id":"(rsc)/./components/pages/HomePage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/shop/page.tsx":{"*":{"id":"(rsc)/./app/shop/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/products/DynamicProductPage.tsx":{"*":{"id":"(rsc)/./components/products/DynamicProductPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./components/pages/WalletPage.tsx":{"*":{"id":"(rsc)/./components/pages/WalletPage.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/admin/page.tsx":{"*":{"id":"(rsc)/./app/admin/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}