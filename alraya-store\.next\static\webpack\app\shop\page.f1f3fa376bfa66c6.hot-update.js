"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./components/layout/MobileNavigation.tsx":
/*!************************************************!*\
  !*** ./components/layout/MobileNavigation.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MobileNavigation: () => (/* binding */ MobileNavigation)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/store.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/wallet.js\");\n/* harmony import */ var _barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,Settings,Store,User,Wallet!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/chat/ChatSystem */ \"(app-pages-browser)/./components/chat/ChatSystem.tsx\");\n/* harmony import */ var _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/chat/GlobalChatProvider */ \"(app-pages-browser)/./components/chat/GlobalChatProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ MobileNavigation auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction MobileNavigation(param) {\n    let { activeTab, onTabChange, unreadChatCount = 0, walletNotificationCount = 0 } = param;\n    _s();\n    const { openChat } = (0,_components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__.useGlobalChat)();\n    // Standardized navigation items matching desktop\n    const navItems = [\n        {\n            id: \"profile\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 28,\n                columnNumber: 13\n            }, this),\n            label: \"حسابي\",\n            action: ()=>onTabChange(\"profile\")\n        },\n        {\n            id: \"shop\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 34,\n                columnNumber: 13\n            }, this),\n            label: \"المتجر\",\n            action: ()=>onTabChange(\"shop\")\n        },\n        {\n            id: \"home\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"h-6 w-6\"\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 40,\n                columnNumber: 13\n            }, this),\n            label: \"الرئيسية\",\n            center: true,\n            action: ()=>onTabChange(\"home\")\n        },\n        {\n            id: \"wallet\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this),\n                    walletNotificationCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__.ChatBadge, {\n                        count: walletNotificationCount,\n                        className: \"absolute -top-2 -right-2 scale-75\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this),\n            label: \"المحفظة\",\n            action: ()=>onTabChange(\"wallet\")\n        },\n        {\n            id: \"support\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_Settings_Store_User_Wallet_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        className: \"h-5 w-5\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this),\n                    unreadChatCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatSystem__WEBPACK_IMPORTED_MODULE_1__.ChatBadge, {\n                        count: unreadChatCount,\n                        className: \"absolute -top-2 -right-2 scale-75\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 64,\n                columnNumber: 9\n            }, this),\n            label: \"الدعم\",\n            action: ()=>openChat() // Opens chat for support\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"lg:hidden fixed bottom-6 left-1/2 w-[calc(100%-2rem)] max-w-sm -translate-x-1/2 z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-slate-800/80 backdrop-blur-2xl rounded-3xl px-6 py-4 shadow-2xl border border-slate-700/50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-around\",\n                children: navItems.map((param)=>{\n                    let { id, icon, label, center } = param;\n                    return center ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>onTabChange(id),\n                        className: \"flex flex-col items-center p-4 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl shadow-lg hover:scale-110 transition-all duration-300 text-slate-900\",\n                        children: icon\n                    }, id, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>{\n                            if (id === 'chat') {\n                                openChat();\n                            } else {\n                                onTabChange(id);\n                            }\n                        },\n                        className: \"flex flex-col items-center space-y-1 p-3 rounded-2xl transition-all duration-300 hover:scale-110 \".concat(activeTab === id ? \"bg-white/20 text-yellow-400 shadow-lg\" : \"text-slate-400 hover:text-white hover:bg-white/10\"),\n                        children: [\n                            icon,\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs font-medium\",\n                                children: label\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, id, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 15\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n                lineNumber: 82,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n            lineNumber: 81,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\layout\\\\MobileNavigation.tsx\",\n        lineNumber: 80,\n        columnNumber: 5\n    }, this);\n}\n_s(MobileNavigation, \"gwvX/mRH6xKscAo9IuM/CxxGiZg=\", false, function() {\n    return [\n        _components_chat_GlobalChatProvider__WEBPACK_IMPORTED_MODULE_2__.useGlobalChat\n    ];\n});\n_c = MobileNavigation;\nvar _c;\n$RefreshReg$(_c, \"MobileNavigation\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvbGF5b3V0L01vYmlsZU5hdmlnYXRpb24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBRWlGO0FBQ3pCO0FBQ1k7QUFXN0QsU0FBU08saUJBQWlCLEtBS1Q7UUFMUyxFQUMvQkMsU0FBUyxFQUNUQyxXQUFXLEVBQ1hDLGtCQUFrQixDQUFDLEVBQ25CQywwQkFBMEIsQ0FBQyxFQUNMLEdBTFM7O0lBTS9CLE1BQU0sRUFBRUMsUUFBUSxFQUFFLEdBQUdOLGtGQUFhQTtJQUVsQyxpREFBaUQ7SUFDakQsTUFBTU8sV0FBVztRQUNmO1lBQ0VDLElBQUk7WUFDSkMsb0JBQU0sOERBQUNmLDJHQUFJQTtnQkFBQ2dCLFdBQVU7Ozs7OztZQUN0QkMsT0FBTztZQUNQQyxRQUFRLElBQU1ULFlBQVk7UUFDNUI7UUFDQTtZQUNFSyxJQUFJO1lBQ0pDLG9CQUFNLDhEQUFDWCwyR0FBS0E7Z0JBQUNZLFdBQVU7Ozs7OztZQUN2QkMsT0FBTztZQUNQQyxRQUFRLElBQU1ULFlBQVk7UUFDNUI7UUFDQTtZQUNFSyxJQUFJO1lBQ0pDLG9CQUFNLDhEQUFDZCwyR0FBSUE7Z0JBQUNlLFdBQVU7Ozs7OztZQUN0QkMsT0FBTztZQUNQRSxRQUFRO1lBQ1JELFFBQVEsSUFBTVQsWUFBWTtRQUM1QjtRQUNBO1lBQ0VLLElBQUk7WUFDSkMsb0JBQ0UsOERBQUNLO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ2IsMkdBQU1BO3dCQUFDYSxXQUFVOzs7Ozs7b0JBQ2pCTCwwQkFBMEIsbUJBQ3pCLDhEQUFDTixrRUFBU0E7d0JBQ1JnQixPQUFPVjt3QkFDUEssV0FBVTs7Ozs7Ozs7Ozs7O1lBS2xCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTVQsWUFBWTtRQUM1QjtRQUNBO1lBQ0VLLElBQUk7WUFDSkMsb0JBQ0UsOERBQUNLO2dCQUFJSixXQUFVOztrQ0FDYiw4REFBQ2QsMkdBQVFBO3dCQUFDYyxXQUFVOzs7Ozs7b0JBQ25CTixrQkFBa0IsbUJBQ2pCLDhEQUFDTCxrRUFBU0E7d0JBQ1JnQixPQUFPWDt3QkFDUE0sV0FBVTs7Ozs7Ozs7Ozs7O1lBS2xCQyxPQUFPO1lBQ1BDLFFBQVEsSUFBTU4sV0FBVyx5QkFBeUI7UUFDcEQ7S0FDRDtJQUVELHFCQUNFLDhEQUFDVTtRQUFJTixXQUFVO2tCQUNiLDRFQUFDSTtZQUFJSixXQUFVO3NCQUNiLDRFQUFDSTtnQkFBSUosV0FBVTswQkFDWkgsU0FBU1UsR0FBRyxDQUFDO3dCQUFDLEVBQUVULEVBQUUsRUFBRUMsSUFBSSxFQUFFRSxLQUFLLEVBQUVFLE1BQU0sRUFBRTsyQkFDeENBLHVCQUNFLDhEQUFDSzt3QkFFQ0MsU0FBUyxJQUFNaEIsWUFBWUs7d0JBQzNCRSxXQUFVO2tDQUVURDt1QkFKSUQ7Ozs7NkNBT1AsOERBQUNVO3dCQUVDQyxTQUFTOzRCQUNQLElBQUlYLE9BQU8sUUFBUTtnQ0FDakJGOzRCQUNGLE9BQU87Z0NBQ0xILFlBQVlLOzRCQUNkO3dCQUNGO3dCQUNBRSxXQUFXLG9HQUlWLE9BSENSLGNBQWNNLEtBQ1YsMENBQ0E7OzRCQUdMQzswQ0FDRCw4REFBQ1c7Z0NBQUtWLFdBQVU7MENBQXVCQzs7Ozs7Ozt1QkFmbENIOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUF1QnJCO0dBckdnQlA7O1FBTU9ELDhFQUFhQTs7O0tBTnBCQyIsInNvdXJjZXMiOlsiRDpcXFZTLXByb2plY3RzXFx0cnlcXGFscmF5YS1zdG9yZVxcY29tcG9uZW50c1xcbGF5b3V0XFxNb2JpbGVOYXZpZ2F0aW9uLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBVc2VyLCBIb21lLCBTZXR0aW5ncywgV2FsbGV0LCBTdG9yZSwgTWVzc2FnZUNpcmNsZSB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHsgQ2hhdEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL2NoYXQvQ2hhdFN5c3RlbSdcbmltcG9ydCB7IHVzZUdsb2JhbENoYXQgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9HbG9iYWxDaGF0UHJvdmlkZXInXG5cbmludGVyZmFjZSBNb2JpbGVOYXZpZ2F0aW9uUHJvcHMge1xuICBhY3RpdmVUYWI6IHN0cmluZ1xuICBvblRhYkNoYW5nZTogKHRhYjogc3RyaW5nKSA9PiB2b2lkXG4gIC8vICMjIENoYXQgSW50ZWdyYXRpb246IFVucmVhZCBtZXNzYWdlIGNvdW50IGZvciBjaGF0IGJhZGdlXG4gIHVucmVhZENoYXRDb3VudD86IG51bWJlclxuICAvLyAjIyBEaWdpdGFsIENvbnRlbnQ6IE5vdGlmaWNhdGlvbiBjb3VudCBmb3Igd2FsbGV0IGJhZGdlXG4gIHdhbGxldE5vdGlmaWNhdGlvbkNvdW50PzogbnVtYmVyXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBNb2JpbGVOYXZpZ2F0aW9uKHtcbiAgYWN0aXZlVGFiLFxuICBvblRhYkNoYW5nZSxcbiAgdW5yZWFkQ2hhdENvdW50ID0gMCxcbiAgd2FsbGV0Tm90aWZpY2F0aW9uQ291bnQgPSAwXG59OiBNb2JpbGVOYXZpZ2F0aW9uUHJvcHMpIHtcbiAgY29uc3QgeyBvcGVuQ2hhdCB9ID0gdXNlR2xvYmFsQ2hhdCgpXG5cbiAgLy8gU3RhbmRhcmRpemVkIG5hdmlnYXRpb24gaXRlbXMgbWF0Y2hpbmcgZGVza3RvcFxuICBjb25zdCBuYXZJdGVtcyA9IFtcbiAgICB7XG4gICAgICBpZDogXCJwcm9maWxlXCIsXG4gICAgICBpY29uOiA8VXNlciBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz4sXG4gICAgICBsYWJlbDogXCLYrdiz2KfYqNmKXCIsXG4gICAgICBhY3Rpb246ICgpID0+IG9uVGFiQ2hhbmdlKFwicHJvZmlsZVwiKVxuICAgIH0sXG4gICAge1xuICAgICAgaWQ6IFwic2hvcFwiLFxuICAgICAgaWNvbjogPFN0b3JlIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPixcbiAgICAgIGxhYmVsOiBcItin2YTZhdiq2KzYsVwiLFxuICAgICAgYWN0aW9uOiAoKSA9PiBvblRhYkNoYW5nZShcInNob3BcIilcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcImhvbWVcIixcbiAgICAgIGljb246IDxIb21lIGNsYXNzTmFtZT1cImgtNiB3LTZcIiAvPixcbiAgICAgIGxhYmVsOiBcItin2YTYsdim2YrYs9mK2KlcIixcbiAgICAgIGNlbnRlcjogdHJ1ZSxcbiAgICAgIGFjdGlvbjogKCkgPT4gb25UYWJDaGFuZ2UoXCJob21lXCIpXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogXCJ3YWxsZXRcIixcbiAgICAgIGljb246IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgIDxXYWxsZXQgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAge3dhbGxldE5vdGlmaWNhdGlvbkNvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICA8Q2hhdEJhZGdlXG4gICAgICAgICAgICAgIGNvdW50PXt3YWxsZXROb3RpZmljYXRpb25Db3VudH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWJzb2x1dGUgLXRvcC0yIC1yaWdodC0yIHNjYWxlLTc1XCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICApLFxuICAgICAgbGFiZWw6IFwi2KfZhNmF2K3Zgdi42KlcIixcbiAgICAgIGFjdGlvbjogKCkgPT4gb25UYWJDaGFuZ2UoXCJ3YWxsZXRcIilcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiBcInN1cHBvcnRcIixcbiAgICAgIGljb246IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgIDxTZXR0aW5ncyBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICB7dW5yZWFkQ2hhdENvdW50ID4gMCAmJiAoXG4gICAgICAgICAgICA8Q2hhdEJhZGdlXG4gICAgICAgICAgICAgIGNvdW50PXt1bnJlYWRDaGF0Q291bnR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIC10b3AtMiAtcmlnaHQtMiBzY2FsZS03NVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgKSxcbiAgICAgIGxhYmVsOiBcItin2YTYr9i52YVcIixcbiAgICAgIGFjdGlvbjogKCkgPT4gb3BlbkNoYXQoKSAvLyBPcGVucyBjaGF0IGZvciBzdXBwb3J0XG4gICAgfSxcbiAgXVxuXG4gIHJldHVybiAoXG4gICAgPG5hdiBjbGFzc05hbWU9XCJsZzpoaWRkZW4gZml4ZWQgYm90dG9tLTYgbGVmdC0xLzIgdy1bY2FsYygxMDAlLTJyZW0pXSBtYXgtdy1zbSAtdHJhbnNsYXRlLXgtMS8yIHotNDBcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc2xhdGUtODAwLzgwIGJhY2tkcm9wLWJsdXItMnhsIHJvdW5kZWQtM3hsIHB4LTYgcHktNCBzaGFkb3ctMnhsIGJvcmRlciBib3JkZXItc2xhdGUtNzAwLzUwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1hcm91bmRcIj5cbiAgICAgICAgICB7bmF2SXRlbXMubWFwKCh7IGlkLCBpY29uLCBsYWJlbCwgY2VudGVyIH0pID0+XG4gICAgICAgICAgICBjZW50ZXIgPyAoXG4gICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICBrZXk9e2lkfVxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uVGFiQ2hhbmdlKGlkKX1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGl0ZW1zLWNlbnRlciBwLTQgYmctZ3JhZGllbnQtdG8tciBmcm9tLXllbGxvdy00MDAgdG8tb3JhbmdlLTUwMCByb3VuZGVkLTJ4bCBzaGFkb3ctbGcgaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCB0ZXh0LXNsYXRlLTkwMFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICB7aWNvbn1cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAga2V5PXtpZH1cbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICBpZiAoaWQgPT09ICdjaGF0Jykge1xuICAgICAgICAgICAgICAgICAgICBvcGVuQ2hhdCgpXG4gICAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBvblRhYkNoYW5nZShpZClcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHNwYWNlLXktMSBwLTMgcm91bmRlZC0yeGwgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwIGhvdmVyOnNjYWxlLTExMCAke1xuICAgICAgICAgICAgICAgICAgYWN0aXZlVGFiID09PSBpZFxuICAgICAgICAgICAgICAgICAgICA/IFwiYmctd2hpdGUvMjAgdGV4dC15ZWxsb3ctNDAwIHNoYWRvdy1sZ1wiXG4gICAgICAgICAgICAgICAgICAgIDogXCJ0ZXh0LXNsYXRlLTQwMCBob3Zlcjp0ZXh0LXdoaXRlIGhvdmVyOmJnLXdoaXRlLzEwXCJcbiAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtpY29ufVxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHMgZm9udC1tZWRpdW1cIj57bGFiZWx9PC9zcGFuPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICksXG4gICAgICAgICAgKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L25hdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlVzZXIiLCJIb21lIiwiU2V0dGluZ3MiLCJXYWxsZXQiLCJTdG9yZSIsIkNoYXRCYWRnZSIsInVzZUdsb2JhbENoYXQiLCJNb2JpbGVOYXZpZ2F0aW9uIiwiYWN0aXZlVGFiIiwib25UYWJDaGFuZ2UiLCJ1bnJlYWRDaGF0Q291bnQiLCJ3YWxsZXROb3RpZmljYXRpb25Db3VudCIsIm9wZW5DaGF0IiwibmF2SXRlbXMiLCJpZCIsImljb24iLCJjbGFzc05hbWUiLCJsYWJlbCIsImFjdGlvbiIsImNlbnRlciIsImRpdiIsImNvdW50IiwibmF2IiwibWFwIiwiYnV0dG9uIiwib25DbGljayIsInNwYW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout/MobileNavigation.tsx\n"));

/***/ })

});