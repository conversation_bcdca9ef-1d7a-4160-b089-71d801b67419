"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/[slug]/page",{

/***/ "(app-pages-browser)/./components/products/DynamicProductPage.tsx":
/*!****************************************************!*\
  !*** ./components/products/DynamicProductPage.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DynamicProductPage: () => (/* binding */ DynamicProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Clock,Download,Gift,Shield,Sparkles,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_services_orderService__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/services/orderService */ \"(app-pages-browser)/./lib/services/orderService.ts\");\n/* harmony import */ var _SimpleProductForm__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./SimpleProductForm */ \"(app-pages-browser)/./components/products/SimpleProductForm.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* __next_internal_client_entry_do_not_use__ DynamicProductPage auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DynamicProductPage(param) {\n    let { productId } = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"shop\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_13__.useToast)();\n    // Load product from the new product management system\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DynamicProductPage.useEffect\": ()=>{\n            loadProduct();\n        }\n    }[\"DynamicProductPage.useEffect\"], [\n        productId\n    ]);\n    /**\n   * ## TODO: Replace with Supabase query\n   * Load product by ID\n   */ const loadProduct = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            console.log('\\uD83D\\uDD04 Loading product with ID: \"'.concat(productId, '\"'));\n            const productData = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_10__.getProductById)(productId);\n            if (!productData) {\n                console.log('❌ Product not found: \"'.concat(productId, '\"'));\n                setError(\"المنتج غير موجود\");\n                return;\n            }\n            if (!productData.isActive) {\n                console.log('⚠️ Product inactive: \"'.concat(productId, '\"'));\n                setError(\"هذا المنتج غير متاح حالياً\");\n                return;\n            }\n            console.log('✅ Product loaded successfully: \"'.concat(productData.name, '\"'));\n            setProduct(productData);\n        } catch (error) {\n            console.error(\"Error loading product:\", error);\n            setError(\"حدث خطأ أثناء تحميل المنتج\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Navigation handler\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n        } else if (tab === \"support\") {\n            router.push(\"/contact\");\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    // Handle form submission (purchase)\n    const handlePurchase = async (formData)=>{\n        try {\n            console.log('Purchase data:', formData);\n            // Create mock user details (in real app, this would come from auth)\n            const userDetails = {\n                name: \"مستخدم تجريبي\",\n                email: \"<EMAIL>\",\n                phone: \"+966500000000\",\n                address: \"الرياض، المملكة العربية السعودية\"\n            };\n            toast({\n                title: \"جاري معالجة الطلب...\",\n                description: \"يرجى الانتظار\"\n            });\n            // Create order using our localStorage system\n            const order = await (0,_lib_services_orderService__WEBPACK_IMPORTED_MODULE_11__.createOrderFromProduct)(formData, userDetails);\n            toast({\n                title: \"تم إنشاء الطلب بنجاح!\",\n                description: \"رقم الطلب: \".concat(order.id)\n            });\n            // Redirect to order success page with order ID\n            router.push(\"/orders/\".concat(order.id));\n        } catch (error) {\n            console.error(\"Error processing purchase:\", error);\n            toast({\n                title: \"خطأ في معالجة الطلب\",\n                description: error instanceof Error ? error.message : \"حدث خطأ غير متوقع\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"⏳\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-2\",\n                        children: \"جاري تحميل المنتج...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-400\",\n                        children: \"يرجى الانتظار\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n            lineNumber: 141,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center max-w-md mx-auto px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-6xl mb-4\",\n                        children: \"❌\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold text-white mb-2\",\n                        children: error || \"المنتج غير موجود\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-slate-400 mb-4\",\n                        children: error || \"لم نتمكن من العثور على المنتج المطلوب\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                     true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-slate-800/50 rounded-lg p-4 mb-6 text-left text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-400 font-medium mb-2\",\n                                children: \"معلومات التطوير:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300\",\n                                children: [\n                                    \"المعرف المطلوب: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-400\",\n                                        children: productId\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 61\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 mt-1\",\n                                children: \"المنتجات المتاحة:\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"text-slate-400 text-xs mt-1 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• pubg-mobile-uc\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• free-fire-diamonds\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• google-play-gift-card\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• tiktok-coins\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        onClick: ()=>router.push('/shop'),\n                        className: \"bg-yellow-500 hover:bg-yellow-600 text-slate-900\",\n                        children: \"العودة للمتجر\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 154,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 189,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_6__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_7__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"container mx-auto px-4 py-8 pb-32 lg:pb-8 relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        onClick: ()=>router.back(),\n                        className: \"mb-6 text-slate-300 hover:text-white hover:bg-slate-800/50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-4 w-4 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            \"العودة\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-6xl mx-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm overflow-hidden\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square\",\n                                                    children: [\n                                                        product.previewImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.previewImage,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                            lineNumber: 218,\n                                                            columnNumber: 23\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-24 w-24 text-slate-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                            lineNumber: 224,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-4 right-4 space-y-2\",\n                                                            children: [\n                                                                product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white font-bold animate-pulse\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 234,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"منتج رقمي\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"\".concat(product.processingType === \"instant\" ? 'bg-green-500/20 text-green-400 border-green-500/30' : 'bg-blue-500/20 text-blue-400 border-blue-500/30'),\n                                                                    children: product.processingType === \"instant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                lineNumber: 248,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            product.estimatedTime\n                                                                        ]\n                                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                lineNumber: 253,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            product.estimatedTime\n                                                                        ]\n                                                                    }, void 0, true)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                    lineNumber: 239,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-purple-500/20 text-purple-400 border-purple-500/30\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"منتج رقمي\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                            className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                                className: \"p-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-bold text-white mb-4 flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-5 w-5 text-green-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"مميزات المنتج\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: product.features && product.features.length > 0 ? product.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-slate-300 text-sm\",\n                                                                        children: feature\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                        lineNumber: 282,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                lineNumber: 280,\n                                                                columnNumber: 25\n                                                            }, this)) : // Default features based on product type\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                product.productType === \"digital\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 291,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-slate-300 text-sm\",\n                                                                                    children: \"\\uD83D\\uDE80 تسليم فوري للمحتوى الرقمي\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 292,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 290,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-slate-300 text-sm\",\n                                                                                    children: \"\\uD83D\\uDD12 محتوى محمي ومشفر\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 296,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 302,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-slate-300 text-sm\",\n                                                                                    children: \"⚡ شحن سريع وآمن\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 303,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 301,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-3\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 306,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-slate-300 text-sm\",\n                                                                                    children: \"\\uD83D\\uDEE1️ ضمان الجودة\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 305,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Clock_Download_Gift_Shield_Sparkles_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-green-400 flex-shrink-0\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 312,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-slate-300 text-sm\",\n                                                                            children: \"\\uD83D\\uDCAC دعم فني متخصص\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                            lineNumber: 313,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_SimpleProductForm__WEBPACK_IMPORTED_MODULE_12__.SimpleProductForm, {\n                                        template: product,\n                                        onSubmit: handlePurchase,\n                                        currency: \"USD\" // ## TODO: Get from user preferences\n                                        ,\n                                        showPricing: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                            lineNumber: 209,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_8__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 335,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_9__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n                lineNumber: 336,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\components\\\\products\\\\DynamicProductPage.tsx\",\n        lineNumber: 187,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicProductPage, \"H8w3eh/A0aAMjKD32mWR72xCPKo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_13__.useToast\n    ];\n});\n_c = DynamicProductPage;\nvar _c;\n$RefreshReg$(_c, \"DynamicProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/products/DynamicProductPage.tsx\n"));

/***/ })

});