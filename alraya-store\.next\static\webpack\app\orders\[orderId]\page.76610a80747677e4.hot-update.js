"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/orders/[orderId]/page",{

/***/ "(app-pages-browser)/./lib/services/orderService.ts":
/*!**************************************!*\
  !*** ./lib/services/orderService.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignDigitalCodesToOrder: () => (/* binding */ assignDigitalCodesToOrder),\n/* harmony export */   cancelOrder: () => (/* binding */ cancelOrder),\n/* harmony export */   createOrderFromProduct: () => (/* binding */ createOrderFromProduct),\n/* harmony export */   getOrderById: () => (/* binding */ getOrderById),\n/* harmony export */   getOrderStats: () => (/* binding */ getOrderStats),\n/* harmony export */   getOrders: () => (/* binding */ getOrders),\n/* harmony export */   getOrdersByStatus: () => (/* binding */ getOrdersByStatus),\n/* harmony export */   getOrdersByUser: () => (/* binding */ getOrdersByUser),\n/* harmony export */   processPendingOrders: () => (/* binding */ processPendingOrders),\n/* harmony export */   refundOrder: () => (/* binding */ refundOrder),\n/* harmony export */   updateOrderStatus: () => (/* binding */ updateOrderStatus)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _productService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/**\n * Order Service\n * \n * Handles all order-related operations using localStorage\n */ \n\n/**\n * Create order from product form data\n */ async function createOrderFromProduct(formData, userDetails) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 300));\n    try {\n        // Validate that the selected package is still available\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(formData.templateId);\n        if (!product) {\n            throw new Error('Product not found');\n        }\n        const selectedPackage = product.packages.find((pkg)=>pkg.id === formData.selectedPackage.id);\n        if (!selectedPackage || !selectedPackage.isActive) {\n            throw new Error('Selected package is no longer available');\n        }\n        // Enhanced availability check for different product types\n        if (selectedPackage.digitalCodes && selectedPackage.digitalCodes.length > 0) {\n            // Digital products with codes - check code availability\n            const availableCodes = selectedPackage.digitalCodes.filter((code)=>!code.used);\n            if (availableCodes.length < formData.quantity) {\n                throw new Error(\"Only \".concat(availableCodes.length, \" codes available, but \").concat(formData.quantity, \" requested\"));\n            }\n        } else if (selectedPackage.quantityLimit !== undefined && selectedPackage.quantityLimit !== null) {\n            // Products with manual quantity limits\n            if (selectedPackage.quantityLimit < formData.quantity) {\n                throw new Error(\"Only \".concat(selectedPackage.quantityLimit, \" items available, but \").concat(formData.quantity, \" requested\"));\n            }\n        }\n        // For unlimited digital products/services (no codes, no limits), no availability check needed\n        // Create the order\n        const orderData = {\n            productId: formData.templateId,\n            productName: product.name,\n            packageId: formData.selectedPackage.id,\n            packageName: formData.selectedPackage.name,\n            quantity: formData.quantity,\n            unitPrice: formData.selectedPackage.price,\n            totalPrice: formData.totalPrice,\n            currency: formData.currency,\n            status: 'pending',\n            userDetails,\n            customFields: formData.customFields,\n            digitalCodes: [],\n            processingType: product.processingType,\n            deliveryType: product.deliveryType,\n            timeline: [\n                {\n                    id: \"timeline_\".concat(Date.now()),\n                    status: 'pending',\n                    timestamp: new Date(),\n                    message: 'Order created and awaiting processing',\n                    isVisible: true\n                }\n            ]\n        };\n        const newOrder = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.create(orderData);\n        // Auto-assign digital codes if it's an instant processing product\n        if (product.processingType === 'instant' && selectedPackage.digitalCodes) {\n            await assignDigitalCodesToOrder(newOrder.id, formData.templateId, formData.selectedPackage.id, formData.quantity);\n        }\n        console.log(\"✅ Created order: \".concat(newOrder.id, \" for product: \").concat(product.name));\n        return newOrder;\n    } catch (error) {\n        console.error('Error creating order:', error);\n        throw error;\n    }\n}\n/**\n * Assign digital codes to an order\n */ async function assignDigitalCodesToOrder(orderId, productId, packageId, quantity) {\n    const assignedCodes = [];\n    try {\n        for(let i = 0; i < quantity; i++){\n            const code = await (0,_productService__WEBPACK_IMPORTED_MODULE_1__.assignDigitalCode)(productId, packageId, orderId);\n            if (code) {\n                assignedCodes.push(code);\n            } else {\n                throw new Error(\"Failed to assign digital code \".concat(i + 1, \" of \").concat(quantity));\n            }\n        }\n        // Add codes to order\n        if (assignedCodes.length > 0) {\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.addDigitalCodes(orderId, assignedCodes);\n            // Update order status to completed if all codes assigned\n            _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'completed', \"\".concat(assignedCodes.length, \" digital codes assigned\"));\n        }\n        return assignedCodes;\n    } catch (error) {\n        console.error('Error assigning digital codes:', error);\n        // Update order status to failed\n        _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, 'failed', \"Failed to assign digital codes: \".concat(error));\n        throw error;\n    }\n}\n/**\n * Get all orders\n */ async function getOrders() {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getAll();\n}\n/**\n * Get order by ID\n */ async function getOrderById(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getById(id);\n}\n/**\n * Get orders by user email\n */ async function getOrdersByUser(userEmail) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByUser(userEmail);\n}\n/**\n * Get orders by status\n */ async function getOrdersByStatus(status) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.getByStatus(status);\n}\n/**\n * Update order status\n */ async function updateOrderStatus(orderId, status, notes) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.OrderStorage.updateStatus(orderId, status, notes);\n}\n/**\n * Process pending orders (for admin use)\n */ async function processPendingOrders() {\n    const pendingOrders = await getOrdersByStatus('pending');\n    let processed = 0;\n    let failed = 0;\n    const errors = [];\n    for (const order of pendingOrders){\n        try {\n            // Try to assign digital codes if not already assigned\n            if (order.digitalCodes.length === 0 && order.packageId) {\n                await assignDigitalCodesToOrder(order.id, order.productId, order.packageId, order.quantity);\n                processed++;\n            } else {\n                // Just mark as completed if codes already assigned\n                await updateOrderStatus(order.id, 'completed', 'Order processed successfully');\n                processed++;\n            }\n        } catch (error) {\n            failed++;\n            errors.push(\"Order \".concat(order.id, \": \").concat(error));\n            await updateOrderStatus(order.id, 'failed', \"Processing failed: \".concat(error));\n        }\n    }\n    return {\n        processed,\n        failed,\n        errors\n    };\n}\n/**\n * Get order statistics\n */ async function getOrderStats() {\n    const orders = await getOrders();\n    const stats = {\n        total: orders.length,\n        pending: orders.filter((o)=>o.status === 'pending').length,\n        completed: orders.filter((o)=>o.status === 'completed').length,\n        failed: orders.filter((o)=>o.status === 'failed').length,\n        totalRevenue: orders.filter((o)=>o.status === 'completed').reduce((sum, o)=>sum + o.totalPrice, 0)\n    };\n    return stats;\n}\n/**\n * Cancel order (if still pending)\n */ async function cancelOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order || order.status !== 'pending') {\n        return false;\n    }\n    const updated = await updateOrderStatus(orderId, 'cancelled', reason || 'Order cancelled by user');\n    return !!updated;\n}\n/**\n * Refund order (release digital codes back to pool)\n */ async function refundOrder(orderId, reason) {\n    const order = await getOrderById(orderId);\n    if (!order) return false;\n    try {\n        // Release digital codes back to the product\n        if (order.digitalCodes.length > 0) {\n            const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(order.productId);\n            if (product) {\n                const pkg = product.packages.find((p)=>p.id === order.packageId);\n                if (pkg && pkg.digitalCodes) {\n                    // Mark codes as unused and remove order assignment\n                    order.digitalCodes.forEach((orderCode)=>{\n                        const productCode = pkg.digitalCodes.find((pc)=>pc.id === orderCode.id);\n                        if (productCode) {\n                            productCode.used = false;\n                            productCode.assignedToOrderId = null;\n                            productCode.usedAt = undefined;\n                        }\n                    });\n                    // Update product in storage\n                    _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(order.productId, product);\n                }\n            }\n        }\n        // Update order status\n        await updateOrderStatus(orderId, 'refunded', reason || 'Order refunded');\n        return true;\n    } catch (error) {\n        console.error('Error refunding order:', error);\n        return false;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/orderService.ts\n"));

/***/ })

});