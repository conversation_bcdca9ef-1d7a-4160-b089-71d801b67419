"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/shop/page",{

/***/ "(app-pages-browser)/./app/shop/page.tsx":
/*!***************************!*\
  !*** ./app/shop/page.tsx ***!
  \***************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ShopPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/layout/AppHeader */ \"(app-pages-browser)/./components/layout/AppHeader.tsx\");\n/* harmony import */ var _components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/layout/SideMenu */ \"(app-pages-browser)/./components/layout/SideMenu.tsx\");\n/* harmony import */ var _components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/shared/NewsTicket */ \"(app-pages-browser)/./components/shared/NewsTicket.tsx\");\n/* harmony import */ var _components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout/MobileNavigation */ \"(app-pages-browser)/./components/layout/MobileNavigation.tsx\");\n/* harmony import */ var _components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/layout/DesktopFooter */ \"(app-pages-browser)/./components/layout/DesktopFooter.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gift.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gamepad-2.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,CreditCard,Filter,Gamepad2,Gift,Key,MessageCircle,Package,Search,Sparkles,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/contexts/CurrencyContext */ \"(app-pages-browser)/./contexts/CurrencyContext.tsx\");\n/* harmony import */ var _lib_services_productService__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/services/productService */ \"(app-pages-browser)/./lib/services/productService.ts\");\n/* harmony import */ var _lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/utils/pricingUtils */ \"(app-pages-browser)/./lib/utils/pricingUtils.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Enhanced categories with icons\nconst categories = [\n    {\n        id: \"all\",\n        label: \"جميع المنتجات\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 35,\n            columnNumber: 46\n        }, undefined)\n    },\n    {\n        id: \"ألعاب الموبايل\",\n        label: \"ألعاب الموبايل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 36,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"منصات التواصل\",\n        label: \"منصات التواصل\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 56\n        }, undefined)\n    },\n    {\n        id: \"بطاقات الألعاب\",\n        label: \"بطاقات الألعاب\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 38,\n            columnNumber: 58\n        }, undefined)\n    },\n    {\n        id: \"digital\",\n        label: \"🎮 منتجات رقمية\",\n        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n            className: \"h-4 w-4\"\n        }, void 0, false, {\n            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n            lineNumber: 39,\n            columnNumber: 52\n        }, undefined)\n    }\n];\nfunction ShopPage() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\");\n    const [isMenuOpen, setIsMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Use global currency context for price conversion\n    const { formatPrice } = (0,_contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter)();\n    // Load products from localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ShopPage.useEffect\": ()=>{\n            const loadProducts = {\n                \"ShopPage.useEffect.loadProducts\": async ()=>{\n                    try {\n                        setIsLoading(true);\n                        const productList = await (0,_lib_services_productService__WEBPACK_IMPORTED_MODULE_13__.getProducts)();\n                        setProducts(productList);\n                    } catch (error) {\n                        console.error('Error loading products:', error);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"ShopPage.useEffect.loadProducts\"];\n            loadProducts();\n            // Listen for real-time product updates\n            const handleProductsUpdated = {\n                \"ShopPage.useEffect.handleProductsUpdated\": (event)=>{\n                    console.log('Products updated:', event.detail);\n                    loadProducts() // Reload products when they change\n                    ;\n                }\n            }[\"ShopPage.useEffect.handleProductsUpdated\"];\n            window.addEventListener('productsUpdated', handleProductsUpdated);\n            return ({\n                \"ShopPage.useEffect\": ()=>{\n                    window.removeEventListener('productsUpdated', handleProductsUpdated);\n                }\n            })[\"ShopPage.useEffect\"];\n        }\n    }[\"ShopPage.useEffect\"], []);\n    // Navigation handler\n    const handleTabChange = (tab)=>{\n        if (tab === \"wallet\") {\n            router.push(\"/wallet\");\n        } else if (tab === \"profile\") {\n            router.push(\"/profile\");\n        } else if (tab === \"shop\") {\n            router.push(\"/shop\");\n        } else if (tab === \"home\") {\n            router.push(\"/\");\n        } else if (tab === \"support\") {\n            router.push(\"/contact\");\n        } else {\n            setActiveTab(tab);\n        }\n    };\n    // Product click handler\n    const handleProductClick = (productId)=>{\n        router.push(\"/shop/\".concat(productId));\n    };\n    // Filter products based on search and category\n    const filteredProducts = products.filter((product)=>{\n        const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.description && product.description.toLowerCase().includes(searchQuery.toLowerCase());\n        // Handle category filtering including digital products\n        let matchesCategory = false;\n        if (selectedCategory === \"all\") {\n            matchesCategory = true;\n        } else if (selectedCategory === \"digital\") {\n            matchesCategory = product.productType === \"digital\";\n        } else {\n            matchesCategory = product.category === selectedCategory;\n        }\n        return matchesSearch && matchesCategory;\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 overflow-hidden pointer-events-none\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-yellow-400/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-orange-500/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_AppHeader__WEBPACK_IMPORTED_MODULE_7__.AppHeader, {\n                onMenuOpen: ()=>setIsMenuOpen(true)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_shared_NewsTicket__WEBPACK_IMPORTED_MODULE_9__.NewsTicket, {}, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_SideMenu__WEBPACK_IMPORTED_MODULE_8__.SideMenu, {\n                isOpen: isMenuOpen,\n                onClose: ()=>setIsMenuOpen(false)\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10 container mx-auto px-4 py-8 max-w-6xl pt-32 pb-32\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl lg:text-5xl font-bold bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent mb-4\",\n                                children: \"متجر الألعاب\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-300 text-lg\",\n                                children: \"اكتشف أفضل العروض لشحن ألعابك المفضلة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n                                            placeholder: \"ابحث عن لعبتك المفضلة...\",\n                                            value: searchQuery,\n                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                            className: \"pr-10 bg-slate-800/50 border-slate-700/50 text-white placeholder:text-slate-400 focus:border-yellow-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: selectedCategory,\n                                        onChange: (e)=>setSelectedCategory(e.target.value),\n                                        className: \"w-full pr-10 pl-4 py-3 bg-slate-800/50 border border-slate-700/50 rounded-lg text-white focus:border-yellow-400 focus:outline-none appearance-none\",\n                                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: category.id,\n                                                className: \"bg-slate-800\",\n                                                children: category.label\n                                            }, category.id, false, {\n                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-3 mb-8\",\n                        children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: selectedCategory === category.id ? \"default\" : \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setSelectedCategory(category.id),\n                                className: \"flex items-center gap-2 \".concat(selectedCategory === category.id ? \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 border-0\" : \"border-slate-600 text-slate-300 hover:border-yellow-400 hover:text-yellow-400\"),\n                                children: [\n                                    category.icon,\n                                    category.label\n                                ]\n                            }, category.id, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-6xl mb-4\",\n                                children: \"⏳\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-bold text-white mb-2\",\n                                children: \"جاري تحميل المنتجات...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-slate-400\",\n                                children: \"يرجى الانتظار\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 md:gap-6\",\n                                children: filteredProducts.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                        onClick: ()=>handleProductClick(product.id),\n                                        className: \"bg-slate-800/50 border-slate-700/50 backdrop-blur-sm hover:bg-slate-700/50 hover:border-yellow-400/30 hover:shadow-2xl hover:shadow-yellow-400/10 transition-all duration-500 cursor-pointer group overflow-hidden transform hover:scale-105 hover:-translate-y-2\",\n                                        style: {\n                                            animationDelay: \"\".concat(index * 100, \"ms\")\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"p-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative aspect-square overflow-hidden\",\n                                                    children: [\n                                                        product.previewImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: product.previewImage,\n                                                            alt: product.name,\n                                                            className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full h-full bg-gradient-to-br from-slate-700 via-slate-600 to-slate-800 flex items-center justify-center relative overflow-hidden\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 group-hover:from-yellow-400/10 group-hover:to-orange-500/10 transition-all duration-500\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 233,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-12 w-12 text-slate-400 group-hover:text-yellow-400 transition-colors duration-300 relative z-10\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 234,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                        lineNumber: 242,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"أكواد فورية\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.isProductPopular)(product) && product.productType !== \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 animate-pulse\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg\",\n                                                                children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        (0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.isProductPopular)(product) && product.productType === \"digital\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 right-3 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-bold shadow-lg animate-pulse block\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"أكواد فورية\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-slate-900 text-xs font-bold shadow-lg block\",\n                                                                    children: \"\\uD83D\\uDD25 الأكثر طلباً\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-3 left-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs font-medium border-0 \".concat(product.processingType === \"instant\" ? 'bg-green-500/20 text-green-400' : 'bg-blue-500/20 text-blue-400'),\n                                                                children: product.processingType === \"instant\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 282,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"فوري\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 287,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        product.estimatedTime || \"يدوي\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 bg-gradient-to-t from-slate-900/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 w-full\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between text-white\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-3 w-3 text-yellow-400 fill-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 299,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs font-medium\",\n                                                                                    children: \"4.8\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 300,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs text-slate-300\",\n                                                                                    children: \"(جديد)\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                                    lineNumber: 301,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs font-bold text-yellow-400\",\n                                                                            children: [\n                                                                                \"من \",\n                                                                                formatPrice((0,_lib_utils_pricingUtils__WEBPACK_IMPORTED_MODULE_14__.getProductStartingPrice)(product))\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 303,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-white font-bold text-center group-hover:text-yellow-400 transition-colors duration-300 text-sm leading-tight\",\n                                                            children: product.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-400 text-xs text-center mt-2 line-clamp-2 group-hover:text-slate-300 transition-colors duration-300\",\n                                                            children: product.description || \"منتج رائع من متجر الراية\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 316,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center gap-2 mt-3 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 323,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: product.estimatedTime\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 324,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: \"•\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 326,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-1 text-slate-500\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_CreditCard_Filter_Gamepad2_Gift_Key_MessageCircle_Package_Search_Sparkles_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                            className: \"h-3 w-3\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 328,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: [\n                                                                                product.packages.length,\n                                                                                \" حزمة\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                            lineNumber: 329,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                                    lineNumber: 327,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, product.id, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, this),\n                            filteredProducts.length === 0 && !isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-6xl mb-4\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-bold text-white mb-2\",\n                                        children: \"لا توجد منتجات\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-400\",\n                                        children: products.length === 0 ? \"لم يتم إنشاء أي منتجات بعد. قم بإنشاء منتجات من لوحة الإدارة.\" : \"لم نجد أي منتجات تطابق بحثك. جرب كلمات مختلفة أو اختر فئة أخرى.\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_MobileNavigation__WEBPACK_IMPORTED_MODULE_10__.MobileNavigation, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_DesktopFooter__WEBPACK_IMPORTED_MODULE_11__.DesktopFooter, {\n                activeTab: activeTab,\n                onTabChange: handleTabChange\n            }, void 0, false, {\n                fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\VS-projects\\\\try\\\\alraya-store\\\\app\\\\shop\\\\page.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(ShopPage, \"A8mhKoSiVoIazGCOv9+WNuSP9O8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _contexts_CurrencyContext__WEBPACK_IMPORTED_MODULE_12__.useCurrencyConverter\n    ];\n});\n_c = ShopPage;\nvar _c;\n$RefreshReg$(_c, \"ShopPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/shop/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/data/defaultProductTemplates.ts":
/*!*********************************************!*\
  !*** ./lib/data/defaultProductTemplates.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultTemplates: () => (/* binding */ defaultTemplates),\n/* harmony export */   deleteProductTemplate: () => (/* binding */ deleteProductTemplate),\n/* harmony export */   forceReinitializeTemplates: () => (/* binding */ forceReinitializeTemplates),\n/* harmony export */   freeFireTemplate: () => (/* binding */ freeFireTemplate),\n/* harmony export */   googlePlayTemplate: () => (/* binding */ googlePlayTemplate),\n/* harmony export */   initializeDefaultTemplates: () => (/* binding */ initializeDefaultTemplates),\n/* harmony export */   loadProductTemplates: () => (/* binding */ loadProductTemplates),\n/* harmony export */   pubgMobileTemplate: () => (/* binding */ pubgMobileTemplate),\n/* harmony export */   saveProductTemplate: () => (/* binding */ saveProductTemplate),\n/* harmony export */   tiktokCoinsTemplate: () => (/* binding */ tiktokCoinsTemplate)\n/* harmony export */ });\n// =====================================================\n// DEFAULT PRODUCT TEMPLATES\n// =====================================================\n// ## TODO: Replace with Supabase data loading\n// These templates will be used to initialize the system with sample products\n/**\n * Generate consistent ID for templates (fixed IDs for stability)\n */ function generateId() {\n    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '', suffix = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '';\n    // Use consistent IDs instead of random ones to avoid localStorage issues\n    return \"\".concat(prefix).concat(Date.now().toString(36)).concat(suffix).concat(Math.random().toString(36).substr(2, 4));\n}\n/**\n * Generate fixed ID for template components (for consistency)\n */ function fixedId(id) {\n    return id;\n}\n/**\n * PUBG Mobile UC Top-up Template\n */ const pubgMobileTemplate = {\n    id: \"pubg-mobile-uc\",\n    name: \"شحن يوسي PUBG Mobile\",\n    nameEnglish: \"PUBG Mobile UC Top-up\",\n    description: \"شحن فوري لعملة UC في لعبة PUBG Mobile - احصل على يوسي فوراً بأفضل الأسعار\",\n    descriptionEnglish: \"Instant UC top-up for PUBG Mobile - Get your UC instantly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 25,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم إرسال الكود إلى حسابك فوراً بعد الدفع. استخدم الكود في اللعبة لشحن UC.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"pubg-player-id\"),\n            type: \"text\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                minLength: 8,\n                maxLength: 12,\n                pattern: \"^[0-9]+$\"\n            },\n            sortOrder: 0,\n            isActive: true\n        },\n        {\n            id: fixedId(\"pubg-server\"),\n            type: \"dropdown\",\n            name: \"server\",\n            label: \"الخادم\",\n            placeholder: \"اختر الخادم...\",\n            required: true,\n            options: [\n                \"الشرق الأوسط\",\n                \"أوروبا\",\n                \"آسيا\",\n                \"أمريكا الشمالية\",\n                \"أمريكا الجنوبية\"\n            ],\n            sortOrder: 1,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"pubg-uc-60\"),\n            name: \"60 يوسي\",\n            amount: \"60 UC\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-325\"),\n            name: \"325 يوسي\",\n            amount: \"325 UC\",\n            price: 25,\n            originalPrice: 30,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-660\"),\n            name: \"660 يوسي\",\n            amount: \"660 UC\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        },\n        {\n            id: fixedId(\"pubg-uc-1800\"),\n            name: \"1800 يوسي\",\n            amount: \"1800 UC\",\n            price: 120,\n            originalPrice: 150,\n            discount: 20,\n            popular: false,\n            isActive: true,\n            sortOrder: 3,\n            digitalCodes: [] // ## TODO: Add encrypted codes\n        }\n    ],\n    features: [\n        \"🚀 تسليم فوري للأكواد\",\n        \"💯 ضمان الجودة والأمان\",\n        \"🔒 معاملات آمنة ومشفرة\",\n        \"📱 يعمل على جميع الأجهزة\",\n        \"🎮 دعم فني متخصص\",\n        \"💳 طرق دفع متعددة\"\n    ],\n    tags: [\n        \"pubg\",\n        \"mobile\",\n        \"uc\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Free Fire Diamonds Template\n */ const freeFireTemplate = {\n    id: \"free-fire-diamonds\",\n    name: \"شحن جواهر Free Fire\",\n    nameEnglish: \"Free Fire Diamonds Top-up\",\n    description: \"شحن فوري لجواهر Free Fire - احصل على الجواهر بأسرع وقت وأفضل الأسعار\",\n    descriptionEnglish: \"Instant Free Fire Diamonds top-up - Get your diamonds quickly at the best prices\",\n    category: \"ألعاب الموبايل\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"game_code\",\n        deliveryInstructions: \"سيتم شحن الجواهر مباشرة إلى حسابك في اللعبة خلال دقائق.\",\n        expiryDays: 7\n    },\n    fields: [\n        {\n            id: fixedId(\"ff-player-id\"),\n            type: \"number\",\n            name: \"player_id\",\n            label: \"معرف اللاعب\",\n            labelEnglish: \"Player ID\",\n            placeholder: \"أدخل معرف اللاعب...\",\n            required: true,\n            validation: {\n                min: 100000000,\n                max: 9999999999\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"ff-diamonds-100\"),\n            name: \"100 جوهرة\",\n            amount: \"100 💎\",\n            price: 10,\n            originalPrice: 12,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-520\"),\n            name: \"520 جوهرة\",\n            amount: \"520 💎\",\n            price: 50,\n            originalPrice: 60,\n            discount: 17,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"ff-diamonds-1080\"),\n            name: \"1080 جوهرة\",\n            amount: \"1080 💎\",\n            price: 100,\n            originalPrice: 120,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💎 جواهر أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎮 دعم فني 24/7\"\n    ],\n    tags: [\n        \"free fire\",\n        \"diamonds\",\n        \"جواهر\",\n        \"شحن\",\n        \"ألعاب\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * Google Play Gift Card Template\n */ const googlePlayTemplate = {\n    id: \"google-play-gift-card\",\n    name: \"بطاقة هدايا Google Play\",\n    nameEnglish: \"Google Play Gift Card\",\n    description: \"بطاقات هدايا Google Play الرقمية - استخدمها لشراء التطبيقات والألعاب والمحتوى الرقمي\",\n    descriptionEnglish: \"Digital Google Play Gift Cards - Use them to buy apps, games, and digital content\",\n    category: \"بطاقات الهدايا\",\n    basePrice: 10,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"coupon\",\n        deliveryInstructions: \"استخدم الكود في متجر Google Play لإضافة الرصيد إلى حسابك.\",\n        expiryDays: 365\n    },\n    fields: [\n        {\n            id: fixedId(\"gp-email\"),\n            type: \"email\",\n            name: \"email\",\n            label: \"البريد الإلكتروني\",\n            labelEnglish: \"Email Address\",\n            placeholder: \"أدخل بريدك الإلكتروني...\",\n            required: true,\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"gp-usd-10\"),\n            name: \"$10 USD\",\n            nameArabic: \"10 دولار\",\n            amount: \"$10 USD\",\n            price: 10,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-25\"),\n            name: \"$25 USD\",\n            nameArabic: \"25 دولار\",\n            amount: \"$25 USD\",\n            price: 25,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"gp-usd-50\"),\n            name: \"$50 USD\",\n            nameArabic: \"50 دولار\",\n            amount: \"$50 USD\",\n            price: 50,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🎁 بطاقة هدايا رقمية\",\n        \"🚀 تسليم فوري\",\n        \"🌍 صالحة عالمياً\",\n        \"📱 لجميع أجهزة Android\",\n        \"🔒 آمنة ومضمونة\"\n    ],\n    tags: [\n        \"google play\",\n        \"gift card\",\n        \"بطاقة هدايا\",\n        \"تطبيقات\"\n    ],\n    isActive: true,\n    isFeatured: false,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * TikTok Coins Template\n */ const tiktokCoinsTemplate = {\n    id: \"tiktok-coins\",\n    name: \"شحن عملات TikTok\",\n    nameEnglish: \"TikTok Coins Top-up\",\n    description: \"شحن فوري لعملات TikTok - ادعم المبدعين المفضلين لديك واحصل على المزيد من المزايا\",\n    descriptionEnglish: \"Instant TikTok Coins top-up - Support your favorite creators and get more features\",\n    category: \"وسائل التواصل\",\n    basePrice: 5,\n    estimatedTime: \"فوري\",\n    productType: \"digital\",\n    processingType: \"instant\",\n    digitalConfig: {\n        autoDeliver: true,\n        codeType: \"gift_code\",\n        deliveryInstructions: \"سيتم إضافة العملات إلى حسابك في TikTok فوراً بعد الدفع.\",\n        expiryDays: 30\n    },\n    fields: [\n        {\n            id: fixedId(\"tiktok-username\"),\n            type: \"text\",\n            name: \"username\",\n            label: \"اسم المستخدم في TikTok\",\n            placeholder: \"أدخل اسم المستخدم...\",\n            required: true,\n            validation: {\n                minLength: 3,\n                maxLength: 30\n            },\n            sortOrder: 0,\n            isActive: true\n        }\n    ],\n    packages: [\n        {\n            id: fixedId(\"tiktok-coins-100\"),\n            name: \"100 عملة\",\n            amount: \"100 Coins\",\n            price: 5,\n            originalPrice: 6,\n            discount: 17,\n            popular: false,\n            isActive: true,\n            sortOrder: 0,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-500\"),\n            name: \"500 عملة\",\n            amount: \"500 Coins\",\n            price: 20,\n            originalPrice: 25,\n            discount: 20,\n            popular: true,\n            isActive: true,\n            sortOrder: 1,\n            digitalCodes: []\n        },\n        {\n            id: fixedId(\"tiktok-coins-1000\"),\n            name: \"1000 عملة\",\n            amount: \"1000 Coins\",\n            price: 35,\n            originalPrice: 45,\n            discount: 22,\n            popular: false,\n            isActive: true,\n            sortOrder: 2,\n            digitalCodes: []\n        }\n    ],\n    features: [\n        \"🚀 شحن فوري ومباشر\",\n        \"💰 عملات أصلية 100%\",\n        \"🔒 آمن ومضمون\",\n        \"📱 لجميع الأجهزة\",\n        \"🎁 ادعم المبدعين المفضلين\"\n    ],\n    tags: [\n        \"tiktok\",\n        \"coins\",\n        \"عملات\",\n        \"شحن\",\n        \"وسائل التواصل\"\n    ],\n    isActive: true,\n    isFeatured: true,\n    createdAt: new Date(),\n    updatedAt: new Date()\n};\n/**\n * All default templates\n */ const defaultTemplates = [\n    pubgMobileTemplate,\n    freeFireTemplate,\n    googlePlayTemplate,\n    tiktokCoinsTemplate\n];\n/**\n * Initialize default templates in localStorage (client-side only)\n * ## TODO: Replace with Supabase initialization\n */ function initializeDefaultTemplates() {\n    // Check if we're in a browser environment\n    if (false) {}\n    try {\n        const existingTemplates = localStorage.getItem('productTemplates');\n        if (!existingTemplates) {\n            console.log('Initializing default product templates...');\n            localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            console.log(\"Initialized \".concat(defaultTemplates.length, \" default templates\"));\n        } else {\n            // Validate existing templates\n            try {\n                const parsed = JSON.parse(existingTemplates);\n                if (!Array.isArray(parsed) || parsed.length === 0) {\n                    console.log('Invalid templates found, reinitializing...');\n                    localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n                } else {\n                    console.log(\"Found \".concat(parsed.length, \" existing templates in localStorage\"));\n                }\n            } catch (parseError) {\n                console.log('Corrupted templates found, reinitializing...');\n                localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n            }\n        }\n    } catch (error) {\n        console.error('Error initializing default templates:', error);\n    }\n}\n/**\n * Force reinitialize templates (useful for debugging)\n */ function forceReinitializeTemplates() {\n    if (false) {}\n    try {\n        console.log('Force reinitializing product templates...');\n        localStorage.setItem('productTemplates', JSON.stringify(defaultTemplates));\n        console.log(\"Reinitialized \".concat(defaultTemplates.length, \" templates\"));\n    } catch (error) {\n        console.error('Error force reinitializing templates:', error);\n    }\n}\n/**\n * Load product templates from localStorage\n * ## TODO: Replace with Supabase query\n */ function loadProductTemplates() {\n    try {\n        const savedTemplates = localStorage.getItem('productTemplates');\n        if (savedTemplates) {\n            return JSON.parse(savedTemplates);\n        }\n        return [];\n    } catch (error) {\n        console.error('Error loading product templates:', error);\n        return [];\n    }\n}\n/**\n * Save product template to localStorage\n * ## TODO: Replace with Supabase insert/update\n */ function saveProductTemplate(template) {\n    try {\n        const templates = loadProductTemplates();\n        const existingIndex = templates.findIndex((t)=>t.id === template.id);\n        if (existingIndex >= 0) {\n            templates[existingIndex] = template;\n        } else {\n            templates.push(template);\n        }\n        localStorage.setItem('productTemplates', JSON.stringify(templates));\n    } catch (error) {\n        console.error('Error saving product template:', error);\n        throw error;\n    }\n}\n/**\n * Delete product template from localStorage\n * ## TODO: Replace with Supabase delete\n */ function deleteProductTemplate(templateId) {\n    try {\n        const templates = loadProductTemplates();\n        const filteredTemplates = templates.filter((t)=>t.id !== templateId);\n        localStorage.setItem('productTemplates', JSON.stringify(filteredTemplates));\n    } catch (error) {\n        console.error('Error deleting product template:', error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/data/defaultProductTemplates.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/services/productService.ts":
/*!****************************************!*\
  !*** ./lib/services/productService.ts ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addPackageToProduct: () => (/* binding */ addPackageToProduct),\n/* harmony export */   assignDigitalCode: () => (/* binding */ assignDigitalCode),\n/* harmony export */   createProduct: () => (/* binding */ createProduct),\n/* harmony export */   deleteProduct: () => (/* binding */ deleteProduct),\n/* harmony export */   getAvailableCodes: () => (/* binding */ getAvailableCodes),\n/* harmony export */   getProductById: () => (/* binding */ getProductById),\n/* harmony export */   getProductPackages: () => (/* binding */ getProductPackages),\n/* harmony export */   getProductStats: () => (/* binding */ getProductStats),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getProductsByCategory: () => (/* binding */ getProductsByCategory),\n/* harmony export */   hardDeleteProduct: () => (/* binding */ hardDeleteProduct),\n/* harmony export */   searchProducts: () => (/* binding */ searchProducts),\n/* harmony export */   updateProduct: () => (/* binding */ updateProduct)\n/* harmony export */ });\n/* harmony import */ var _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/storage/localStorage */ \"(app-pages-browser)/./lib/storage/localStorage.ts\");\n/* harmony import */ var _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data/defaultProductTemplates */ \"(app-pages-browser)/./lib/data/defaultProductTemplates.ts\");\n// =====================================================\n// PRODUCT MANAGEMENT SERVICE\n// =====================================================\n// ## TODO: Implement Supabase integration for all functions\n// ## DATABASE LATER: Connect to products, packages, custom_fields tables\n\n\n// =====================================================\n// PRODUCT CRUD OPERATIONS\n// =====================================================\n/**\n * ## TODO: Implement Supabase product fetching\n * Fetch all products with optional filtering\n */ async function getProducts(filters) {\n    // Initialize database and ensure sample data exists\n    if (true) {\n        (0,_lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase)();\n        // Check if we need to initialize with sample data\n        const existingProducts = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAll();\n        if (existingProducts.length === 0) {\n            console.log('🔄 Initializing with sample products...');\n            (0,_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.initializeDefaultTemplates)();\n            // Add default templates to localStorage\n            for (const template of _lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates){\n                _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(template);\n            }\n        }\n    }\n    // Simulate API delay for realistic UX\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        // On server-side, return default templates\n        if (false) {}\n        // On client-side, load from localStorage\n        const products = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getActive();\n        return applyFilters(products, filters);\n    } catch (error) {\n        console.error('Error loading products:', error);\n        // Fallback to default templates\n        return applyFilters(_lib_data_defaultProductTemplates__WEBPACK_IMPORTED_MODULE_1__.defaultTemplates, filters);\n    }\n}\n/**\n * ## TODO: Implement Supabase product fetching by ID\n * Fetch single product by ID\n */ async function getProductById(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    try {\n        console.log('\\uD83D\\uDD0D Looking for product with ID: \"'.concat(id, '\"'));\n        // On server-side, search in default templates\n        if (false) {}\n        // On client-side, search in localStorage\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (product && product.isActive) {\n            console.log('✅ Found product: \"'.concat(product.name, '\" (Active: ').concat(product.isActive, \")\"));\n            return product;\n        } else {\n            console.log('❌ Product with ID \"'.concat(id, '\" not found or inactive'));\n            return null;\n        }\n    } catch (error) {\n        console.error('Error in getProductById:', error);\n        return null;\n    }\n}\n/**\n * ## TODO: Implement Supabase product creation\n * Create new product with packages and fields\n */ async function createProduct(product) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Use our new localStorage system\n        const newProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.create(product);\n        console.log('✅ Created product: \"'.concat(newProduct.name, '\" with ID: ').concat(newProduct.id));\n        return newProduct;\n    } catch (error) {\n        console.error('Error creating product:', error);\n        throw new Error('Failed to create product');\n    }\n}\n/**\n * ## TODO: Implement Supabase product update\n * Update existing product\n */ async function updateProduct(id, updates) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const updatedProduct = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, updates);\n        if (!updatedProduct) {\n            throw new Error(\"Product with id \".concat(id, \" not found\"));\n        }\n        console.log('✅ Updated product: \"'.concat(updatedProduct.name, '\"'));\n        return updatedProduct;\n    } catch (error) {\n        console.error('Error updating product:', error);\n        throw error;\n    }\n}\n/**\n * ## TODO: Implement Supabase product deletion\n * Delete product and related data\n */ async function deleteProduct(id) {\n    // Simulate API delay\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        // Soft delete by setting isActive to false\n        const product = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getById(id);\n        if (!product) return false;\n        const updated = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.update(id, {\n            isActive: false\n        });\n        console.log('✅ Soft deleted product: \"'.concat(product.name, '\"'));\n        return !!updated;\n    } catch (error) {\n        console.error('Error deleting product:', error);\n        return false;\n    }\n}\n/**\n * Hard delete product (completely remove from storage)\n */ async function hardDeleteProduct(id) {\n    await new Promise((resolve)=>setTimeout(resolve, 200));\n    try {\n        const success = _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.delete(id);\n        console.log(\"✅ Hard deleted product with ID: \".concat(id));\n        return success;\n    } catch (error) {\n        console.error('Error hard deleting product:', error);\n        return false;\n    }\n}\n// =====================================================\n// PACKAGE MANAGEMENT\n// =====================================================\n/**\n * ## TODO: Implement Supabase package operations\n * Get packages for a specific product\n */ async function getProductPackages(productId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    const product = await getProductById(productId);\n    return (product === null || product === void 0 ? void 0 : product.packages.filter((pkg)=>pkg.isActive)) || [];\n}\n/**\n * Get products by category\n */ async function getProductsByCategory(category) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getByCategory(category);\n}\n/**\n * Search products\n */ async function searchProducts(query) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    if (false) {}\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.search(query);\n}\n/**\n * Get available digital codes for a package\n */ async function getAvailableCodes(productId, packageId) {\n    await new Promise((resolve)=>setTimeout(resolve, 50));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.getAvailableCodes(productId, packageId);\n}\n/**\n * Assign digital code to order\n */ async function assignDigitalCode(productId, packageId, orderId) {\n    await new Promise((resolve)=>setTimeout(resolve, 100));\n    return _lib_storage_localStorage__WEBPACK_IMPORTED_MODULE_0__.ProductStorage.assignDigitalCode(productId, packageId, orderId);\n}\n/**\n * ## TODO: Implement Supabase package creation\n * Add package to product\n */ async function addPackageToProduct(productId, packageData) {\n    // ## TODO: Replace with Supabase insert\n    /*\n  const { data, error } = await supabase\n    .from('product_packages')\n    .insert({\n      product_id: productId,\n      name: packageData.name,\n      // ... other fields\n    })\n    .select()\n    .single()\n  \n  if (error) throw error\n  return transformPackageFromDB(data)\n  */ const newPackage = {\n        ...packageData,\n        id: generateId()\n    };\n    const product = await getProductById(productId);\n    if (!product) throw new Error('Product not found');\n    product.packages.push(newPackage);\n    await updateProduct(productId, {\n        packages: product.packages\n    });\n    return newPackage;\n}\n// =====================================================\n// STATISTICS AND ANALYTICS\n// =====================================================\n/**\n * ## TODO: Implement Supabase analytics queries\n * Get product statistics for admin dashboard\n */ async function getProductStats() {\n    // ## TODO: Replace with Supabase aggregation queries\n    /*\n  const [\n    totalProducts,\n    activeProducts,\n    digitalProducts,\n    totalPackages,\n    totalOrders,\n    popularCategories\n  ] = await Promise.all([\n    supabase.from('products').select('id', { count: 'exact' }),\n    supabase.from('products').select('id', { count: 'exact' }).eq('is_active', true),\n    supabase.from('products').select('id', { count: 'exact' }).eq('product_type', 'digital'),\n    supabase.from('product_packages').select('id', { count: 'exact' }),\n    supabase.from('orders').select('id', { count: 'exact' }),\n    supabase.from('products').select('category').groupBy('category')\n  ])\n  */ // Temporary: Calculate from localStorage\n    const products = await getProducts();\n    // Ensure products is an array and has valid structure\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    return {\n        totalProducts: validProducts.length,\n        activeProducts: validProducts.filter((p)=>p.isActive === true).length,\n        digitalProducts: validProducts.filter((p)=>p.productType === 'digital').length,\n        physicalProducts: validProducts.filter((p)=>p.productType === 'physical').length,\n        totalPackages: validProducts.reduce((sum, p)=>{\n            const packages = p.packages || [];\n            return sum + (Array.isArray(packages) ? packages.length : 0);\n        }, 0),\n        totalOrders: 0,\n        popularCategories: getPopularCategories(validProducts)\n    };\n}\n// =====================================================\n// HELPER FUNCTIONS\n// =====================================================\n/**\n * Apply filters to products array (temporary implementation)\n */ function applyFilters(products, filters) {\n    // Ensure products is a valid array\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && typeof p === 'object') : [];\n    if (!filters) return validProducts;\n    return validProducts.filter((product)=>{\n        // Ensure product has required properties\n        if (!product.name || !product.category) return false;\n        if (filters.category && product.category !== filters.category) return false;\n        if (filters.productType && product.productType !== filters.productType) return false;\n        if (filters.processingType && product.processingType !== filters.processingType) return false;\n        if (filters.isActive !== undefined && product.isActive !== filters.isActive) return false;\n        if (filters.isFeatured !== undefined && product.isFeatured !== filters.isFeatured) return false;\n        if (filters.search) {\n            const searchLower = filters.search.toLowerCase();\n            const nameMatch = product.name && product.name.toLowerCase().includes(searchLower);\n            const descMatch = product.description && product.description.toLowerCase().includes(searchLower);\n            if (!nameMatch && !descMatch) return false;\n        }\n        return true;\n    });\n}\n/**\n * Get popular categories from products\n */ function getPopularCategories(products) {\n    const categoryCount = {};\n    // Ensure products is an array and filter valid products\n    const validProducts = Array.isArray(products) ? products.filter((p)=>p && p.category) : [];\n    validProducts.forEach((product)=>{\n        if (product.category && typeof product.category === 'string') {\n            categoryCount[product.category] = (categoryCount[product.category] || 0) + 1;\n        }\n    });\n    return Object.entries(categoryCount).map((param)=>{\n        let [category, count] = param;\n        return {\n            category,\n            count\n        };\n    }).sort((a, b)=>b.count - a.count).slice(0, 5);\n}\n/**\n * Generate unique ID (temporary implementation)\n */ function generateId() {\n    return Math.random().toString(36).substr(2, 9);\n}\n// =====================================================\n// DATA TRANSFORMATION HELPERS\n// =====================================================\n/**\n * ## TODO: Transform database product to ProductTemplate interface\n */ function transformProductFromDB(dbProduct) {\n    // ## TODO: Implement transformation from Supabase row to ProductTemplate\n    return dbProduct;\n}\n/**\n * ## TODO: Transform database package to ProductPackage interface\n */ function transformPackageFromDB(dbPackage) {\n    // ## TODO: Implement transformation from Supabase row to ProductPackage\n    return dbPackage;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/services/productService.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/storage/localStorage.ts":
/*!*************************************!*\
  !*** ./lib/storage/localStorage.ts ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrderStorage: () => (/* binding */ OrderStorage),\n/* harmony export */   ProductStorage: () => (/* binding */ ProductStorage),\n/* harmony export */   SettingsStorage: () => (/* binding */ SettingsStorage),\n/* harmony export */   UserStorage: () => (/* binding */ UserStorage),\n/* harmony export */   clearDatabase: () => (/* binding */ clearDatabase),\n/* harmony export */   exportDatabase: () => (/* binding */ exportDatabase),\n/* harmony export */   importDatabase: () => (/* binding */ importDatabase),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/**\n * Local Storage Database System\n * \n * Provides a complete database-like interface using localStorage\n * Handles products, orders, users, and digital codes\n */ // Storage keys\nconst STORAGE_KEYS = {\n    PRODUCTS: 'alraya_products',\n    ORDERS: 'alraya_orders',\n    USERS: 'alraya_users',\n    SETTINGS: 'alraya_settings',\n    COUNTERS: 'alraya_counters'\n};\n// =====================================================\n// CORE STORAGE UTILITIES\n// =====================================================\n/**\n * Safe localStorage operations with error handling\n */ class SafeStorage {\n    static get(key, defaultValue) {\n        try {\n            if (false) {}\n            const item = localStorage.getItem(key);\n            return item ? JSON.parse(item) : defaultValue;\n        } catch (error) {\n            console.error('Error reading from localStorage key \"'.concat(key, '\":'), error);\n            return defaultValue;\n        }\n    }\n    static set(key, value) {\n        try {\n            if (false) {}\n            localStorage.setItem(key, JSON.stringify(value));\n            return true;\n        } catch (error) {\n            console.error('Error writing to localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static remove(key) {\n        try {\n            if (false) {}\n            localStorage.removeItem(key);\n            return true;\n        } catch (error) {\n            console.error('Error removing localStorage key \"'.concat(key, '\":'), error);\n            return false;\n        }\n    }\n    static clear() {\n        try {\n            if (false) {}\n            localStorage.clear();\n            return true;\n        } catch (error) {\n            console.error('Error clearing localStorage:', error);\n            return false;\n        }\n    }\n}\n/**\n * Generate unique IDs\n */ function generateId() {\n    let prefix = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : '';\n    const timestamp = Date.now();\n    const random = Math.random().toString(36).substr(2, 9);\n    return \"\".concat(prefix).concat(timestamp, \"_\").concat(random);\n}\n/**\n * Get and increment counter\n */ function getNextId(type) {\n    const counters = SafeStorage.get(STORAGE_KEYS.COUNTERS, {\n        products: 1,\n        orders: 1,\n        users: 1\n    });\n    const nextId = counters[type];\n    counters[type] = nextId + 1;\n    SafeStorage.set(STORAGE_KEYS.COUNTERS, counters);\n    return nextId;\n}\n// =====================================================\n// PRODUCT STORAGE OPERATIONS\n// =====================================================\nclass ProductStorage {\n    /**\n   * Get all products\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.PRODUCTS, []);\n    }\n    /**\n   * Get product by ID\n   */ static getById(id) {\n        const products = this.getAll();\n        return products.find((p)=>p.id === id) || null;\n    }\n    /**\n   * Get active products only\n   */ static getActive() {\n        return this.getAll().filter((p)=>p.isActive);\n    }\n    /**\n   * Get products by category\n   */ static getByCategory(category) {\n        return this.getActive().filter((p)=>p.category.toLowerCase().includes(category.toLowerCase()));\n    }\n    /**\n   * Search products\n   */ static search(query) {\n        const searchTerm = query.toLowerCase();\n        return this.getActive().filter((p)=>{\n            var _p_description, _p_tags;\n            return p.name.toLowerCase().includes(searchTerm) || ((_p_description = p.description) === null || _p_description === void 0 ? void 0 : _p_description.toLowerCase().includes(searchTerm)) || p.category.toLowerCase().includes(searchTerm) || ((_p_tags = p.tags) === null || _p_tags === void 0 ? void 0 : _p_tags.some((tag)=>tag.toLowerCase().includes(searchTerm)));\n        });\n    }\n    /**\n   * Create new product\n   */ static create(productData) {\n        const products = this.getAll();\n        const now = new Date();\n        const newProduct = {\n            ...productData,\n            id: generateId('prod_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        products.push(newProduct);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event for real-time updates\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'create',\n                product: newProduct\n            }\n        }));\n        return newProduct;\n    }\n    /**\n   * Update existing product\n   */ static update(id, updates) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return null;\n        const updatedProduct = {\n            ...products[index],\n            ...updates,\n            id,\n            updatedAt: new Date()\n        };\n        products[index] = updatedProduct;\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'update',\n                product: updatedProduct\n            }\n        }));\n        return updatedProduct;\n    }\n    /**\n   * Delete product\n   */ static delete(id) {\n        const products = this.getAll();\n        const index = products.findIndex((p)=>p.id === id);\n        if (index === -1) return false;\n        const deletedProduct = products[index];\n        products.splice(index, 1);\n        SafeStorage.set(STORAGE_KEYS.PRODUCTS, products);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('productsUpdated', {\n            detail: {\n                action: 'delete',\n                product: deletedProduct\n            }\n        }));\n        return true;\n    }\n    /**\n   * Get available digital codes for a package\n   */ static getAvailableCodes(productId, packageId) {\n        const product = this.getById(productId);\n        if (!product) return [];\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return [];\n        return pkg.digitalCodes.filter((code)=>!code.used);\n    }\n    /**\n   * Assign digital code to order\n   */ static assignDigitalCode(productId, packageId, orderId) {\n        const product = this.getById(productId);\n        if (!product) return null;\n        const pkg = product.packages.find((p)=>p.id === packageId);\n        if (!pkg || !pkg.digitalCodes) return null;\n        const availableCode = pkg.digitalCodes.find((code)=>!code.used);\n        if (!availableCode) return null;\n        // Mark code as used and assign to order\n        availableCode.used = true;\n        availableCode.assignedToOrderId = orderId;\n        availableCode.usedAt = new Date();\n        // Update product in storage\n        this.update(productId, product);\n        return availableCode;\n    }\n    /**\n   * Initialize with sample data if empty\n   */ static initializeSampleData() {\n        const existingProducts = this.getAll();\n        if (existingProducts.length > 0) return;\n        console.log('Initializing sample products...');\n    // Sample products will be added here\n    // This will be implemented in the next step\n    }\n}\n// =====================================================\n// ORDER STORAGE OPERATIONS\n// =====================================================\nclass OrderStorage {\n    /**\n   * Get all orders\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.ORDERS, []);\n    }\n    /**\n   * Get order by ID\n   */ static getById(id) {\n        const orders = this.getAll();\n        return orders.find((o)=>o.id === id) || null;\n    }\n    /**\n   * Get orders by user (using email as identifier)\n   */ static getByUser(userEmail) {\n        return this.getAll().filter((o)=>o.userDetails.email === userEmail);\n    }\n    /**\n   * Get orders by status\n   */ static getByStatus(status) {\n        return this.getAll().filter((o)=>o.status === status);\n    }\n    /**\n   * Create new order\n   */ static create(orderData) {\n        const orders = this.getAll();\n        const now = new Date();\n        const newOrder = {\n            ...orderData,\n            id: generateId('order_'),\n            createdAt: now,\n            updatedAt: now\n        };\n        orders.push(newOrder);\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'create',\n                order: newOrder\n            }\n        }));\n        return newOrder;\n    }\n    /**\n   * Update order status\n   */ static updateStatus(id, status, notes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === id);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            status,\n            updatedAt: new Date(),\n            ...notes && {\n                notes\n            }\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        // Trigger storage event\n        window.dispatchEvent(new CustomEvent('ordersUpdated', {\n            detail: {\n                action: 'update',\n                order: updatedOrder\n            }\n        }));\n        return updatedOrder;\n    }\n    /**\n   * Add digital codes to order\n   */ static addDigitalCodes(orderId, codes) {\n        const orders = this.getAll();\n        const index = orders.findIndex((o)=>o.id === orderId);\n        if (index === -1) return null;\n        const updatedOrder = {\n            ...orders[index],\n            digitalCodes: [\n                ...orders[index].digitalCodes || [],\n                ...codes\n            ],\n            updatedAt: new Date()\n        };\n        orders[index] = updatedOrder;\n        SafeStorage.set(STORAGE_KEYS.ORDERS, orders);\n        return updatedOrder;\n    }\n}\n// =====================================================\n// USER STORAGE OPERATIONS\n// =====================================================\nclass UserStorage {\n    /**\n   * Get all users\n   */ static getAll() {\n        return SafeStorage.get(STORAGE_KEYS.USERS, []);\n    }\n    /**\n   * Get user by email\n   */ static getByEmail(email) {\n        const users = this.getAll();\n        return users.find((u)=>u.email === email) || null;\n    }\n    /**\n   * Create or update user\n   */ static upsert(userData) {\n        const users = this.getAll();\n        const existingIndex = users.findIndex((u)=>u.email === userData.email);\n        const now = new Date();\n        if (existingIndex >= 0) {\n            // Update existing user\n            const updatedUser = {\n                ...users[existingIndex],\n                ...userData,\n                updatedAt: now\n            };\n            users[existingIndex] = updatedUser;\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return updatedUser;\n        } else {\n            // Create new user\n            const newUser = {\n                ...userData,\n                id: generateId('user_'),\n                createdAt: now,\n                updatedAt: now\n            };\n            users.push(newUser);\n            SafeStorage.set(STORAGE_KEYS.USERS, users);\n            return newUser;\n        }\n    }\n}\n// =====================================================\n// SETTINGS STORAGE\n// =====================================================\nclass SettingsStorage {\n    /**\n   * Get application settings\n   */ static get() {\n        return SafeStorage.get(STORAGE_KEYS.SETTINGS, {});\n    }\n    /**\n   * Update settings\n   */ static update(settings) {\n        const currentSettings = this.get();\n        const updatedSettings = {\n            ...currentSettings,\n            ...settings\n        };\n        SafeStorage.set(STORAGE_KEYS.SETTINGS, updatedSettings);\n    }\n    /**\n   * Get specific setting\n   */ static getSetting(key, defaultValue) {\n        const settings = this.get();\n        return settings[key] !== undefined ? settings[key] : defaultValue;\n    }\n    /**\n   * Set specific setting\n   */ static setSetting(key, value) {\n        this.update({\n            [key]: value\n        });\n    }\n}\n// =====================================================\n// DATABASE INITIALIZATION\n// =====================================================\n/**\n * Initialize the entire localStorage database\n */ function initializeDatabase() {\n    console.log('🔄 Initializing localStorage database...');\n    // Initialize sample data if needed\n    ProductStorage.initializeSampleData();\n    console.log('✅ Database initialized successfully');\n}\n/**\n * Clear all data (for development/testing)\n */ function clearDatabase() {\n    console.log('🗑️ Clearing all localStorage data...');\n    Object.values(STORAGE_KEYS).forEach((key)=>{\n        SafeStorage.remove(key);\n    });\n    console.log('✅ Database cleared successfully');\n}\n/**\n * Export database (for backup/migration)\n */ function exportDatabase() {\n    const data = {};\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        data[name] = SafeStorage.get(key, null);\n    });\n    return data;\n}\n/**\n * Import database (for backup/migration)\n */ function importDatabase(data) {\n    Object.entries(STORAGE_KEYS).forEach((param)=>{\n        let [name, key] = param;\n        if (data[name] !== undefined) {\n            SafeStorage.set(key, data[name]);\n        }\n    });\n    console.log('✅ Database imported successfully');\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/storage/localStorage.ts\n"));

/***/ })

});