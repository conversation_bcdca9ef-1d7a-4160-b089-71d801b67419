# PUBG STORE - Premium Gaming Marketplace

<div align="center">
  
![PUBG STORE](./public/og-image.png)

[![React](https://img.shields.io/badge/React-18.3-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.5-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-5.4-brightgreen.svg)](https://vitejs.dev/)
[![Firebase](https://img.shields.io/badge/Firebase-10.14-orange.svg)](https://firebase.google.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind-3.4-38B2AC.svg)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](#license)

[![Website](https://img.shields.io/badge/Website-pubg--sd.netlify.app-brightgreen.svg)](https://pubg-sd.netlify.app)

</div>

<p align="center">A premium e-commerce platform for PUBG Mobile gamers, offering accounts, UC packages, mods, and gaming content with AI-powered features.</p>

## 📋 Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Project Structure](#project-structure)
- [Technologies](#technologies)
- [SEO Optimization](#seo-optimization)
- [AI Integration](#ai-integration)
- [Security Measures](#security-measures)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Deployment](#deployment)
- [Performance Optimization](#performance-optimization)
- [Contributing](#contributing)
- [License](#license)

## 🔍 Overview

PUBG STORE serves as a comprehensive marketplace for PUBG Mobile players, providing a secure and feature-rich platform to buy, sell, and discover gaming resources. The platform is built with modern web technologies and incorporates AI-powered features to enhance user experience.

## ✨ Features

### Core Functionality

- **PUBG Accounts Marketplace**
  - Browse and purchase verified gaming accounts
  - Filter by level, skins, and special items
  - Secure transaction processing

- **UC Store**
  - Various UC (in-game currency) package options
  - Competitive pricing with volume discounts
  - Instant delivery system

- **Mods & Hacks Section**
  - Curated gaming mods and enhancements
  - Compatibility verification
  - Installation guides

- **Blog & Community**
  - Latest PUBG news and updates
  - Strategy guides and tips
  - Community discussions

### User Experience

- **Responsive Design** - Optimized for all devices (mobile, tablet, desktop)
- **Dark/Light Mode** - Customizable interface theme
- **Localization** - Multi-language support
- **Real-time Updates** - Live notifications for orders and stock
- **Optimized Images** - Lazy loading and progressive image rendering

### Admin Features

- **Dashboard** - Comprehensive admin control panel
- **Analytics** - Sales reports and user engagement metrics
- **Content Management** - Easy product and blog post creation
- **Order Processing** - Streamlined workflow for fulfillment

## 🏗️ Project Structure

```
pubg-store/
├── public/                  # Static assets and images
│   ├── images/              # Site images
│   ├── robots.txt           # Search engine crawling rules
│   ├── sitemap.xml          # XML sitemap for search engines
│   └── og-image.png         # Open Graph image for sharing
├── src/                     # Source code
│   ├── components/          # Reusable UI components
│   │   ├── ui/              # Base UI components
│   │   ├── forms/           # Form components
│   │   ├── SEO.tsx          # SEO component for meta tags
│   │   ├── JsonLd.tsx       # Structured data components 
│   │   ├── OptimizedImage.tsx # Image optimization component
│   │   └── layouts/         # Layout components
│   ├── contexts/            # React context providers
│   │   ├── AuthContext.tsx  # Authentication context
│   │   ├── CartContext.tsx  # Shopping cart context
│   │   └── ConfigContext.tsx # Site configuration
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility functions and shared code
│   │   └── metadata.ts      # Global SEO metadata
│   ├── pages/               # Page components
│   │   ├── Index.tsx        # Homepage
│   │   ├── Accounts.tsx     # Accounts marketplace
│   │   ├── UCStore.tsx      # UC packages store
│   │   ├── Mods.tsx         # Mods and hacks
│   │   ├── Blog.tsx         # Blog listing
│   │   ├── BlogPost.tsx     # Individual blog posts
│   │   ├── About.tsx        # About page
│   │   ├── Contact.tsx      # Contact form
│   │   ├── Cart.tsx         # Shopping cart
│   │   ├── Login.tsx        # Authentication
│   │   ├── Profile.tsx      # User profile
│   │   ├── Admin.tsx        # Admin dashboard
│   │   └── NotFound.tsx     # 404 page
│   ├── services/            # API and external services integration
│   │   ├── firebase.ts      # Firebase setup and utilities
│   │   ├── firestore.ts     # Firestore database operations
│   │   ├── storage.ts       # Firebase storage operations
│   │   └── gemini.ts        # AI models integration
│   └── main.tsx             # Application entry point
├── scripts/                 # Build and utility scripts
│   └── generate-sitemap.cjs # Sitemap generator
├── firebase.json            # Firebase configuration
├── firestore.rules          # Firestore security rules
├── firestore.indexes.json   # Firestore indexes
├── vite.config.ts           # Vite configuration
├── tailwind.config.ts       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
├── .env.example             # Example environment variables
└── package.json             # Dependencies and scripts
```

## 🚀 Technologies

### Frontend

<table>
  <tr>
    <td><strong>Core</strong></td>
    <td>
      <ul>
        <li><strong>React 18</strong> - Component-based UI library</li>
        <li><strong>TypeScript</strong> - Static typing for robust development</li>
        <li><strong>Vite</strong> - Next-generation frontend tooling</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>Styling</strong></td>
    <td>
      <ul>
        <li><strong>Tailwind CSS</strong> - Utility-first CSS framework</li>
        <li><strong>Shadcn UI</strong> - High-quality UI components built on Radix UI</li>
        <li><strong>Framer Motion</strong> - Animation library</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>State & Routing</strong></td>
    <td>
      <ul>
        <li><strong>React Router</strong> - Declarative routing</li>
        <li><strong>React Query</strong> - Data fetching and cache management</li>
        <li><strong>Context API</strong> - State management</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>SEO & Optimization</strong></td>
    <td>
      <ul>
        <li><strong>React Helmet Async</strong> - Document head manager</li>
        <li><strong>JSON-LD</strong> - Structured data implementation</li>
        <li><strong>Lazy Loading</strong> - Performance optimization</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>Forms & Validation</strong></td>
    <td>
      <ul>
        <li><strong>React Hook Form</strong> - Performant form management</li>
        <li><strong>Zod</strong> - TypeScript-first schema validation</li>
      </ul>
    </td>
  </tr>
</table>

### Backend & Services

<table>
  <tr>
    <td><strong>Firebase</strong></td>
    <td>
      <ul>
        <li><strong>Firestore</strong> - NoSQL cloud database</li>
        <li><strong>Authentication</strong> - User management and security</li>
        <li><strong>Storage</strong> - File storage for images and assets</li>
        <li><strong>Hosting</strong> - Website deployment and delivery</li>
      </ul>
    </td>
  </tr>
  <tr>
    <td><strong>External APIs</strong></td>
    <td>
      <ul>
        <li><strong>ImgBB API</strong> - Image hosting and CDN delivery</li>
        <li><strong>OpenRouter API</strong> - Gateway to multiple AI models</li>
        <li><strong>Payment Gateways</strong> - Secure transaction processing</li>
      </ul>
    </td>
  </tr>
</table>

## 🔍 SEO Optimization

The platform implements comprehensive SEO strategies to enhance visibility and organic traffic:

### Meta Tag Management

- **Dynamic SEO Component** - Custom React component for managing title, description, and meta tags
- **Open Graph Support** - Rich social sharing capabilities with customizable images and content types
- **Twitter Card Integration** - Enhanced appearance when shared on Twitter
- **Canonical URLs** - Proper handling to prevent duplicate content issues

### Structured Data

- **JSON-LD Implementation** - Schema.org markup for better search result presentation
- **Rich Results Support** - Enhanced search appearance with:
  - Organization data
  - Product information (prices, availability, ratings)
  - Article metadata (authors, publish dates)
  - Local business details

### Technical SEO

- **Sitemap Generation** - Automated sitemap.xml creation during build process
- **Robots.txt** - Custom configuration for search engine crawlers
- **Semantic HTML** - Proper document structure for improved indexing
- **Arabic Language Support** - RTL and proper language attributes

### Image Optimization

- **Lazy Loading** - Deferred loading for off-screen images
- **Responsive Images** - Optimal sizing for different viewports
- **Alt Text** - Descriptive alternative text for accessibility and SEO
- **Progressive Loading** - Placeholder display while images load

## 🧠 AI Integration

The platform leverages advanced AI capabilities through OpenRouter API integration:

### Available Models

| Model | Use Case | Performance |
|-------|----------|-------------|
| Claude 3 Opus | High-quality content generation | Highest quality, slower |
| Gemini 2.0 Pro | General purpose text and data processing | Balanced |
| Gemini Flash | Fast responses for simpler queries | Quick response time |
| DeepSeek R1 Zero | Code and technical content | Specialized for technical content |
| Qwen models | Alternative processing capabilities | Good for some language tasks |
| Mistral 7B | Lightweight, fast responses | Fastest, more limited capabilities |

### AI Features Implementation

- **Smart Content Analysis**
  - Automated validation of account details
  - Price recommendation based on account value
  - Content moderation for user-submitted descriptions

- **Data Processing Pipeline**
  ```
  User Input → Text Preprocessing → AI Model Selection → 
  Model Processing → JSON Parsing → Validation → Database Storage
  ```

- **User-Configurable AI Settings**
  - Model selection for different operations
  - Temperature adjustment for creativity vs precision
  - Saved preferences per user account

## 🔐 Security Measures

### Authentication & Authorization

- JWT-based authentication flow
- Role-based access control (User/Admin)
- Session management with secure token storage
- Account recovery and verification procedures

### Data Protection

- All sensitive data encrypted at rest and in transit
- Firestore security rules with field-level security
- Input sanitization to prevent injection attacks
- Content Security Policy (CSP) implementation

### API Security

- Environment variables for credential management
- Rate limiting to prevent abuse
- CORS configuration for authorized origins
- API key rotation procedures

## 🚀 Performance Optimization

### Build Optimization

- **Vite Configuration** - Optimized for production builds with code splitting
- **Chunk Management** - Manual chunk configuration for vendor and UI libraries
- **Tree Shaking** - Elimination of unused code
- **Minification** - Terser configuration for reduced bundle size
- **Asset Compression** - Optimized loading of static assets

### Runtime Performance

- **Lazy Component Loading** - Components loaded only when needed
- **Memoization** - Cached calculations to avoid redundant processing
- **OptimizedImage Component** - Custom component for efficient image loading and display
- **Efficient Rerenders** - Strategic use of React.memo and dependency arrays

### Core Web Vitals

- **LCP (Largest Contentful Paint)** - Optimized with image preloading and lazy loading
- **FID (First Input Delay)** - Minimized with code splitting and efficient event handling
- **CLS (Cumulative Layout Shift)** - Reduced with proper image sizing and placeholder strategies

## 🚀 Getting Started

### Prerequisites

- Node.js (v18.0.0 or higher)
- npm (v9.0.0 or higher) or yarn
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/pubg-account-hub.git
   cd pubg-account-hub
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configuration
   ```

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   
   The application will be available at [http://localhost:5173](http://localhost:5173)

## ⚙️ Development Workflow

### Available Scripts

```bash
# Development
npm run dev         # Start development server

# Building
npm run build       # Production build
npm run build:dev   # Development build

# Quality Assurance
npm run lint        # Run ESLint

# Testing & Preview
npm run preview     # Preview production build
```

### Code Standards

- Follow TypeScript best practices and maintain type safety
- Use functional components with hooks
- Implement proper error handling and loading states
- Document complex functions and components
- Follow the established project structure

## 📦 Deployment

### Firebase Deployment

```bash
# Install Firebase CLI globally
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize your project (first time only)
firebase init

# Deploy to Firebase
firebase deploy
```

### Environment-specific Configurations

- Development: `.env.development`
- Production: `.env.production`
- Testing: `.env.test`

### Netlify Deployment

The project is configured for seamless deployment on Netlify:

- **Continuous Deployment**: Automatically deploy when changes are pushed to the repository
- **SPA Routing**: Configured with proper redirects for client-side routing
- **Environment Variables**: Securely manage API keys and configuration
- **CDN Distribution**: Fast content delivery through Netlify's global CDN
- **Custom Domain**: Easy setup with custom domains and free SSL

For detailed deployment instructions, see [NETLIFY-DEPLOY.md](./NETLIFY-DEPLOY.md).

[![Deploy to Netlify](https://www.netlify.com/img/deploy/button.svg)](https://app.netlify.com/start/deploy?repository=https://github.com/yourusername/pubg-account-hub)

## ⚡ Performance Optimization

- Code splitting for reduced bundle size
- Image optimization with next-gen formats
- Lazy loading of non-critical components
- Memoization of expensive calculations
- Service worker for offline capabilities
- CDN delivery of static assets

## 🤝 Contributing

While this is a proprietary project, internal contributions follow these guidelines:

1. Create feature branches from `develop`
2. Follow the established code style
3. Update documentation as necessary
4. Create PRs with detailed descriptions
5. Ensure all tests pass before merging

## 📄 License

Proprietary - All rights reserved.

This software and its contents are protected under copyright law. Unauthorized reproduction, distribution, or modification is prohibited.

## 🚀 Deployment

The application is deployed and accessible at:

- **Production Site**: [https://pubg-sd.netlify.app](https://pubg-sd.netlify.app)
- **GitHub Repository**: [https://github.com/altyb/pubg-account-hub](https://github.com/altyb/pubg-account-hub)
